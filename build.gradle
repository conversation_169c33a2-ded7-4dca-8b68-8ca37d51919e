buildscript {
    ext {
        springBootVersion = '3.2.9'
    }
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

group = 'com.cdz360.iot.gw'
version = '0.0.1-SNAPSHOT'


apply plugin: 'idea'
apply plugin: 'eclipse'

ext {
    dcCloudVersion = "20250707_1640_DEV-SNAPSHOT"
    springBootVersion = '3.2.9'
    lombokVersion = '1.18.34'
    commonsTextVersion = '1.8'
    commonsCodecVersion = '1.13'
    aliAmqpVersion = '1.0.3'
    commonsPoolVersion = '2.11.1' //apache commons pool. redis pool 需要 https://commons.apache.org/proper/commons-pool/
    mybatisSpringVersion = '3.0.3'  // org.mybatis.spring.boot:mybatis-spring-boot-starter
    junitVersion = '5.9.1'
}

allprojects {
    group = 'com.topower.iot'
    version = '0.0.1-SNAPSHOT'
    springBootVersion = '3.2.9'
    apply plugin: 'java'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17


    compileJava {
        options.encoding = "UTF-8"
    }
    processResources {
        duplicatesStrategy = DuplicatesStrategy.INCLUDE
    }

    test.onlyIf { !Boolean.getBoolean('skipTests') }

    repositories {
        mavenLocal()
        maven {
            url 'https://repo.iot.renwochong.com/repository/maven-snapshots/'
            mavenContent {
                snapshotsOnly()
            }
        }

        maven { url 'https://maven.aliyun.com/repository/central' }
        //maven { url 'https://maven.aliyun.com/repository/jcenter' }
        mavenCentral()
    }


}


subprojects {


    dependencies {
        // 使用桩枪redis相关缓存对象
        implementation("com.cdz360.cloud:dc-base-model:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-base-utils:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-data-cache:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-data-cache-reader:${dcCloudVersion}")
        implementation("com.cdz360.cloud:dc-data-cache-writer:${dcCloudVersion}")

        implementation('org.springframework.boot:spring-boot-starter')
        implementation 'org.springframework.boot:spring-boot-starter-aop'
        implementation 'org.springframework.boot:spring-boot-autoconfigure'
        implementation 'org.springframework.amqp:spring-rabbit'
        implementation('org.springframework.boot:spring-boot-starter-data-redis')
//        implementation('io.netty:netty-all')

        implementation('com.fasterxml.jackson.core:jackson-core')
        implementation('com.fasterxml.jackson.core:jackson-annotations')
        implementation('com.fasterxml.jackson.core:jackson-databind')
        implementation('com.fasterxml.jackson.dataformat:jackson-dataformat-yaml')

//		implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2'
        implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisSpringVersion}")
        implementation 'org.xerial:sqlite-jdbc:3.28.0'
        implementation 'org.springframework.boot:spring-boot-starter-logging'
        implementation group: 'org.apache.commons', name: 'commons-collections4', version: '4.3'
        implementation("org.apache.commons:commons-text:${commonsTextVersion}")
        implementation("commons-codec:commons-codec:${commonsCodecVersion}")
        implementation("org.apache.commons:commons-pool2:${commonsPoolVersion}")

        implementation("com.alibaba.mq-amqp:mq-amqp-client:${aliAmqpVersion}")

        compileOnly group: 'com.google.code.findbugs', name: 'jsr305', version: '3.0.1'

        compileOnly("org.projectlombok:lombok:${lombokVersion}")
        annotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testAnnotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testCompileOnly("org.projectlombok:lombok:${lombokVersion}")

        testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")
        testCompileOnly('org.springframework.boot:spring-boot-starter-test')

        implementation("org.springframework.boot:spring-boot-starter-webflux")
    }
}


dependencies {}
