package com.cdz360.iot.gw;

import com.cdz360.iot.gw.south.ws.WsJsonServer;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 边界网关启动的 main 函数
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableAspectJAutoProxy
@ComponentScan(basePackages = {"com.cdz360.iot", "com.cdz360.data"})
public class IotGwMain implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(IotGwMain.class);

    public static void main(String[] args) throws Exception {
        logger.info("*******************************************************");
        logger.info("*****                   GW-OCPP                  *****");
        logger.info("*******************************************************");

        logger.warn("开始启动-OCPP!!!!!!!!!!!!!!!!!!!!");
        ConfigurableApplicationContext context = new SpringApplicationBuilder(IotGwMain.class)
            .web(WebApplicationType.NONE)
            .run(args);
        WsJsonServer jsonServer = context.getBean(WsJsonServer.class);
        jsonServer.startServer();
    }

    @PreDestroy
    public void stop() {
        logger.warn("网关已停止-OCPP!!!!!!!!!!!!!!!!!!!!");
    }


    @Override
    public void run(String... args) throws Exception {

    }
}