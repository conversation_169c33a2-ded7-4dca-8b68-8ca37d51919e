package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import java.util.Optional;

public interface CacheManagerService {

    /**
     * 仅从本地缓存获取
     *
     * @param evseNo 桩编号
     * @return
     */
    Optional<GwEvseVo> getEvseOfLocalCache(String evseNo);

    /**
     * 从非本地数据源获取
     *
     * @param evseNo 桩编号
     * @return
     */
    Optional<GwEvseVo> getEvseOfOtherCache(String evseNo);

    /**
     * 先从本地获取，如果不存在则从非本地数据源获取: 可自定获取规则
     *
     * @param evseNo 桩编号
     * @return
     */
    Optional<GwEvseVo> getEvseCache(String evseNo);

    /**
     * 仅从本地缓存获取
     *
     * @param evseNo 桩编号
     * @param plugId 枪口编号
     * @return
     */
    Optional<GwPlugVo> getPlugOfLocalCache(String evseNo, int plugId);

    /**
     * 从非本地数据源获取
     *
     * @param evseNo 桩编号
     * @param plugId 枪口编号
     * @return
     */
    Optional<GwPlugVo> getPlugOfOtherCache(String evseNo, int plugId);

    /**
     * 先从本地获取，如果不存在则从非本地数据源获取: 可自定获取规则
     *
     * @param evseNo 桩编号
     * @param plugId 枪口编号
     * @return
     */
    Optional<GwPlugVo> getPlugCache(String evseNo, int plugId);

    /**
     * 将数据同步到本地的同时也同步到非本地数据源
     *
     * @param evseVo
     */
    void updateAndSync2OtherCache(GwEvseVo evseVo);

    /**
     * 仅仅更新本地缓存
     *
     * @param evseVo
     */
    void updateLocal(GwEvseVo evseVo);

    /**
     * 仅仅更新本地缓存
     *
     * @param ctx
     * @param plugVo
     */
     GwPlugVo updateLocal(EvseContext ctx, GwPlugVo plugVo);

    /**
     * 将数据同步到本地的同时也同步到非本地数据源
     *
     * @param ctx
     * @param plugVo
     */
    void updateAndSync2OtherCache(EvseContext ctx, GwPlugVo plugVo);


}
