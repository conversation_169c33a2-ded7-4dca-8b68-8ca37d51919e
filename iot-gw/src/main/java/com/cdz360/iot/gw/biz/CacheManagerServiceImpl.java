package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CacheManagerServiceImpl implements CacheManagerService {

    // 网关本地桩/枪缓存
    private static final Map<String, GwEvseVo> evseMap = new ConcurrentHashMap<>();
    @Autowired
    private RedisIotRwService redisIotRwService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    private static Pair<Boolean, GwEvseVo> compareEvseProperties(GwEvseVo result,
        GwEvseVo current) {
        if (null == result) {
            return Pair.of(true, current);
        }

        try {
            boolean changed = false;
            if (CollectionUtils.isNotEmpty(current.getPlugs()) &&
                (CollectionUtils.isEmpty(result.getPlugs()) ||
                    result.getPlugs().size() != current.getPlugs().size())) {
                changed = true;
                result.setPlugs(current.getPlugs() == null ? List.of() : current.getPlugs());
            }

            if (current.getBooting() != null &&
                (result.getBooting() == null || !result.getBooting()
                    .equals(current.getBooting()))) {
                changed = true;
                result.setBooting(current.getBooting());
            }

            if (current.getNet() != null &&
                (result.getNet() == null || !result.getNet().equals(current.getNet()))) {
                changed = true;
                result.setNet(current.getNet());
            }

            if (current.getSupportVin() != null &&
                (result.getSupportVin() == null || !result.getSupportVin()
                    .equals(current.getSupportVin()))) {
                changed = true;
                result.setSupportVin(current.getSupportVin());
            }

            if (StringUtils.isNotBlank(current.getError()) &&
                (StringUtils.isBlank(result.getError()) || !result.getError()
                    .equals(current.getError()))) {
                changed = true;
                result.setError(current.getError());
            }

            if (current.getStatus() != null && (
                result.getStatus() == null || !result.getStatus().equals(current.getStatus()))) {
                changed = true;
                result.setStatus(current.getStatus());
            }

            if (current.getPlugNum() != null &&
                (result.getPlugNum() == null || !result.getPlugNum()
                    .equals(current.getPlugNum()))) {
                changed = true;
                result.setPlugNum(current.getPlugNum());
            }

            if (current.getSupplyType() != null &&
                (result.getSupplyType() == null || !result.getSupplyType()
                    .equals(current.getSupplyType()))) {
                changed = true;
                result.setSupplyType(current.getSupplyType());
            }

            if (current.getPower() != null &&
                (result.getPower() == null || !result.getPower().equals(current.getPower()))) {
                changed = true;
                result.setPower(current.getPower());
            }

            if (current.getVoltage() != null &&
                (result.getVoltage() == null || !result.getVoltage()
                    .equals(current.getVoltage()))) {
                changed = true;
                result.setVoltage(current.getVoltage());
            }

            if (current.getCurrent() != null &&
                (result.getCurrent() == null || !result.getCurrent()
                    .equals(current.getCurrent()))) {
                changed = true;
                result.setCurrent(current.getCurrent());
            }

            if (current.getProtocolVer() != null &&
                (result.getProtocolVer() == null || !result.getProtocolVer()
                    .equals(current.getProtocolVer()))) {
                changed = true;
                result.setProtocolVer(current.getProtocolVer());
            }

            if (current.getProtocol() != null &&
                (result.getProtocol() == null || !result.getProtocol()
                    .equals(current.getProtocol()))) {
                changed = true;
                result.setProtocol(current.getProtocol());
            }

            if (StringUtils.isNotBlank(current.getFirmwareVer()) &&
                (StringUtils.isBlank(result.getFirmwareVer()) || !result.getFirmwareVer()
                    .equals(current.getFirmwareVer()))) {
                changed = true;
                result.setFirmwareVer(current.getFirmwareVer());
            }

            if (StringUtils.isNotBlank(current.getPc01Ver()) &&
                (StringUtils.isBlank(result.getPc01Ver()) || !result.getPc01Ver()
                    .equals(current.getPc01Ver()))) {
                changed = true;
                result.setPc01Ver(current.getPc01Ver());
            }

            if (StringUtils.isNotBlank(current.getPc02Ver()) &&
                (StringUtils.isBlank(result.getPc02Ver()) || !result.getPc02Ver()
                    .equals(current.getPc02Ver()))) {
                changed = true;
                result.setPc02Ver(current.getPc02Ver());
            }

            if (StringUtils.isNotBlank(current.getPc03Ver()) &&
                (StringUtils.isBlank(result.getPc03Ver()) || !result.getPc03Ver()
                    .equals(current.getPc03Ver()))) {
                changed = true;
                result.setPc03Ver(current.getPc03Ver());
            }

            if (current.getTemperature() != null &&
                (result.getTemperature() == null || !result.getTemperature()
                    .equals(current.getTemperature()))) {
                changed = true;
                result.setTemperature(current.getTemperature());
            }

            if (current.getErrorCode() != null &&
                (result.getErrorCode() == null || !result.getErrorCode()
                    .equals(current.getErrorCode()))) {
                changed = true;
                result.setErrorCode(current.getErrorCode());
            }

            if (current.getAlertCode() != null &&
                (result.getAlertCode() == null || !result.getAlertCode()
                    .equals(current.getAlertCode()))) {
                changed = true;
                result.setAlertCode(current.getAlertCode());
            }

            if (changed) {
                log.info("桩信息更新: {}", JsonUtils.toJsonString(result));
                result.setUpdateTime(new Date());
            }
            return Pair.of(changed, result);
        } catch (Exception e) {
            log.info("内容比较异常: err = {}", e.getMessage(), e);
            return Pair.of(false, result);
        }
    }

    // 属性列表本地缓存
//    private static final Map<Class<Object>, PropertyDescriptor[]> propertyMap = new HashMap<>();

    private static Pair<Boolean, GwPlugVo> comparePlugProperties(GwPlugVo result,
        GwPlugVo current) {
        if (null == result) {
            return Pair.of(true, current);
        }

        try {
            boolean changed = false;

            if (current.getMsgStatus() != null &&
                (result.getMsgStatus() == null || !result.getMsgStatus()
                    .equals(current.getMsgStatus()))) {
                changed = true;
                result.setMsgStatus(current.getMsgStatus());
            }

            if (current.getLastHbSeq() != null &&
                (result.getLastHbSeq() == null || !result.getLastHbSeq()
                    .equals(current.getLastHbSeq()))) {
                changed = true;
                result.setLastHbSeq(current.getLastHbSeq());
            }

            if (current.getStatus() != null && (
                result.getStatus() == null || !result.getStatus().equals(current.getStatus()))) {
                changed = true;
                result.setStatus(current.getStatus());
            }

            if (current.getSupply() != null &&
                (result.getSupply() == null || !result.getSupply().equals(current.getSupply()))) {
                changed = true;
                result.setSupply(current.getSupply());
            }

            if (current.getTemperature() != null &&
                (result.getTemperature() == null || !result.getTemperature()
                    .equals(current.getTemperature()))) {
                changed = true;
                result.setTemperature(current.getTemperature());
            }

            if (StringUtils.isNotBlank(current.getErrorMsg()) &&
                (StringUtils.isBlank(result.getErrorMsg()) || !result.getErrorMsg()
                    .equals(current.getErrorMsg()))) {
                changed = true;
                result.setErrorMsg(current.getErrorMsg());
            }

            if (current.getErrorCode() != null &&
                (result.getErrorCode() == null || !result.getErrorCode()
                    .equals(current.getErrorCode()))) {
                changed = true;
                result.setErrorCode(current.getErrorCode());
            }

            if (current.getAlertCode() != null &&
                (result.getAlertCode() == null || !result.getAlertCode()
                    .equals(current.getAlertCode()))) {
                changed = true;
                result.setAlertCode(current.getAlertCode());
            }

            if (current.getMaxVoltage() != null &&
                (result.getMaxVoltage() == null || !result.getMaxVoltage()
                    .equals(current.getMaxVoltage()))) {
                changed = true;
                result.setMaxVoltage(current.getMaxVoltage());
            }

            if (current.getMinVoltage() != null &&
                (result.getMinVoltage() == null || !result.getMinVoltage()
                    .equals(current.getMinVoltage()))) {
                changed = true;
                result.setMinVoltage(current.getMinVoltage());
            }

            if (current.getMaxCurrent() != null &&
                (result.getMaxCurrent() == null || !result.getMaxCurrent()
                    .equals(current.getMaxCurrent()))) {
                changed = true;
                result.setMaxCurrent(current.getMaxCurrent());
            }

            if (current.getMinCurrent() != null &&
                (result.getMinCurrent() == null || !result.getMinCurrent()
                    .equals(current.getMinCurrent()))) {
                changed = true;
                result.setMinCurrent(current.getMinCurrent());
            }

            if (changed) {
                // 缓存里的plugNo存在丢失的情况,这里做一个数据完整性补充
                if (StringUtils.isBlank(result.getPlugNo())
                    && StringUtils.isNotBlank(current.getPlugNo())) {
                    result.setPlugNo(current.getPlugNo());
                }
                log.info("枪头信息更新: plugNo = {}, status = {}", result.getPlugNo(),
                    result.getStatus());
                result.setUpdateTime(new Date());
            }

            return Pair.of(changed, result);
        } catch (Exception e) {
            log.info("内容比较异常: err = {}", e.getMessage(), e);
            return Pair.of(false, result);
        }
    }

    @Override
    public Optional<GwEvseVo> getEvseOfLocalCache(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            log.error("桩编号不能为空");
            return Optional.empty();
        }

        return Optional.ofNullable(evseMap.get(evseNo));
    }

    @Override
    public Optional<GwEvseVo> getEvseOfOtherCache(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            log.error("桩编号不能为空");
            return Optional.empty();
        }

        return Optional.ofNullable(
            GwEvseVo.toGwEvseVo(redisIotRwService.getEvseRedisCache(evseNo)));
    }

    @Override
    public Optional<GwEvseVo> getEvseCache(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            log.error("桩编号不能为空");
            return Optional.empty();
        }

        // 从本地缓存获取
        GwEvseVo vo = evseMap.get(evseNo);
        if (null == vo) {
            log.debug("本地缓存匹配失败, 查询redis. evseNo = {}", evseNo);
            // 本地缓存获取不到则从redis中获取
            EvseVo evseVo = redisIotRwService.getEvseRedisCache(evseNo);
            if (null != evseVo) {
                vo = GwEvseVo.toGwEvseVo(evseVo);
                evseMap.put(evseNo, vo);
            }
        }

        return Optional.ofNullable(vo);
    }

    @Override
    public Optional<GwPlugVo> getPlugOfLocalCache(String evseNo, int plugId) {
        if (StringUtils.isBlank(evseNo) || plugId < 1) {
            log.error("桩编号为空或枪号不合法: evseNo = {}, plugId = {}", evseNo, plugId);
            return Optional.empty();
        }

        GwEvseVo evseVo = evseMap.get(evseNo);
        if (null == evseVo) {
            return Optional.empty();
        }

        return evseVo.getPlugs().stream().filter(vo -> NumberUtils.equals(vo.getIdx(), plugId))
            .findFirst();
    }

    @Override
    public Optional<GwPlugVo> getPlugOfOtherCache(String evseNo, int plugId) {
        if (StringUtils.isBlank(evseNo) || plugId < 1) {
            log.error("桩编号为空或枪号不合法: evseNo = {}, plugId = {}", evseNo, plugId);
            return Optional.empty();
        }

        return Optional.ofNullable(
            GwPlugVo.toGwPlugVo(redisIotRwService.getPlugRedisCache(evseNo, plugId)));
    }

    @Override
    public Optional<GwPlugVo> getPlugCache(String evseNo, int plugId) {
        if (StringUtils.isBlank(evseNo) || plugId < 1) {
            log.error("桩编号为空或枪号不合法: evseNo = {}, plugId = {}", evseNo, plugId);
            return Optional.empty();
        }

        // 获取桩的缓存信息
        Optional<GwEvseVo> cacheEvse = this.getEvseCache(evseNo);
        if (cacheEvse.isEmpty()) {
            log.error("枪头对应的桩缓存不存在，丢弃: evseNo = {}, plugId = {}", evseNo, plugId);
            return Optional.empty();
        }
        log.info("cacheEvse: {}", JsonUtils.toJsonString(cacheEvse.get()));

        // 获取本地缓存
        Optional<GwPlugVo> localOp = this.getPlugOfLocalCache(evseNo, plugId);
        if (localOp.isPresent() && localOp.get().getIdx() != null) {
            return localOp;
        } else {
            // 从非本地数据源获取
            Optional<GwPlugVo> otherOp = this.getPlugOfOtherCache(evseNo, plugId);
            if (otherOp.isPresent()) {
                // 将非本地缓存同步到本地
                GwEvseVo evse = cacheEvse.get();
                evse.getPlugs().add(otherOp.get());
                evseMap.put(evseNo, evse);
                return otherOp;
            }

            log.error("枪头缓存不存在，丢弃: evseNo = {}, plugId = {}", evseNo, plugId);
            return Optional.empty();
        }
    }

    @Override
    public void updateAndSync2OtherCache(GwEvseVo evseVo) {
        if (StringUtils.isBlank(evseVo.getEvseNo())) {
            log.info("桩编号无效: evse = {}", JsonUtils.toJsonString(evseVo));
            return;
        }

        evseMap.put(evseVo.getEvseNo(), evseVo);
        redisIotRwService.updateEvseRedisCache(GwEvseVo.toEvseVo(evseVo));
        evseVo.getPlugs()
            .stream().map(vo -> GwPlugVo.toPlugVo(vo))
            .forEach(this.redisIotRwService::updatePlugRedisCache);
    }

    @Override
    public void updateLocal(GwEvseVo evseVo) {
        if (StringUtils.isBlank(evseVo.getEvseNo())) {
            log.info("桩编号无效: evse = {}", JsonUtils.toJsonString(evseVo));
            return;
        }
        evseMap.put(evseVo.getEvseNo(), evseVo);
    }

    @Override
    public GwPlugVo updateLocal(EvseContext ctx, final GwPlugVo plugVo) {
        // 本地缓存给变更
        GwPlugVo plugResult = plugVo;

        // 枪数据是否变更
        Optional<GwPlugVo> plugOp = ctx.getEvse().getPlugs().stream()
            .filter(vo -> vo.getIdx() == plugVo.getIdx()).findFirst();
//        log.info("plug = {}", JsonUtils.toJsonString(plugOp));
        if (plugOp.isPresent()) {
            Pair<Boolean, GwPlugVo> plugPair = comparePlugProperties(plugOp.get(), plugVo);
            if (plugPair.getFirst()) {
                // 枪信息有变更
                int idx = 0;
                Iterator<GwPlugVo> iter = ctx.getEvse().getPlugs().iterator();
                log.debug("打个标记，debug性能问题. plugs.size = {}",
                    ctx.getEvse().getPlugs().size()); // xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx alpha
                while (iter.hasNext()) {
                    if (iter.next().getIdx() == plugVo.getIdx()) {

                        plugResult = plugPair.getSecond();
                        ctx.getEvse().getPlugs().set(idx, plugResult);
                        log.info(
                            "[{} {}] 调整后的枪头信息: plugIdx = {}, plugNo = {}, plugStatus = {}",
                            ctx.getTraceId(), plugResult.getEvseNo(), plugResult.getIdx(),
                            plugResult.getPlugNo(),
                            plugResult.getStatus());
                        break;
                    }
                    idx++;
                }
            }
            log.info("[{} {}]  plugNo = {}, plugIdx = {}, plugStatus = {}",
                ctx.getTraceId(), ctx.getEvse().getEvseNo(), plugResult.getPlugNo(),
                plugResult.getIdx(), plugResult.getStatus());
        } else {
            log.info(
                "[{} {}] 枪头缓存匹配失败，增加缓存枪头. plugNo = {}, plugIdx = {}",
                ctx.getTraceId(), plugVo.getEvseNo(), plugVo.getPlugNo(), plugVo.getIdx());
            ctx.getEvse().getPlugs().add(plugVo);
        }
        return plugResult;
    }

    @Override
    public void updateAndSync2OtherCache(EvseContext ctx, GwPlugVo plugVo) {
        // 本地缓存给变更
        GwPlugVo result = this.updateLocal(ctx, plugVo);

        // 更新非本地数据源
        redisIotRwService.updateEvseRedisCache(GwEvseVo.toEvseVo(ctx.getEvse()));
        redisIotRwService.updatePlugRedisCache(GwPlugVo.toPlugVo(result));

//        List<PlugStatus> statusList = List.of(PlugStatus.IDLE, PlugStatus.CONNECT);
//        if (statusList.contains(result.getSecond().getStatus())) {
//            GwPlugVo plug = new GwPlugVo();
//            plug.setEvseNo(plugVo.getEvseNo()).setIdx(plugVo.getIdx())
//                    .setOrderNo("");
//            redisIotRwService.updatePlugRedisCache(plug);
//        }

        //return ctx.getEvse();
    }

    // 仅用作测试，别用
    public void addEvseVo(GwEvseVo vo) {
        evseMap.put(vo.getEvseNo(), vo);
    }

}
