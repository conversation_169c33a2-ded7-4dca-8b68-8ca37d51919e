package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.CardConfigCache;
import com.cdz360.iot.gw.biz.ds.ConfigCache;
import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.model.type.EvseCfgResultType;
import com.cdz360.iot.gw.north.model.ConfigModifyResult;
import com.cdz360.iot.gw.north.model.EvseCfgGetResponse;
import com.cdz360.iot.gw.north.model.EvseOperationDto;
import com.cdz360.iot.gw.north.model.GetConfig;
import com.cdz360.iot.gw.north.model.OcppConfigResult;
import com.cdz360.iot.gw.north.model.SubCfgResult;
import com.cdz360.iot.gw.north.mq.model.ConfigModifyRequest;
import com.cdz360.iot.gw.north.server.ConfigService;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Timer;
import java.util.TimerTask;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@Scope(value = "singleton")
public class CloudOperationsProcess {

    private final Logger logger = LoggerFactory.getLogger(CloudOperationsProcess.class);

    @Autowired
    private ConfigService configService;

    @Autowired
    private IdempotenceService idempotenceService;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private CacheManagerService cacheManagerService;

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private CardConfigCache cardConfigCache;

    @Autowired
    private CardProcess cardProcess;

    /*
    @Autowired
    private VinProcess vinProcess;
    */

    @Value("${cdz360.timer.cfg-report-timeout}")
    private long cfgReportTimeout;


    public void processCloudOperations(String traceId, ConfigModifyRequest request) {
        logger.info("OCPP云端操作。traceId: {}, message: {}.", traceId,
            JsonUtils.toJsonString(request));

        String cfgVer = request.getData().getCfgVer();//版本号
        String cacheVersion = idempotenceService.get(
            ConstantCollections.GW_CLOUD_OPERATIONS_VERSION);

        validateRequest(traceId, request, cfgVer, cacheVersion);

        idempotenceService.put(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION, cfgVer);

        try {
            request.getData().getEvseNos().forEach(
                evseId -> configCache.put(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION, evseId,
                    evseId));

            Map<String, SubCfgResult> failEvseMap = new HashMap<>();//更新失败的桩号

            // 去重
            List<String> evseIds = request.getData().getEvseNos().stream()
                .filter(StringUtils::isNotBlank).toList();

            int i = 1;
            for (String evseId : evseIds) {

                try {
                    GetConfig config = new GetConfig();
                    config.setCfgVer(cfgVer);
                    config.setEvseNo(evseId);

                    EvseCfgGetResponse response = configService.evseCfgGet(traceId, config);

                    if (ObjectUtils.isEmpty(response) || ObjectUtils.isEmpty(response.getData())
                        || response.getData().getOperationDto() == null) {
                        logger.info("[{}] 下发配置时未获取到云端最新配置。request: {}, response: {}",
                            traceId, request, response);
                        failEvseMap.put(evseId, null);
                        i++;
                        continue;
                    }
                    EvseOperationDto cfg = response.getData().getOperationDto();
                    logger.info("[{}] evseCfgGet.cfg = {}", traceId, cfg);
                    EvseMsgBase base = new EvseMsgBase();
                    base.setTid(traceId);
                    base.setEvseNo(evseId);
                    if (cfg.getPlugId() != null) {
                        base.setPlugIdx(cfg.getPlugId());
                    }

                    boolean success = true;
                    SubCfgResult subCfgResult = new SubCfgResult();

                    if (CollectionUtils.isNotEmpty(cfg.getGetConfigKeyList())) {
                        Optional<OcppConfigResult> configResOpt = jsonServer.sendGetConfigurationReq(
                            base, cfg.getGetConfigKeyList());

                        if (configResOpt.isPresent()) {
                            success = true;
                            subCfgResult.setGetConfigResult(DcConstants.KEY_RES_CODE_SUCCESS);
                            subCfgResult.setOcppConfigResult(configResOpt.get());
                            this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                subCfgResult);

                        } else {
                            logger.info("[{} {}] 获取桩配置失败 getConfigKeyList: {}", traceId,
                                evseId, cfg.getGetConfigKeyList());
                            success = false;
                            subCfgResult.setGetConfigResult(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                        }
                    } else if (CollectionUtils.isNotEmpty(cfg.getChangeConfigVoList())) {
                        List<EvseOperationDto.ConfigurationVo> changeConfigVoList = cfg.getChangeConfigVoList();

                        Optional<Boolean> configResOpt = jsonServer.sendChangeConfigurationReq(base,
                            changeConfigVoList);
                        success = configResOpt.isPresent() && configResOpt.get();

                        if (!success) {
                            subCfgResult.setChangeConfigResult(
                                DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                        } else {
                            subCfgResult.setChangeConfigResult(DcConstants.KEY_RES_CODE_SUCCESS);
                            this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                subCfgResult);
                        }
                    } else if (cfg.getGetDiagnosticsVo() != null) {

                        Optional<Pair<Boolean, String>> resultOpt = jsonServer.sendGetDiagnosticsRequest(
                            base, cfg.getGetDiagnosticsVo());
                        success = resultOpt.isPresent() && resultOpt.get().getLeft();

                        if (!success) {
                            subCfgResult.setGetDiagnosticsResult(
                                DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                        } else {
                            String fileName = resultOpt.map(Pair::getRight).orElse(null);
                            subCfgResult.setGetDiagnosticsResult(DcConstants.KEY_RES_CODE_SUCCESS);
                            subCfgResult.setGetDiagnosticsFileName(fileName);
                            this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                subCfgResult);
                        }
                    } else if (cfg.getAvailability() != null) {
                        Optional<Boolean> availabilityResOpt = jsonServer.sendChangeAvailabilityReq(
                            base, cfg.getPlugId(), cfg.getAvailability());
                        success = availabilityResOpt.isPresent() && availabilityResOpt.get();

                        if (!success) {
                            subCfgResult.setChangeAvailabilityResult(
                                DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                        } else {
                            subCfgResult.setChangeAvailabilityResult(
                                DcConstants.KEY_RES_CODE_SUCCESS);
                            this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                subCfgResult);
                        }

                    } else if (cfg.getClearCache() != null) {
                        if (BooleanUtils.isTrue(cfg.getClearCache())) {
                            success = jsonServer.sendClearCacheRequest(traceId, evseId);
                            if (!success) {
                                subCfgResult.setClearCacheResult(
                                    DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                            } else {
                                subCfgResult.setClearCacheResult(DcConstants.KEY_RES_CODE_SUCCESS);
                                this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                    subCfgResult);
                            }
                        } else {
                            logger.info("[{} {}] 无需清除缓存 clearCache: {}", traceId, evseId,
                                cfg.getClearCache());
                        }
                    } else if (cfg.getMessageTrigger() != null) {
                        success = jsonServer.sendTriggerMessageReq(base, null,
                            cfg.getMessageTrigger());

                        if (!success) {
                            subCfgResult.setTriggerMessageResult(
                                DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                        } else {
                            subCfgResult.setTriggerMessageResult(DcConstants.KEY_RES_CODE_SUCCESS);
                            this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                subCfgResult);
                        }

                    } else if (cfg.getUnlockConnector() != null) {
                        if (BooleanUtils.isTrue(cfg.getUnlockConnector())) {
                            success = jsonServer.sendUnlockConnectorRequest(base, cfg.getPlugId());
                            if (!success) {
                                subCfgResult.setUnlockConnectorResult(
                                    DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
                            } else {
                                subCfgResult.setUnlockConnectorResult(
                                    DcConstants.KEY_RES_CODE_SUCCESS);
                                this.reportByEvse(traceId, evseId, EvseCfgResultType.SUCCESS,
                                    subCfgResult);
                            }
                        } else {
                            logger.info("[{} {}] 无需解锁桩 unlockConnector: {}", traceId, evseId,
                                cfg.getUnlockConnector());
                        }
                    } else {
                        logger.error("[{} {}] 暂未支持的配置下发类型 ", traceId, evseId);
                        success = false;
                    }

                    if (!success) {
                        failEvseMap.put(evseId, subCfgResult);
                    }

                    i++;
                } catch (Exception ex) {
                    failEvseMap.put(evseId, new SubCfgResult());
                    logger.error("[{}] 下发配置/紧急卡到桩时发生错误 error: {}", traceId,
                        ex.getMessage(), ex);
                }
            }

            //对于没有拉到配置信息的桩或者桩已经离线的，回复云端更新结果(失败）
            if (!failEvseMap.isEmpty()) {
                logger.info("[{}] 部分桩下发配置失败 evseIds: {}", traceId, failEvseMap.keySet());

                failEvseMap.forEach((evseId, subCfgResult) -> {
                    reportOperationResult(traceId, cfgVer, evseId, EvseCfgResultType.FAIL,
                        subCfgResult);
                });
            }

        } catch (Exception e) {
            logger.error("[{}] 下发桩配置时发生错误 error: {}", traceId, e.getMessage(), e);
        }

        if (configCache.size() <= 0) {
            logger.info("[{}] 桩配置结果已经全部上报完成 ", traceId);
            idempotenceService.remove(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION);
            return;
        }

        Timer timer = new Timer("Timer-Config");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                logger.info("[{}] 超时上报桩配置结果 ", traceId);

                try {

                    for (String evseId : request.getData().getEvseNos()) {
                        if (configCache.containsKey(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION,
                            evseId)) {
                            reportOperationResult(traceId, request.getData().getCfgVer(), evseId,
                                EvseCfgResultType.TIMEOUT, null);
                        }
                    }

                } catch (Exception e) {
                    logger.error("[{}] 超时上报桩配置结果失败 error: {}", traceId, e.getMessage(),
                        e);
                }

                logger.info("[{}] 桩配置结果已经全部上报超时完成 ", traceId);
                idempotenceService.remove(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION);

                timer.cancel();
            }
        }, cfgReportTimeout);
    }

    private void validateRequest(String traceId, ConfigModifyRequest request, String cfgVer,
        String cacheVersion) {
        if (StringUtils.isEmpty(cfgVer)) {
            logger.info("[{}] 云端下发配置的版本号非法 ", traceId);
            throw new IllegalArgumentException("云端下发配置的版本号非法");
        }

        if (CollectionUtils.isEmpty(request.getData().getEvseNos())) {
            logger.info("[{}] 没有需要下发配置的桩 ", traceId);
            throw new IllegalArgumentException("没有需要下发配置的桩");
        }

//        if (!StringUtils.isEmpty(cacheVersion)) {
//            logger.info("网关正在处理桩配置更新操作，收到重复操作指令。traceId: {}", traceId);
//
//            reportFailedResult(traceId, request);
//
//            throw new IllegalArgumentException("收到重复操作指令");
//        }

        if (cfgVer.equals(cacheVersion)) {
            logger.info("[{}] 收到重复版本的桩配置下发指令。 cfgVersion: {}, request: {}", traceId,
                cfgVer, request);

            reportFailedResult(traceId, request);

            throw new IllegalArgumentException("收到重复版本的桩配置下发指令");
        }
    }

    private void reportFailedResult(String traceId, ConfigModifyRequest request) {
        logger.info("[{}] 上报配置结果为失败 ", traceId);
        if (request != null && !StringUtils.isEmpty(request.getData().getCfgVer())
            && !CollectionUtils.isEmpty(request.getData().getEvseNos())) {
            request.getData().getEvseNos().forEach(evseId -> {
                reportOperationResult(traceId, request.getData().getCfgVer(), evseId,
                    EvseCfgResultType.FAIL, null);
            });
        }
    }


    public void reportByEvse(String tid, String evseNo, EvseCfgResultType resultType,
        SubCfgResult subResult) {
        logger.info("[{} {}] 桩自行上报配置结果 resultType: {}, subResult: {}", tid, evseNo,
            resultType, subResult);

        String cfgVer = configCache.get(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION, evseNo);

        //cfgVer == null说明超时器已经上报过了
        if (cfgVer == null) {
            logger.info("[{} {}] 桩自行上报配置结果失败，定时器已经完成上报。", tid, evseNo);
            return;
        }

        reportOperationResult(tid, cfgVer, evseNo, resultType, subResult);

        //如果是最后一台桩上报(或者只有一台桩)，立即删除幂等性缓存，不必等待超时器来清除幂等性缓存，方便在下发完成后可以立即下发。
        if (configCache.size() <= 0) {
            logger.info("[{} {}] 桩配置结果已经全部上报完成。", tid, evseNo);
            idempotenceService.remove(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION);
        }
    }

    /**
     * 回复云端更新结果(成功/失败/超时）
     */
    private void reportOperationResult(String traceId, String version, String evseId,
        EvseCfgResultType resultType, SubCfgResult subResult) {

        try {
            logger.info("[{} {}] 上报桩操作结果 version: {}, resultType: {}, subResult: {}",
                traceId, evseId, version, resultType, subResult);

            ConfigModifyResult request = new ConfigModifyResult();
            request.setCfgVer(version);
            request.setEvseNo(evseId);
            // request.setMethod(RequestMethod.EVSE_CFG_RESULT);
            // request.setType(IotPackageType.REQ);
            // request.setSeq(SeqGeneratorUtil.newStringId());
            request.setResult(resultType.name());

            if (!ObjectUtils.isEmpty(subResult)) {
                request.setGetConfigResult(subResult.getGetConfigResult());
                request.setOcppConfigResult(subResult.getOcppConfigResult());
                request.setChangeConfigResult(subResult.getChangeConfigResult());
                request.setGetDiagnosticsResult(subResult.getGetDiagnosticsResult());
                request.setGetDiagnosticsFileName(subResult.getGetDiagnosticsFileName());
                request.setChangeAvailabilityResult(subResult.getChangeAvailabilityResult());
                request.setClearCacheResult(subResult.getClearCacheResult());
                request.setTriggerMessageResult(subResult.getTriggerMessageResult());
                request.setUnlockConnectorResult(subResult.getUnlockConnectorResult());
            }

            configService.reportModifyResult(traceId, request);
            configCache.remove(ConstantCollections.GW_CLOUD_OPERATIONS_VERSION, evseId);

        } catch (Exception e) {
            logger.error("[{} {}] 上报桩操作结果时发生错误。", traceId, evseId, e);
        }
    }


}
