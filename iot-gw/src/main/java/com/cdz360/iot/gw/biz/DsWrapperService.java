package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.biz.mapper.GwConfigMapper;
import com.cdz360.iot.gw.biz.mapper.GwInfoMapper;
import com.cdz360.iot.gw.biz.mapper.MqttInfoMapper;
import com.cdz360.iot.gw.biz.mapper.SSHReverseCloseMapper;
import com.cdz360.iot.gw.model.GwConfig;
import com.cdz360.iot.gw.model.GwInfo;
import com.cdz360.iot.gw.model.SSHReverseClose;
import com.cdz360.iot.gw.model.gw.MqttProperties;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DsWrapperService {

    @Autowired
    private GwConfigMapper gwConfigMapper;

    @Autowired
    private GwInfoMapper gwInfoMapper;

    @Autowired
    private MqttInfoMapper mqttInfoMapper;

    @Autowired
    private SSHReverseCloseMapper sshReverseCloseMapper;


    public GwConfig getGwConfig(String key) {
        synchronized (this) {
            return this.gwConfigMapper.get(key);
        }
    }


    public void addGw(GwInfo gw) {
        synchronized (this) {
            this.gwInfoMapper.addGw(gw);
        }
    }

    /**
     * 将所有的 cancel==false 变更为 cancel==true
     */

    public void updateAllGwToCancel() {

        synchronized (this) {
            this.gwInfoMapper.updateAllGwToCancel();
        }
    }

    /**
     * 获取激活的网关配置记录
     *
     * @return
     */
    public GwInfo getGwNotCancel() {
        synchronized (this) {
            return this.gwInfoMapper.getGwNotCancel();
        }
    }


    public void addMqttInfo(MqttProperties mqtt) {
        synchronized (this) {
            this.mqttInfoMapper.addMqttInfo(mqtt);
        }

    }

    /**
     * 删除
     *
     * @return
     */

    public int deleteMqtt() {
        synchronized (this) {
            return this.mqttInfoMapper.delete();
        }
    }


    /**
     * 获取激活的MQTT配置记录
     *
     * @return
     */
    public MqttProperties getMqttInfoNotCancel() {
        synchronized (this) {
            return this.mqttInfoMapper.getMqttInfoNotCancel();
        }
    }


    public void addSsh(SSHReverseClose reverse) {
        synchronized (this) {
            this.sshReverseCloseMapper.add(reverse);
        }
    }

    /**
     * 更新已有数据
     *
     * @param reverse
     */
    public void updateSsh(SSHReverseClose reverse) {
        synchronized (this) {
            this.sshReverseCloseMapper.update(reverse);
        }
    }

    public List<SSHReverseClose> getAllSsh() {
        synchronized (this) {
            return this.sshReverseCloseMapper.getAll();
        }
    }

    public void removeAllSsh() {
        synchronized (this) {
            this.sshReverseCloseMapper.removeAll();
        }
    }
}
