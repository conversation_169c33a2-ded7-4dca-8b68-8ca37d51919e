package com.cdz360.iot.gw.biz;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.evse.CloudCmdResultObjectReq;
import com.cdz360.iot.gw.model.evse.CloudEvseRebootResult;
import com.cdz360.iot.gw.model.evse.CloudSocCtrlResult;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.model.type.CloudResultType;
import com.cdz360.iot.gw.north.mq.model.ChargeSocCtrlMsg;
import com.cdz360.iot.gw.north.mq.model.MqEvseRebootMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.cdz360.iot.gw.north.server.IotClient;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 桩端下行指令相关服务
 */
@Slf4j
@Service
public class EvseCmdBizService {

    @Autowired
    private IotClient iotClient;

//    @Autowired
//    private UpgradeProcess upgradeProcess;

    private Map<String, MqMsgBase> cmdMap = new ConcurrentHashMap<>();

    public boolean existKey(String Key) {
        return this.cmdMap.containsKey(Key);
    }

    /**
     * @param evseNo  桩编号
     * @param evseSeq 桩端指令序列号
     * @param msg
     */
    public void addCmd(String evseNo, long evseSeq, MqMsgBase msg) {
        log.info("增加指令缓存: {}", genKey(evseNo, evseSeq));
        this.cmdMap.put(genKey(evseNo, evseSeq), msg);
        log.debug("当前: {}", this.cmdMap);
    }

    /**
     * @param evseNo 桩编号
     * @param msg
     */
    public void addCmd(String evseNo, MqMsgBase msg) {
        log.info("增加指令缓存key: {}", evseNo);
        this.cmdMap.put(evseNo, msg);
        log.debug("当前: {}", this.cmdMap);
    }

    public MqMsgBase removeCmd(String evseNo) {
        log.info("移除指令缓存key: {}", evseNo);
        MqMsgBase ret = this.cmdMap.remove(evseNo);
        log.debug("当前: {}", this.cmdMap);
        return ret;
    }

    public MqMsgBase removeCmd(String evseNo, long evseSeq) {
        String key = genKey(evseNo, evseSeq);
        log.info("移除指令缓存key: {}", key);
        MqMsgBase ret = this.cmdMap.remove(key);
        log.debug("当前: {}", this.cmdMap);
        return ret;
    }

    public void reportCmdResult(String traceId, String evseNo, long evseSeq, int result,
        String errorMsg) {
        String key = genKey(evseNo, evseSeq);
        MqMsgBase msg = this.cmdMap.get(key);
        if (msg == null) {
            log.warn("[{}] 无法找到下行指令. evseNo: {}, evseSeq: {}", traceId, evseNo, evseSeq);
            return;
        }
        if (IotGwCmdType2.CE_REBOOT == msg.getCmd()) {
            this.reportRebootResult(traceId, msg, result, errorMsg);
        } else if (IotGwCmdType2.CE_UPGRADE == msg.getCmd()) {
//            upgradeProcess.reportUpgradeResult(traceId, msg, result, null);
        } else if (IotGwCmdType2.CE_CHARGE_SOC_CTRL.equals(msg.getCmd())) {
            this.reportSocCtrlResult(traceId, msg, result, errorMsg);
        }
        log.info("[{}] 移除指令缓存key: {}", traceId, key);
        this.cmdMap.remove(key);
        log.debug("[{}] 当前: {}", traceId, this.cmdMap);
    }

    private void reportRebootResult(String traceId, MqMsgBase dwMsgBase, int result,
        String errorMsg) {
        MqEvseRebootMsg dwMsg = (MqEvseRebootMsg) dwMsgBase.getData();
        CloudUpReq<CloudCmdResultObjectReq<CloudEvseRebootResult>> upMsg = new CloudUpReq<>();

        CloudCmdResultObjectReq<CloudEvseRebootResult> data = new CloudCmdResultObjectReq<>();

        CloudEvseRebootResult detail = new CloudEvseRebootResult();
        detail.setEvseNo(dwMsg.getEvseNo()).setResult(CloudResultType.SUCCESS)
            .setTaskNo(dwMsg.getTaskNo());

        data.setCmd(dwMsgBase.getCmd());
        data.setSeq(dwMsgBase.getSeq());
        data.setStatus(result);
        if (result != IotGwConstants.SUCCESS) {
            detail.setResult(CloudResultType.FAIL);
            if (StringUtils.isNotBlank(errorMsg)) {
                data.setMsg(errorMsg);
            } else {
                data.setMsg("重启失败");
            }
        }
        data.setDetail(detail);
        upMsg.setSeq(SeqGeneratorUtil.newStringId());
        upMsg.setData(data);
        iotClient.sendCmdResult(traceId, upMsg);
    }

    private void reportSocCtrlResult(String traceId, MqMsgBase dwMsgBase, int result,
        String errorMsg) {
        ChargeSocCtrlMsg dwMsg = (ChargeSocCtrlMsg) dwMsgBase.getData();
        CloudUpReq<CloudCmdResultObjectReq<CloudSocCtrlResult>> upMsg = new CloudUpReq<>();

        CloudCmdResultObjectReq<CloudSocCtrlResult> data = new CloudCmdResultObjectReq<>();

        CloudSocCtrlResult detail = new CloudSocCtrlResult();
        detail.setOrderNo(dwMsg.getOrderNo())
            .setResult(result)
            .setSeq(dwMsgBase.getSeq());

        data.setCmd(dwMsgBase.getCmd());
        data.setSeq(dwMsgBase.getSeq());
        data.setStatus(result);
        if (result != IotGwConstants.SUCCESS) {
            detail.setResult(IotGwConstants.UNKNOWN_ERROR);
            if (StringUtils.isNotBlank(errorMsg)) {
                data.setMsg(errorMsg);
            } else {
                data.setMsg("soc限制值更新失败");
            }
        }
        data.setDetail(detail);
        upMsg.setSeq(SeqGeneratorUtil.newStringId());
        upMsg.setData(data);
        iotClient.sendCmdResult(traceId, upMsg);
    }


    private String genKey(String evseNo, long evseSeq) {
        return evseNo + "-" + String.valueOf(evseSeq);
    }
}
