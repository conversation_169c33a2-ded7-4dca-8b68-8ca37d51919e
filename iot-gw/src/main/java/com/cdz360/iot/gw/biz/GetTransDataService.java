package com.cdz360.iot.gw.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import eu.chargetime.ocpp.biz.TransDataFile;
import eu.chargetime.ocpp.model.core.StartTransactionRequest;
import eu.chargetime.ocpp.model.core.StatusNotificationRequest;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 桩状态查询服务 从trace_data日志文件中查询指定桩的最新状态信息
 */
@Slf4j
@Service
public class GetTransDataService {

    private final ObjectMapper objectMapper;

    @Autowired
    private EvseRepository evseRepository;

    public GetTransDataService() {
        this.objectMapper = new ObjectMapper();
        // 注册Java 8时间模块
        this.objectMapper.registerModule(new JavaTimeModule());
        // 禁用将日期写为时间戳的功能
        this.objectMapper.disable(
            com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    /**
     * 统一处理时间转换
     *
     * @param evseNo   充电桩编号
     * @param dateTime 需要转换的时间
     * @return 转换后的时间
     */
    private ZonedDateTime convertTimeIfNeeded(String evseNo, ZonedDateTime dateTime) {
        GwEvseVo evse = evseRepository.getEvse(evseNo);
        return OcppZonedDateTime.fromUtcTime(dateTime, evse.getBrand(), evse.getTimeZone());
    }

    /**
     * 根据evseNo查询枪的最近StatusNotification消息
     *
     * @param base
     * @param needReportPlugIdxList 需要查询的枪索引列表，例如[1, 2]
     * @return 包含指定枪的StatusNotification消息（键为connectorId，值为StatusNotificationRequest）
     */
    public Map<Integer, StatusNotificationRequest> queryStatusNotificationMsg(EvseMsgBase base,
        List<Integer> needReportPlugIdxList) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        log.info("[{} {}] 需要查询的枪索引: {}", tid, evseNo, needReportPlugIdxList);

        if (needReportPlugIdxList == null || needReportPlugIdxList.isEmpty()) {
            log.warn("[{} {}] 需要查询的枪索引列表为空", tid, evseNo);
            return createEmptyResult();
        }

        // STEP1. 使用TransDataFile获取当前正在写入的日志文件路径
        String currentLogFile = TransDataFile.getCurrentLogFilePath();
        log.info("[{} {}] 获取到的当前日志文件路径: {}", tid, evseNo, currentLogFile);

        // STEP2.检查文件是否存在
        File logFile = new File(currentLogFile);
        if (!logFile.exists() || !logFile.isFile()) {
            log.warn("[{} {}] 日志文件不存在或不是文件: {}", tid, evseNo, currentLogFile);
            return createEmptyResult();
        }

        // STEP3.从文件尾部开始逐行读取
        Map<Integer, StatusNotificationRequest> result = new HashMap<>();
        Set<Integer> foundConnectorIds = new HashSet<>();
        Set<Integer> targetConnectorIds = new HashSet<>(needReportPlugIdxList);

        try (RandomAccessFile raf = new RandomAccessFile(logFile, "r")) {
            long fileLength = raf.length();
            if (fileLength == 0) {
                log.warn("[{} {}] 日志文件为空: {}", tid, evseNo, currentLogFile);
                return createEmptyResult();
            }

            // 从文件末尾开始读取
            long pos = fileLength - 1;
            StringBuilder lineBuilder = new StringBuilder();

            while (pos >= 0 && foundConnectorIds.size() < targetConnectorIds.size()) {
                raf.seek(pos);
                int ch = raf.read();

                if (ch == '\n' || pos == 0) {
                    // 读到一行的开始，处理这一行
                    if (pos == 0 && ch != '\n') {
                        lineBuilder.insert(0, (char) ch);
                    }

                    String line = lineBuilder.reverse().toString().trim();
                    if (!line.isEmpty()) {
                        processStatusNotificationLine(base, line, targetConnectorIds,
                            foundConnectorIds, result);
                    }

                    lineBuilder.setLength(0);
                } else {
                    lineBuilder.append((char) ch);
                }

                pos--;
            }

        } catch (IOException e) {
            log.error("[{} {}] 读取日志文件时发生错误: {}", tid, evseNo, currentLogFile, e);
            return createEmptyResult();
        }

        log.info("[{} {}] 查询完成，找到 {} 个枪的状态信息", tid, evseNo, result.size());
        return result;
    }

    /**
     * 处理StatusNotification单行日志
     */
    private void processStatusNotificationLine(EvseMsgBase base, String line,
        Set<Integer> targetConnectorIds, Set<Integer> foundConnectorIds,
        Map<Integer, StatusNotificationRequest> result) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        try {
            // 按空格分隔，取最后一块内容
            String[] parts = line.split(" ");
            if (parts.length == 0) {
                return;
            }

            if (parts.length < 5) {
                return;
            }

            String evseNoString = parts[4].replaceAll("\\[", "").replaceAll("\\]", "");
            if (!evseNoString.equals(evseNo)) {
                return;
            }

            String lastPart = parts[parts.length - 1];

            // 判断是否为数组格式
            if (!lastPart.startsWith("[") || !lastPart.endsWith("]")) {
                return;
            }

            // 解析为JSON数组
            JsonNode arrayNode = objectMapper.readTree(lastPart);
            if (!arrayNode.isArray() || arrayNode.size() < 4) {
                return;
            }

            // 检查是否为StatusNotification消息
            JsonNode actionNode = arrayNode.get(2);
            if (!actionNode.isTextual() || !"StatusNotification".equals(actionNode.asText())) {
                return;
            }

            // 取数组中的第4个元素（索引3）
            JsonNode payloadNode = arrayNode.get(3);
            if (!payloadNode.isObject()) {
                return;
            }

            // 取connectorId字段
            JsonNode connectorIdNode = payloadNode.get("connectorId");
            if (!connectorIdNode.isNumber()) {
                return;
            }

            int connectorId = connectorIdNode.asInt();

            // 判断connectorId是否在需要查询的列表中，且还未找到
            if (targetConnectorIds.contains(connectorId) && !foundConnectorIds.contains(
                connectorId)) {
                // 将json对象转换为StatusNotificationRequest对象
                StatusNotificationRequest statusRequest = JsonUtils.fromJsonTime(
                    payloadNode.toString(), StatusNotificationRequest.class);

                // 设置base信息
                base.setCmdCode(EvseMessageType.EVSE_HB);
                base.setPlugIdx(connectorId);
                statusRequest.setBase(base);

                // 使用统一方法处理时间
                statusRequest.setTimestamp(
                    convertTimeIfNeeded(evseNo, statusRequest.getTimestamp()));

                result.put(connectorId, statusRequest);
                foundConnectorIds.add(connectorId);

                log.info("[{} {}] 找到connectorId: {} 的StatusNotification消息: {}", tid, evseNo,
                    connectorId, JsonUtils.toJsonTimeString(statusRequest));
            }

        } catch (Exception e) {
            log.info("[{} {}] 解析日志行时发生错误. line: {}, error: {}", tid, evseNo, line,
                e.getMessage());
            // 继续处理下一行，不中断整个流程
        }
    }

    /**
     * 创建空结果
     */
    private Map<Integer, StatusNotificationRequest> createEmptyResult() {
        return new HashMap<>();
    }


    /**
     * 根据idTag查询对应的StartTransaction消息
     *
     * @param evseNo 桩编号
     * @param idTag
     * @return StartTransaction消息
     */
    public Optional<StartTransactionRequest> queryStartTransactionMsg(String tid, String evseNo,
        String idTag, Integer plugIdx) {
        log.info("[{} {}] 需要查询的idTag: {}, plugIdx: {}", tid, evseNo, idTag, plugIdx);
        if (StringUtils.isEmpty(idTag) && plugIdx == null) {
            log.warn("[{} {}] 未指定idTag或plugIdx", tid, evseNo);
            return Optional.empty();
        }

        // STEP1. 使用TransDataFile获取当前正在写入的日志文件路径
        String currentLogFile = TransDataFile.getCurrentLogFilePath();
        log.info("[{} {}] 获取到的当前日志文件路径: {}", tid, evseNo, currentLogFile);

        // STEP2.检查文件是否存在
        File logFile = new File(currentLogFile);
        if (!logFile.exists() || !logFile.isFile()) {
            log.warn("[{} {}] 日志文件不存在或不是文件: {}", tid, evseNo, currentLogFile);
            return Optional.empty();
        }

        // STEP3.从文件尾部开始逐行读取
        Optional<StartTransactionRequest> result = Optional.empty();

        try (RandomAccessFile raf = new RandomAccessFile(logFile, "r")) {
            long fileLength = raf.length();
            if (fileLength == 0) {
                log.warn("[{} {}] 日志文件为空: {}", tid, evseNo, currentLogFile);
                return Optional.empty();
            }

            // 从文件末尾开始读取
            long pos = fileLength - 1;
            StringBuilder lineBuilder = new StringBuilder();

            while (pos >= 0 && result.isEmpty()) {
                raf.seek(pos);
                int ch = raf.read();

                if (ch == '\n' || pos == 0) {
                    // 读到一行的开始，处理这一行
                    if (pos == 0 && ch != '\n') {
                        lineBuilder.insert(0, (char) ch);
                    }

                    String line = lineBuilder.reverse().toString().trim();
                    if (!line.isEmpty()) {
                        result = processStartTransactionLine(tid, line, evseNo, idTag, plugIdx);
                    }

                    lineBuilder.setLength(0);
                } else {
                    lineBuilder.append((char) ch);
                }

                pos--;
            }

        } catch (IOException e) {
            log.error("[{} {}] 读取日志文件时发生错误: {}", tid, evseNo, currentLogFile, e);
            return Optional.empty();
        }
        return result;
    }

    /**
     * 处理StartTransaction单行日志
     */
    private Optional<StartTransactionRequest> processStartTransactionLine(String tid, String line,
        String evseNo, String idTag, Integer plugIdx) {
        try {
            // 按空格分隔，取最后一块内容
            String[] parts = line.split(" ");
            if (parts.length == 0) {
                return Optional.empty();
            }

            if (parts.length < 5) {
                return Optional.empty();
            }

            String evseNoString = parts[4].replaceAll("\\[", "").replaceAll("\\]", "");
            if (!evseNoString.equals(evseNo)) {
                return Optional.empty();
            }

            String lastPart = parts[parts.length - 1];

            // 判断是否为数组格式
            if (!lastPart.startsWith("[") || !lastPart.endsWith("]")) {
                return Optional.empty();
            }

            // 解析为JSON数组
            JsonNode arrayNode = objectMapper.readTree(lastPart);
            if (!arrayNode.isArray() || arrayNode.size() < 4) {
                return Optional.empty();
            }

            // 检查是否为StartTransaction消息
            JsonNode actionNode = arrayNode.get(2);
            if (!actionNode.isTextual() || !"StartTransaction".equals(actionNode.asText())) {
                return Optional.empty();
            }

            // 取数组中的第4个元素（索引3）
            JsonNode payloadNode = arrayNode.get(3);
            if (!payloadNode.isObject()) {
                return Optional.empty();
            }

            if (StringUtils.isNotEmpty(idTag)) {
                // 取idTag，判断是否匹配
                JsonNode idTagNode = payloadNode.get("idTag");
                if (!idTagNode.isTextual() || !idTag.equals(idTagNode.asText())) {
                    return Optional.empty();
                }
            } else if (NumberUtils.gtZero(plugIdx)) {
                // 取plugIdx，判断是否匹配
                JsonNode plugIdxNode = payloadNode.get("connectorId");
                if (!plugIdxNode.isNumber() || plugIdx != plugIdxNode.asInt()) {
                    return Optional.empty();
                }
            } else {
                log.warn("[{} {}] 未指定idTag或plugIdx", tid, evseNo);
                return Optional.empty();
            }

            // 将json对象转换为StartTransactionRequest对象
            StartTransactionRequest request = JsonUtils.fromJsonTime(payloadNode.toString(),
                StartTransactionRequest.class);

            // 设置base信息
            EvseMsgBase base = new EvseMsgBase();
            base.setTid(tid);
            base.setEvseNo(evseNo);
            base.setPlugIdx(request.getConnectorId());
            request.setBase(base);

            // 使用统一方法处理时间
            request.setTimestamp(convertTimeIfNeeded(evseNo, request.getTimestamp()));

            if (StringUtils.isNotEmpty(idTag)) {
                log.info("[{} {}] 找到idTag {} 对应的StartTransaction消息", tid, evseNo, idTag);
            } else if (NumberUtils.gtZero(plugIdx)) {
                log.info("[{} {}] 找到plugIdx {} 对应的StartTransaction消息", tid, evseNo, plugIdx);
            }
            return Optional.of(request);

        } catch (Exception e) {
            log.info("[{} {}] 解析日志行时发生错误. line: {}, error: {}", tid, evseNo, line,
                e.getMessage());
            // 继续处理下一行，不中断整个流程
        }
        return Optional.empty();
    }


}