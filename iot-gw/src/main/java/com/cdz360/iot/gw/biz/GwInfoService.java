package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.model.GwInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GwInfoService {
    private final Logger logger = LoggerFactory.getLogger(GwInfoService.class);

    @Autowired
    private DsWrapperService mapper;

    //@Transactional(rollbackFor = Exception.class)
    public GwInfo getGwNotCancel() {
        logger.info("从 SQLite 中获取网关配置信息.");
        return mapper.getGwNotCancel();
    }

    //@Transactional(rollbackFor = Exception.class)
    public void addGwInfo(GwInfo gw) {
        logger.info("将网关配置信息添加到 SQLite: {}.", gw);

        // 只保留一条有效数据
        mapper.updateAllGwToCancel();

        mapper.addGw(gw);
    }
}
