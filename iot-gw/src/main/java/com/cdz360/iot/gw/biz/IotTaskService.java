package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvsePackageRepository;
import com.cdz360.iot.gw.biz.task.CheckNetTask;
import com.cdz360.iot.gw.biz.task.GwLoginTask;
import com.cdz360.iot.gw.biz.task.IotDynamicTaskManager;
import com.cdz360.iot.gw.model.type.ScheduledTaskFlag;
import com.cdz360.iot.gw.north.server.IotUpService;
import java.util.Timer;
import java.util.TimerTask;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class IotTaskService implements InitializingBean {

    private final Logger logger = LoggerFactory.getLogger(IotTaskService.class);
    @Autowired
    GwConfigService gwConfigService;
    @Autowired
    private IotDynamicTaskManager taskManager;
    @Autowired
    private IotUpService iotUpService;
    @Autowired
    private EvsePackageRepository evsePackageRepository;

    @Autowired
    private EvseOrderRepository evseOrderRepository;


    /**
     * 离线检测网络状态定时任务
     */
    public void checkNetTask() {
        logger.info(">> ");
        taskManager.startCron(ScheduledTaskFlag.CHECK_NET, new CheckNetTask(iotUpService));
        logger.info("<<");
    }

    // @Scheduled(cron = "0 0/10 * * * ? ") // 每10分钟1次
    @Scheduled(cron = "0 0/1 * * * ? ") // 每1分钟1次
    public void checkLostPackageTask() {
        // logger.debug("丢包检查。。。。。。");
        evsePackageRepository.reportLostPackages();
    }

    @Scheduled(cron = "0 0/1 * * * ? ") // 每1分钟1次
    public void savePlugCache2File() {
        evseOrderRepository.dump2File();
    }

    @Scheduled(cron = "0 0 */1 * * ?") // 每个小时1次
    public void checkStoppedOrder() {
        String tid = RandomStringUtils.randomAlphabetic(12).toUpperCase();
        logger.debug("[{}] 删除过期订单。。。。", tid);
        evseOrderRepository.findAndDelOrder(tid);
    }


    /**
     * 网关登录的定时任务
     */
    public void loginTask() {
        logger.info(">> ");
        taskManager.startCron(ScheduledTaskFlag.LOGIN, new GwLoginTask(iotUpService));

        // 立即调用
        new Timer("Timer-Login").schedule(new TimerTask() {
            @Override
            public void run() {
                iotUpService.login();
            }
        }, 0);

        logger.info("<<");
    }

    /**
     * 停止对应的定时任务
     *
     * @param taskFlag
     */
    public void stopTask(ScheduledTaskFlag taskFlag) {
        logger.trace(">> ");
        taskManager.stopCron(taskFlag);
        logger.trace("<<");
    }

    public boolean isAlive(ScheduledTaskFlag taskFlag) {
        return taskManager.isAlive(taskFlag);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 非透传模式开启上线定时任务
        //        if (!gwConfig.getTrans()) { // 透传模式
        //            this.onlineTask();
        //        }
        // 开启上线功能
        //this.onlineTask();
    }

}
