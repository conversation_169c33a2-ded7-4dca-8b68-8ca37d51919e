package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.model.gw.MqttProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MqttInfoService {
    private final Logger logger = LoggerFactory.getLogger(MqttInfoService.class);

    @Autowired
    private DsWrapperService mapper;

    //@Transactional(rollbackFor = Exception.class)
    public MqttProperties getMqttInfoNotCancel() {
        logger.info("从SQLite中获取MQTT配置信息.");
        return mapper.getMqttInfoNotCancel();
    }

    //@Transactional(rollbackFor = Exception.class)
    public synchronized void addMqttInfo(MqttProperties mqttInfo) {
        logger.info("将MQTT配置信息添加到 SQLite: {}.", mqttInfo);

        // 只保留一条有效数据
        mapper.deleteMqtt();
        mapper.addMqttInfo(mqttInfo);

        // mapper.replaceInfo(mqttInfo);
    }
}
