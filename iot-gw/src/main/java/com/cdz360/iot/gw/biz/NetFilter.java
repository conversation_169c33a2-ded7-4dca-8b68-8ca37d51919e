package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.model.type.ScheduledTaskFlag;
import com.cdz360.iot.gw.north.server.IotClient;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.atomic.AtomicBoolean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * 指令传输，网络监控
 */
@Service
@Scope(value = "singleton")
public class NetFilter {
    private final Logger logger = LoggerFactory.getLogger(NetFilter.class);

    @Autowired
    private IotClient iotClient;


    // Cache Manager
    @Autowired
    private OfflineFileManager fileManager;

    @Autowired
    private IotTaskService taskService;


    @Autowired
    private IdempotenceService idempotenceService;

    // 网络状态
    //  true: OK
    // false: FAIL
    private AtomicBoolean netStatus = new AtomicBoolean(true);

    // 重新上线后启动上报积压消息标识
    private AtomicBoolean alreadyStart = new AtomicBoolean(false);

    // 上报积压消息中止标识
    private AtomicBoolean offlineUpBreakUp = new AtomicBoolean(false);


    /**
     * 非透传保存到文件
     */
    private void toCache(String json) {
        logger.info("[非透传][离线] 保存到文件: {}", json);
        this.fileManager.putFallback(json);
    }


    public void callback(Object o) {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                if (o instanceof String) {
                    callback((String) o);
                } else {
                    // callback((IotRequest) o);
                    logger.warn("不支持的callback. object: {}", o);
                }
            }
        }, 0);
    }

    /**
     * 网络异常回调处理函数
     */
    private void callback(String json) {
        logger.info("offline callback: {}", json);
        // 不做重发机制
        this.netStatus.set(false);
        this.toCache(json);

        // 启动登录注册定时任务
        // CheckNetTask
        this.startCheckNetTask();
    }


    /**
     * 启动登录注册定时任务，用于校验网络状态
     */
    public void startCheckNetTask() {
        // 启动登录注册定时任务
        if (!this.taskService.isAlive(ScheduledTaskFlag.CHECK_NET)) {
            this.taskService.checkNetTask();
        }
    }

    /**
     * 判断是否符合变更网络状态
     *
     * @return
     */
    public boolean canChangeNetStatus() {
        // 判断是否符合变更网络状态
        // 条件要求: 缓存文件已经上传完成，并且缓存队列也没有信息
        return !this.fileManager.isOfflineMode();
    }

    /**
     * 变更网络状态
     *
     * @param status
     */
    public void changeNetStatus(boolean status) {
        this.netStatus.set(status);
    }

    /**
     * 网络恢复
     */
    public void netRecover() {
        // 网络恢复后处理
        if (this.canChangeNetStatus()) {
            logger.info("处理离线数据上传完毕。");
            this.netStatus.set(true);

            // 变更标识，用于下次开启上报
            this.alreadyStart.set(false);

            // 停止网络状态
            if (this.taskService.isAlive(ScheduledTaskFlag.CHECK_NET)) {
                this.taskService.stopTask(ScheduledTaskFlag.CHECK_NET);
            }
        } else {
            this.netStatus.set(false);

            // 启动将文件缓存报文信息上传
            this.startUp2Cloud();
        }
    }

    /**
     * 启动将文件报文信息上传云端
     */
    private void startUp2Cloud() {
        // 启动将文件报文信息上传云端
        // Timer + while
        if (!this.alreadyStart.get()) {
            logger.info("开启上传离线数据");
            // 标识已经开启上报任务
            this.alreadyStart.set(true);

            new Timer().schedule(new TimerTask() {
                @Override
                public void run() {
                    read2UpCloud();
                }
            }, 0);
            logger.info("<< 开启成功");
        }
    }

    /**
     * 上线将报文信息上传
     */
    private void read2UpCloud() {
        // 启动离线上传任务
        this.offlineUpBreakUp.set(false);

        while (fileManager.isOfflineMode()) {
            // 读取报文
            String line = fileManager.getLine();

            if (StringUtils.isEmpty(line)) {
                continue;
            }

            // 是否中止
            if (this.offlineUpBreakUp.get()) {
                logger.info("<< 上传离线数据中止");
                return; // 中止
            }


            this.clientUp(line);


            // 防止频繁上传
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        // 标识已经开启上报任务
        this.alreadyStart.set(false);
        this.changeNetStatus(true);

        logger.info("<< 上传离线数据结束");
    }


    /**
     * 非透传模式上传离线报文
     *
     * @param line
     */
    private void clientUp(String line) {
        logger.info("[非透传] 离线报文上传: {}", line);

        // 上传到云端, 成功则移除
        String res = send2Cloud(line);

        this.fileManager.removeLine();

        if (null == res || "null".equals(res)) {
            logger.warn("离线文件上报失败。request: {}", line);
        }
    }

    /**
     * 非透传离线上报云端
     *
     * @param json
     * @return
     */
    public String send2Cloud(String json) {
        return iotClient.offlineSendMsg(json);
    }

    /**
     * 中断离线上传
     */
    public void breakOff() {
        // 网络状态
        // this.netStatus.set(false);

        // 中止离线上传
        this.alreadyStart.set(false);
        this.offlineUpBreakUp.set(true);

        // 启动网络检验定时任务
        this.taskService.checkNetTask();
    }


}
