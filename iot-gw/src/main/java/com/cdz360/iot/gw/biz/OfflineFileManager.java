package com.cdz360.iot.gw.biz;

import com.cdz360.base.utils.JsonUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.LinkedBlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 离线文件管理类
 */
@Service
public class OfflineFileManager   {

    private final Logger logger = LoggerFactory.getLogger(OfflineFileManager.class);

    @Autowired
    private OfflineFileService offlineFileService;


    // 离线待保存队列
    private Queue<String> msgQ = new LinkedBlockingQueue<>();

    public OfflineFileManager() {

    }

    /**
     * 在离线队列中插入一行，启动线程同步至离线文件中
     *
     * @param line
     */
    public void putFallback(String line) {

        if (!StringUtils.isEmpty(line)) {
            msgQ.add(line);
        }

        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                saveToFile();
            }
        }, 0);

    }

    /**
     * <p>当队列和文件里有未处理的离线信息时</p>
     * 判断是否有离线数据没有上报
     *
     * @return true: 存在待上报报文 false: 已经完成
     */
    public synchronized boolean isOfflineMode() {
        return !msgQ.isEmpty() || !offlineFileService.readFirstLines(1).isEmpty();
    }

    /**
     * 获取离线文件中头几行数据
     *
     * @param cnt 行数量
     * @return 离线数据列表
     */
    private List<String> getFallback(Integer cnt) {
        List<String> ret = offlineFileService.readFirstLines(cnt);
        if (ret.isEmpty()) {
            return null;
        } else {
            return new ArrayList<>(ret);
        }
    }

    /**
     * 获取一行
     *
     * @return
     */
    public String getLine() {
        List<String> list = this.getFallback(1);
        if (null == list || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 删除离线文件中头几行数据
     *
     * @param cnt 行数量
     */
    private void removeFallback(Integer cnt) {
        offlineFileService.removeFirstLines(cnt);
    }

    /**
     * 移除一行
     */
    public void removeLine() {
        this.removeFallback(1);
    }

    /**
     * 从队列中取出信息,存入离线文件中
     */
    private synchronized void saveToFile() {
        // 把队列里的消息保存到离线文件中
        if (msgQ.size() == 0) {
            return;
        }

        // 整合队列中所有的消息
        List<String> list = new ArrayList<>();
        while (msgQ.peek() != null) {
            String obj = msgQ.poll();

            if (null == obj) {
                return;
            }

            list.add(obj);
        }
        logger.info("[保存]=====================》 [离线文件]: {}", JsonUtils.toJsonString(list));

        offlineFileService.writeLines(list);
    }

}
