package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.config.OfflineProperties;
import com.cdz360.iot.gw.util.FileUtil;
import jakarta.annotation.PostConstruct;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 离线文件处理
 */
@EnableConfigurationProperties({OfflineProperties.class})
@Configuration
@Service
public class OfflineFileService {
    private final Logger logger = LoggerFactory.getLogger(OfflineFileService.class);

    @Autowired
    private OfflineProperties offlineProperties;

    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    @PostConstruct
    public void postConstruct() {
        String path = offlineProperties.getPath();
        File folder = new File(path);
        //如果文件夹不存在则创建
        if (!folder.exists() && !folder.isDirectory()) {
            folder.mkdirs();
        }
    }

    /**
     * 保存多行信息至离线缓存文件
     *
     * @param lines
     */
    public synchronized void writeLines(List<String> lines) {
        if(!offlineProperties.getEnable()){
            // 不开启离线日志上传功能
            return;
        }
        String path = offlineProperties.getPath();

        String fileName = "";
        //查询文件夹下所有的离线保存文件
        List<File> fileList = FileUtil.getFileListSort(path);

        if (fileList.size() > 0) {
            //原始最新的文件
            File lastFile = fileList.get(fileList.size() - 1);

            long fileSize = FileUtil.getFileSize(new File(path + File.separator + lastFile.getName()));
            //判断文件是否大于500M(99%),小于的话使用原来最新的文件,大于则另起一个文件
            if (fileSize <= Long.parseLong(offlineProperties.getMaxMB()) * 1024 * 1024 * 99 / 100) {
                fileName = path + File.separator + lastFile.getName();
            }
        }

        if (StringUtils.isEmpty(fileName)) {
            //新的文件
            fileName = path + File.separator + dateFormat.format(new Date(System.currentTimeMillis())) + ".txt";
        }

        FileUtil.saveLines(lines, fileName);
    }

    /**
     * 从文件中取出前几行信息
     *
     * @param lineNum 取出的前几行数量
     * @return 取出的行信息
     */
    public synchronized List<String> readFirstLines(int lineNum) {
        if (lineNum <= 0) {
            return Collections.emptyList();
        }
        String path = offlineProperties.getPath();
        //查询文件夹下所有的离线保存文件
        List<File> fileList = FileUtil.getFileListSort(path);
        List<String> lines = new ArrayList<>();
        if (fileList.size() > 0) {
            int remainNum = lineNum;
            for (int i = 0; i < fileList.size(); i++) {
                File file = fileList.get(i);
                List<String> readLines = FileUtil.readFirstLines(path + File.separator + file.getName(), remainNum);
                if (readLines.size() < remainNum) {
                    remainNum = remainNum - readLines.size();

                } else {//==
                    remainNum = 0;
                }
                lines.addAll(readLines);
                if (remainNum == 0) {
                    break;
                }
            }
        }
        return lines;
    }

    /**
     * 删除文件中前几行信息
     *
     * @param lineNum 删除的前几行数量
     * @return 删除的行信息
     */
    public synchronized List<String> removeFirstLines(int lineNum) {
        if (lineNum <= 0) {
            return Collections.emptyList();
        }
        String path = offlineProperties.getPath();
        //查询文件夹下所有的离线保存文件
        List<File> fileList = FileUtil.getFileListSort(path);
        List<String> lines = new ArrayList<>();
        if (fileList.size() > 0) {
            int remainNum = lineNum;
            for (File file : fileList) {
                List<String> readLines = FileUtil.readAndRemoveFirstLines(path + File.separator + file.getName(), remainNum);
                if (readLines.size() < remainNum) {
                    remainNum = remainNum - readLines.size();

                    //取完后删除文件
                    FileUtil.deleteFile(path + File.separator + file.getName());
                } else {//==
                    remainNum = 0;
                }
                lines.addAll(readLines);
                if (remainNum == 0) {
                    break;
                }
            }
        }
        return lines;
    }

}
