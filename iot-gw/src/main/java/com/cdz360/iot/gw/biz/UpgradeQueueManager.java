package com.cdz360.iot.gw.biz;

import com.cdz360.iot.gw.north.mq.model.EvseVersion;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 升级队列管理器 用于管理多文件升级时的队列处理
 */
@Slf4j
@Component
public class UpgradeQueueManager {

    // 存储每个桩的升级队列
    private final ConcurrentMap<String, UpgradeQueue> upgradeQueues = new ConcurrentHashMap<>();

    /**
     * 添加升级队列
     *
     * @param evseNo            桩编号
     * @param remainingVersions 待升级的版本列表
     * @param accountNo         账号
     * @param password          密码
     */
    public void addUpgradeQueue(String tid, String evseNo, List<EvseVersion> remainingVersions,
        String accountNo, String password) {
        if (remainingVersions == null || remainingVersions.isEmpty()) {
            log.warn("桩 {} 的升级队列为空", evseNo);
            return;
        }

        UpgradeQueue queue = new UpgradeQueue(tid, remainingVersions, accountNo, password);
        upgradeQueues.put(evseNo, queue);
        log.info("为桩 {} 添加升级队列，待升级文件数: {}", evseNo, remainingVersions.size());
    }

    /**
     * 获取下一个要升级的版本
     *
     * @param evseNo 桩编号
     * @return 下一个版本，如果没有则返回null
     */
    public EvseVersion getNextVersion(String evseNo) {
        UpgradeQueue queue = upgradeQueues.get(evseNo);
        if (queue == null) {
            log.debug("桩 {} 没有升级队列", evseNo);
            return null;
        }

        EvseVersion nextVersion = queue.getNextVersion();
        if (nextVersion != null) {
            log.info("桩 {} 获取下一个升级版本: {}, 剩余: {}", evseNo, nextVersion.getName(),
                queue.getRemainingCount());
        } else {
            log.info("桩 {} 没有更多待升级的版本", evseNo);
            // 队列已完成，可以清理
            upgradeQueues.remove(evseNo);
        }

        return nextVersion;
    }

    /**
     * 检查是否还有待升级的版本
     *
     * @param evseNo 桩编号
     * @return true如果还有待升级的版本
     */
    public boolean hasNext(String evseNo) {
        UpgradeQueue queue = upgradeQueues.get(evseNo);
        return queue != null && queue.hasNext();
    }

    /**
     * 获取升级队列信息
     *
     * @param evseNo 桩编号
     * @return 升级队列信息
     */
    public UpgradeQueue getUpgradeQueue(String evseNo) {
        return upgradeQueues.get(evseNo);
    }

    /**
     * 清除升级队列
     *
     * @param evseNo 桩编号
     */
    public void clearUpgradeQueue(String evseNo) {
        UpgradeQueue removed = upgradeQueues.remove(evseNo);
        if (removed != null) {
            log.info("清除桩 {} 的升级队列", evseNo);
        }
    }

    /**
     * 获取当前所有升级队列的桩编号
     *
     * @return 桩编号集合
     */
    public java.util.Set<String> getAllUpgradingEvseNos() {
        return upgradeQueues.keySet();
    }

    /**
     * 升级队列数据结构
     */
    @Data
    public static class UpgradeQueue {

        private String tid;
        private List<EvseVersion> remainingVersions;
        private String accountNo;
        private String password;
        private int currentIndex = 0;

        public UpgradeQueue(String tid, List<EvseVersion> remainingVersions, String accountNo,
            String password) {
            this.remainingVersions = remainingVersions;
            this.accountNo = accountNo;
            this.password = password;
        }

        /**
         * 获取下一个要升级的版本
         *
         * @return 下一个版本，如果没有则返回null
         */
        public EvseVersion getNextVersion() {
            if (currentIndex < remainingVersions.size()) {
                return remainingVersions.get(currentIndex++);
            }
            return null;
        }

        /**
         * 检查是否还有待升级的版本
         *
         * @return true如果还有待升级的版本
         */
        public boolean hasNext() {
            return currentIndex < remainingVersions.size();
        }

        /**
         * 获取剩余版本数量
         *
         * @return 剩余版本数量
         */
        public int getRemainingCount() {
            return remainingVersions.size() - currentIndex;
        }
    }
} 