package com.cdz360.iot.gw.biz;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.GwConfigProperties;
import com.cdz360.iot.gw.model.GwInfo;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 配置文件操作
 * 用于更改网关编号和网关密码
 */
@Service
public class YamlOptService {
    private final Logger logger = LoggerFactory.getLogger(YamlOptService.class);

    @Autowired
    private GwInfoService gwInfoService;

    @Autowired
    private GwConfigProperties gwConfigProperties;

    /**
     * 校验添加的内容是否符合格式要求
     *
     * @param newConf
     * @return
     */
    private boolean checkContext(Map<String, String> newConf) {
        // 所有key值
        if (!newConf.containsKey("gwno") || !newConf.containsKey("passcode")) {
            logger.debug("new config not match, gwno exit: {}, passcode exit: {}.",
                    newConf.containsKey("gwno"),
                    newConf.containsKey("passcode"));
            return false;
        }

        // gwno 内容校验
        String gwno = newConf.get("gwno");
        if (!gwno.matches("[a-zA-Z0-9]{1,}")) {
            logger.debug("gwno not match, gwno: {}.", gwno);
            return false;
        }

        // gwno 内容校验
        String passcode = newConf.get("passcode");
        if (!gwno.matches("[a-zA-Z0-9]{1,}")) {
            logger.debug("passcode not match, passcode: {}.", passcode);
            return false;
        }

        return true;
    }

    /**
     * 更新网关信息
     *
     * @param newContextMap
     */
    public void updateGwInit(Map<String, String> newContextMap) {

        logger.info("修改网关配置。map: {}", JsonUtils.toJsonString(newContextMap));

        // 校验新添加的内容是否符合
        if (!this.checkContext(newContextMap)) {
            logger.info("配置不合理已忽略。map: {}", JsonUtils.toJsonString(newContextMap));
            return;
        }

        // 直接更新配置项
        gwConfigProperties.setGwno(newContextMap.get("gwno"));
        gwConfigProperties.setPasscode(newContextMap.get("passcode"));

        // 保存sqLite
        gwInfoService.addGwInfo(new GwInfo() {{
            setVersion(gwConfigProperties.getVersion());
            setGwno(newContextMap.get("gwno"));
            setPasscode(newContextMap.get("passcode"));
        }});
    }
}
