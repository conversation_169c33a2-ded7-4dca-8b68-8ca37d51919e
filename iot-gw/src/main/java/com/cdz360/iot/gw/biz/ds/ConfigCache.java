package com.cdz360.iot.gw.biz.ds;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Scope(value = "singleton")
public class ConfigCache {

    // private final Logger logger = LoggerFactory.getLogger(ConfigCache.class);

    private Map<String, String> channelCache = new ConcurrentHashMap<>();

    public synchronized ConfigCache put(String configPrefix, String evseId, String map) {
        channelCache.put(configPrefix + evseId, map);
        return this;
    }

    public synchronized String get(String configPrefix, String evseId) {
        return channelCache.get(configPrefix + evseId);
    }

    public synchronized boolean containsKey(String configPrefix, String evseId) {
        return channelCache.containsKey(configPrefix + evseId);
    }

    public synchronized void remove(String configPrefix, String evseId) {
        channelCache.remove(configPrefix + evseId);
    }

    public synchronized int size() {
        return channelCache.size();
    }
}