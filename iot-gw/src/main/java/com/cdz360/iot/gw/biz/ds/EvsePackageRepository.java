package com.cdz360.iot.gw.biz.ds;

import com.cdz360.iot.gw.model.EvseSeq;
import com.cdz360.iot.gw.model.IotRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import org.apache.commons.collections4.queue.CircularFifoQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 丢包检测缓存
 */
@Service
public class EvsePackageRepository {

    private static final int TIME_OUT = 60 * 1000;//ms
    private static final int MAX_SIZE = 1000;
    private static final int SEQ_MAX_GAP = 50;  // 桩端重启后会生成一个随机数作为初始的序列号, 如果检测到序列号超过SEQ_MAX_GAP, 则判断桩端发生了重启.
    private static Map<String, CircularFifoQueue<EvseSeq>> evseSeqMap = new ConcurrentHashMap<>();//收到的报文序列号递增的包（这边只存递增的包）
    private static Map<String, List<EvseSeq>> lostSeqMap = new ConcurrentHashMap<>();//丢失的包 及丢失中的包
    private final Logger logger = LoggerFactory.getLogger(EvsePackageRepository.class);

    public void packetLossCheck(IotRequest msg) {
        logger.info("packetLossCheck中IotRequest {}", msg);

        Integer seq = msg.getSeqNo();
        if (seq == null) {
            seq = 0;
        }

        String evseId = msg.getBaseMsg().getEvseNo();
        if (evseId == null) {
            evseId = "";
        }

        logger.info("报文解析中的msg.getPlugNo: {}", msg.getPlugNo());

        int plugNo =
            msg.getPlugNo() == Byte.MIN_VALUE || msg.getPlugNo() == 0 ? 1 : msg.getPlugNo();//默认一号枪

        long revTime = System.currentTimeMillis();

        logger.info("桩枪报文序列号：evseId: {}, plugNo: {}, seq: {}", evseId, plugNo, seq);

        if (!StringUtils.isEmpty(evseId) && seq > 0) {
            dealWithSeq(evseId, mixId(evseId, plugNo), seq, revTime);
        }

    }

    //保存递增的包,保存丢失的包,推迟1分钟没收到丢失的包则认为丢包
    private synchronized void dealWithSeq(String evseNo, String mixId, int seq, long revTime) {
        // map  evseId ->seq time
        long curTime = System.currentTimeMillis();
        EvseSeq evseSeq = new EvseSeq(mixId, seq, revTime);

        CircularFifoQueue<EvseSeq> evseSeqs = null;
        if (evseSeqMap.get(mixId) == null) {
            //生成固定长度的队列
            evseSeqs = new CircularFifoQueue<>(MAX_SIZE);
            evseSeqMap.put(mixId, evseSeqs);
        } else {
            evseSeqs = evseSeqMap.get(mixId);
        }

        if (evseSeqs.size() == 0) {
            evseSeqs.add(evseSeq);
            return;
        }
        //2个及以上才能判断
        //EvseSeq evseSeqLast = evseSeqs.get(evseSeqs.size() - 1);
        EvseSeq evseSeq2nd = evseSeqs.get(evseSeqs.size() - 1);
        //序号相差大于1
        if (Math.abs(evseSeq.getSeq() - evseSeq2nd.getSeq()) > SEQ_MAX_GAP) {
            logger.warn("序列号差过大.{} .. {}, 可能桩端发生重启. evseNo: {}", evseSeq.getSeq(),
                evseSeq2nd.getSeq(), evseNo);
            evseSeqs.add(evseSeq);//保存递增的包
        } else if (evseSeq.getSeq() - evseSeq2nd.getSeq() > 1) {
            //报文系列号间隔大于1,存在丢包
            evseSeqs.add(evseSeq);//保存递增的包
            List<EvseSeq> lostSeqList = lostSeqMap.get(mixId);
            if (lostSeqList == null) {
                lostSeqList = new CopyOnWriteArrayList<>();
                lostSeqMap.put(mixId, lostSeqList);
            }

            for (int i = 1; i < evseSeq.getSeq() - evseSeq2nd.getSeq(); i++) {
                EvseSeq lostSeq = new EvseSeq(mixId, evseSeq2nd.getSeq() + i);
                lostSeq.setLostTime(curTime);
                logger.warn("mixId：{}, losting：{}", mixId, lostSeq.getSeq());
                lostSeqList.add(lostSeq);
            }


        } else if (evseSeq.getSeq() - evseSeq2nd.getSeq() == 1) {
            //报文系列号间隔为1,为正常的
            evseSeqs.add(evseSeq);//保存递增的包
        } else if (evseSeq.getSeq() - evseSeq2nd.getSeq() < 0) {
            //报文系列号小于前面的，即前面丢失的包传了过来
            List<EvseSeq> lostSeqList = lostSeqMap.get(mixId);
            if (lostSeqList != null) {
                //删除, CopyOnWriteArrayList无法使用迭代器
                for (int i = 0; i < lostSeqList.size(); i++) {
                    EvseSeq next = lostSeqList.get(i);
                    //检查丢失的包是否超时,删除没超时的
                    if (next != null && next.getSeq() - evseSeq.getSeq() == 0 && !timeOut(curTime,
                        next.getLostTime())) {
                        lostSeqList.remove(next);
                        --i;//删除一个元素,游标往前挪一位
                        break;
                    }
                }
            }
        }
    }

    //上报丢包
    public void reportLostPackages() {
        // logger.info("丢包检查===================》");
        if (lostSeqMap.size() > 0) {
            long curTime = System.currentTimeMillis();
            for (String mixId : lostSeqMap.keySet()) {
                List<EvseSeq> evseSeqList = lostSeqMap.get(mixId);
                if (!CollectionUtils.isEmpty(evseSeqList)) {
                    List<EvseSeq> realLosts = evseSeqList.stream()
                        .filter(t -> timeOut(curTime, t.getLostTime()))
                        .collect(Collectors.toList());
                    logger.warn("lost package mixId {}, seq msg {}", mixId, realLosts);
                    //清理已经上报的
                    if (!CollectionUtils.isEmpty(realLosts)) {
                        synchronized (this) {
                            evseSeqList.removeAll(realLosts);
                        }
                    }
                }
            }
        }
    }

    private boolean timeOut(long curTime, long lostTime) {
        return curTime - lostTime > TIME_OUT;
    }

    private String mixId(String evseId, int plugNo) {
        return evseId + "#" + plugNo;
    }

}
