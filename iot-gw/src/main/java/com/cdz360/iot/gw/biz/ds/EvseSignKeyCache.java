package com.cdz360.iot.gw.biz.ds;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 桩签名密钥包装类
 */
@Slf4j
@Component
@Scope(value = "singleton")
public class EvseSignKeyCache {

    private static final long LOCAL_CACHE_TTL = 60 * 60 * 1000; // 1小时本地缓存有效期
    private static final ConcurrentMap<String, EvsePasscode> channelCache = new ConcurrentHashMap<>();

    @Autowired
    private RedisRwService redisRwService;

    /**
     * @param evseNo   桩编号
     * @param passcode 密钥(16字节 ~ 40字节)
     * @return
     */
    public EvseSignKeyCache put(String evseNo, String passcode) {
        channelCache.put(evseNo, new EvsePasscode(passcode));
        redisRwService.setPasscode(evseNo, passcode);
        return this;
    }

    public String get(String evseNo) {
        EvsePasscode passcode = channelCache.get(evseNo);
        if (passcode == null) {
            // 本地缓存不匹配时从redis获取
            return this.getFromRedis(evseNo);
        } else if (passcode.getExpire() < new Date().getTime()) {
            // 本地缓存的key超过ttl
            return this.getFromRedis(evseNo);
        } else {
            return passcode.getPasscode();
        }
    }

    /**
     * 直接从redis获取缓存的密钥, 且刷新本地缓存
     */
    public String getFromRedis(String evseNo) {
        String passcode = redisRwService.getPasscode(evseNo);
        if (StringUtils.isEmpty(passcode)) {
            log.info("redis无法获取到桩密钥. evseNo: {}", evseNo);
            return null;
        } else {
            channelCache.put(evseNo, new EvsePasscode(passcode));
            return passcode;
        }
    }

//    public boolean containsKey(String evseNo,  @Nullable Long ver) {
//        return channelCache.containsKey(this.buildKey(evseNo, ver));
//    }

    public void remove(String evseNo) {
        channelCache.remove(evseNo);
    }

    /*
    private String buildKey(String evseNo, Long ver) {
        if (ver == null) {
            return evseNo;
        } else {
            return evseNo + "-" + ver;
        }
    }
    */

    public int size() {
        return channelCache.size();
    }

    @Data
    private static class EvsePasscode {

        String passcode;
        long expire;

        public EvsePasscode(String passcode) {
            this.passcode = passcode;
            this.expire = new Date().getTime() + LOCAL_CACHE_TTL;
        }
    }
}