package com.cdz360.iot.gw.biz.ds;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/*

幂等性服务接口

*/
@Slf4j
@Service
public class IdempotenceService   {

    private final Lock lock = new ReentrantLock();
    @Value("${cdz360.cache.max-size}")
    private int maxSize;
    @Value("${cdz360.cache.clean-size}")
    private int cleanSize;
    private static final Map<String, String> cacheMap = new LinkedHashMap<>();

    public void put(String seqNo, String data) {
        try {
            lock.lock();
            cacheMap.put(seqNo, data);
            clean();
        } finally {
            lock.unlock();
        }
    }

    public String get(String seqNo) {
        try {
            lock.lock();
            clean();
            return cacheMap.get(seqNo);
        } finally {
            lock.unlock();
        }
    }

    public String remove(String seqNo) {
        try {
            lock.lock();
            clean();
            return cacheMap.remove(seqNo);
        } finally {
            lock.unlock();
        }
    }

    private void clean() {
        if (cacheMap.size() > maxSize) {
            log.info("start to drop seq cache. size = {}, maxSize = {}", cacheMap.size(), maxSize);
            int index = 0;
            Iterator<String> iterator = cacheMap.keySet().iterator();
            while (iterator.hasNext()) {
                if (index >= cleanSize) {
                    break;
                }

                iterator.next();//需要移动游标到下一个元素，否则删除会报错。
                iterator.remove();

                index++;
            }
            log.info("drop seq cache end. size = {}, maxSize = {}", cacheMap.size(), maxSize);
        }
    }
}
