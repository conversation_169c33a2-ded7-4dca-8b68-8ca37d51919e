package com.cdz360.iot.gw.biz.ds;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class IotGwCacheService {

    private final static Map<String, Object> cache = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(IotGwCacheService.class);

    public String getToken(String gwno) {
        // logger.debug("从缓存中获取token。 gwno:{}", gwno);

        String key = this.formatTokenKey(gwno);
        Object obj = cache.get(key);
        return null == obj ? null : cache.get(key).toString();
    }

    /**
     * 更新本地缓存中的token
     *
     * @param gwno
     * @param token
     */
    public void updateToken(String gwno, String token) {
        String key = this.formatTokenKey(gwno);
        cache.put(key, token);
    }

    private String formatTokenKey(String gwno) {
        return "token-" + gwno;
    }

//    /**
//     * 添加字段缓存
//     *
//     * @param key
//     * @param value
//     */
//    public void updateField(String key, String value) {
//        cache.put(key, value);
//    }

//    /**
//     * 获取字段内容
//     *
//     * @param key
//     * @return
//     */
//    public Object getField(String key) {
//        Object obj = cache.get(key);
//        return null == obj ? null : cache.get(key).toString();
//    }

    public void remove(String gwno) {
        if (null != cache.get(this.formatTokenKey(gwno))) {
            cache.remove(this.formatTokenKey(gwno));
        }
    }

//    public void removeField(String key) {
//        if (null != cache.get(key)) {
//            cache.remove(key);
//        }
//    }
}
