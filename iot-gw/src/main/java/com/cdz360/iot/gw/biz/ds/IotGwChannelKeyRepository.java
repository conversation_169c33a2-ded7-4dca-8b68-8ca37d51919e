package com.cdz360.iot.gw.biz.ds;

import com.cdz360.iot.gw.model.connection.EvseConnection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 桩 netty tcp 连接 key 映射管理
 */
@Component
@Slf4j
@Scope(value = "singleton")
public class IotGwChannelKeyRepository   {

    // key: 桩号; value: netty 连接
    private static final ConcurrentMap<String, EvseConnection> channelCache = new ConcurrentHashMap<>();



    public synchronized IotGwChannelKeyRepository put(String evseNo, EvseConnection channelKey) {
        channelCache.put(evseNo, channelKey);
        return this;
    }

    public synchronized EvseConnection get(String evseNo) {
        if (StringUtils.isEmpty(evseNo)) {
            return null;
        }
        return channelCache.get(evseNo);
    }

    public synchronized void remove(String evseNo) {
        channelCache.remove(evseNo);
    }

    public synchronized int size() {
        return channelCache.size();
    }

    public synchronized String getKey(String value) {
        // String key = null;
        for (Map.Entry<String, EvseConnection> m : channelCache.entrySet()) {
            if (m.getValue().getClientAddress().equals(value)) {
                return m.getKey();
            }
        }
        return null;
    }

    public synchronized void removeByValue(EvseConnection connection) {
        if (connection == null) {
            return;
        }

        String key = getKey(connection.getClientAddress());
        channelCache.remove(key);
    }

    public synchronized List<EvseConnection> getClients() {
        List<EvseConnection> list = new ArrayList<>();
        channelCache.forEach((key, value) -> {
            list.add(value);
        });

        return list;
    }
}
