package com.cdz360.iot.gw.biz.ds;

import com.cdz360.iot.gw.model.connection.PlugConnection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 记录枪相关信息，用作超时判断
 */
@Slf4j
@Component
@Scope(value = "singleton")
public class PlugCache {

    private static final ConcurrentMap<String, PlugConnection> plugMap = new ConcurrentHashMap<>();

    public void put(String evseId, int plugNo, PlugConnection plug) {
        String key = evseId + ":" + plugNo;
        plugMap.put(key, plug);
    }

    public PlugConnection get(String evseId, int plugNo) {
        String key = evseId + ":" + plugNo;
        return plugMap.get(key);
    }

    public boolean containsKey(String evseId, int plugNo) {
        String key = evseId + ":" + plugNo;
        return plugMap.containsKey(key);
    }

    public void remove(String evseId, int plugNo) {
        String key = evseId + ":" + plugNo;
        plugMap.remove(key);
    }

    public int size() {
        return plugMap.size();
    }

}