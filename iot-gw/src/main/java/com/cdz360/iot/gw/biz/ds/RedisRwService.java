package com.cdz360.iot.gw.biz.ds;

import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.model.iot.vo.TransformerVo;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.iot.gw.north.model.OrderStopRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Service
public class RedisRwService {

    private final static String EVSE_PASSCODE_PREFIX = "iot:gw:evse:passcode:";
    private final static String OFFLINE_ORDER_STOP_REQUEST = "iot:gw:order:";

    @Autowired
    protected StringRedisTemplate redisTemplate;


    @Autowired
    private RedisIotRwService redisIotRwService;

    public void setOfflineOrderStopRequest(OrderStopRequest request) {
        String key = OFFLINE_ORDER_STOP_REQUEST + request.getData().getOrderNo();
        this.redisTemplate.opsForValue().set(key, request.toString());
    }

    public String getPasscode(String evseNo) {
        String key = this.formatEvsePasscodeKey(evseNo, null);
        return redisTemplate.opsForValue().get(key);
    }

    public void setPasscode(String evseNo, String passcode) {
        String key = this.formatEvsePasscodeKey(evseNo, null);
        this.redisTemplate.opsForValue().set(key, passcode);
    }

    private String formatEvsePasscodeKey(String evseNo, Long ver) {
        StringBuilder buf = new StringBuilder();
        buf.append(EVSE_PASSCODE_PREFIX).append(evseNo);
        if (ver != null) {
            buf.append(":").append(ver);
        }
        return buf.toString();
    }

    public TransformerVo getTransformerRedisCache(Long tfmId) {
        return this.redisIotRwService.getTransformerRedisCache(tfmId);
    }

    public EvseVo getEvseRedisCache(String evseNo) {
        return this.redisIotRwService.getEvseRedisCache(evseNo);
    }

    public PlugVo getPlugRedisCache(String plugNo) {
        return this.redisIotRwService.getPlugRedisCache(plugNo);
    }

    public PlugVo getPlugRedisCache(String evseNo, int idx) {
        return this.redisIotRwService.getPlugRedisCache(evseNo, idx);
    }

    public void updateTransformerRedisCache(@NonNull TransformerVo tfm) {
        this.redisIotRwService.updateTransformerRedisCache(tfm);
    }

    public void updateEvseCache(EvseVo evse) {
        this.redisIotRwService.updateEvseRedisCache(evse);
    }

    public PlugVo updatePlugCache(PlugVo plug) {
        return this.redisIotRwService.updatePlugRedisCache(plug);
    }


}
