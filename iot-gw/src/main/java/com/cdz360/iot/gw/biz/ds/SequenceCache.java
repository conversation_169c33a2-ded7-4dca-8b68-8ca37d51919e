package com.cdz360.iot.gw.biz.ds;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Scope(value = "singleton")
public class SequenceCache {

    private static final ConcurrentMap<String, String> channelCache = new ConcurrentHashMap<>();

    public synchronized SequenceCache put(String seq, String map) {
        channelCache.put(seq, map);
        return this;
    }

    public synchronized String get(String seq) {
        return channelCache.get(seq);
    }

    public synchronized boolean containsKey(String seq) {
        return channelCache.containsKey(seq);
    }

    public synchronized void remove(String seq) {
        channelCache.remove(seq);
    }

    public synchronized int size() {
        return channelCache.size();
    }
}