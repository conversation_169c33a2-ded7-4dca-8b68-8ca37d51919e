package com.cdz360.iot.gw.biz.mapper;

import com.cdz360.iot.gw.model.GwConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface GwConfigMapper {
    /**
     * 获取网关的指定配置项
     *
     * @return 网关配置
     */
    @Select("select config_key as key, config_value as value, value_type as valueType from gw_config where is_enabled = 1 and config_key = #{key}")
    GwConfig get(@Param("key") String key);
}
