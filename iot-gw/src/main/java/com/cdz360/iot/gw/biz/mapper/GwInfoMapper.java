package com.cdz360.iot.gw.biz.mapper;

import com.cdz360.iot.gw.model.GwInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface GwInfoMapper {
    /**
     * 添加一条网关配置记录
     * @param gw
     */
    @Insert("insert into gw_info" +
            "(cancel, version, gwno, passcode, create_time, update_time)" +
            "values" +
            "(0, #{version}, #{gwno}, #{passcode}, datetime(CURRENT_TIMESTAMP,'localtime'), datetime(CURRENT_TIMESTAMP,'localtime'))")
    void addGw(GwInfo gw);

    /**
     * 将所有的 cancel==false 变更为 cancel==true
     */
    @Update("update gw_info set cancel=1 where cancel=0")
    void updateAllGwToCancel();

    /**
     * 获取激活的网关配置记录
     * @return
     */
    @Select("select cancel, gwno, version, passcode, create_time, update_time from gw_info where cancel=0")
    GwInfo getGwNotCancel();
}
