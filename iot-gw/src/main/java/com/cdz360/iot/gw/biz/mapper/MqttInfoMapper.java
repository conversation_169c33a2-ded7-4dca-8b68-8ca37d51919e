package com.cdz360.iot.gw.biz.mapper;

import com.cdz360.iot.gw.model.gw.MqttProperties;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface MqttInfoMapper {

    /**
     * 添加一条MQTT配置记录
     * @param mqtt
     */
    @Insert("insert into mqtt_info" +
            "(cancel, url, clientId, username, password, topic, create_time, update_time)" +
            "values" +
            "(0, #{url}, #{clientId}, #{username}, #{password}, #{topic}, datetime(CURRENT_TIMESTAMP,'localtime'), datetime(CURRENT_TIMESTAMP,'localtime'))")
    void addMqttInfo(MqttProperties mqtt);

    // @Insert("replace into mqtt_info" +
    //         "(cancel, url, clientId, username, password, topic, create_time, update_time)" +
    //         "values" +
    //         "(0, #{url}, #{clientId}, #{username}, #{password}, #{topic}, datetime(CURRENT_TIMESTAMP,'localtime'), datetime(CURRENT_TIMESTAMP,'localtime'))")
    // void replaceInfo(MqttProperties mqtt);

    /**
     * 删除
     * @return
     */
    @Delete("DELETE FROM mqtt_info")
    int delete();

    /**
     * 将所有的 cancel==false 变更为 cancel==true
     */
    @Update("update mqtt_info set cancel=1 where cancel=0")
    void updateAllMqttInfoToCancel();

    /**
     * 获取激活的MQTT配置记录
     * @return
     */
    @Select("select cancel, url, clientId, username, password, topic, create_time, update_time from mqtt_info where cancel=0")
    MqttProperties getMqttInfoNotCancel();
}
