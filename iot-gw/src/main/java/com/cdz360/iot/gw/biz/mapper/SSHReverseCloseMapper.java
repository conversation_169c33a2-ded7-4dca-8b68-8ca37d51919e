package com.cdz360.iot.gw.biz.mapper;

import com.cdz360.iot.gw.model.SSHReverseClose;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface SSHReverseCloseMapper {

    /**
     * 获取激活的网关配置记录
     */
    @Select("select flag, pid, expire, create_time createTime, update_time updateTime from ssh_reverse_close")
    List<SSHReverseClose> getAll();

    /**
     * 添加一条ssh关闭信息记录
     * @param reverse
     */
    @Insert("insert into ssh_reverse_close " +
            "(flag, pid, expire, create_time, update_time) values " +
            "(#{flag}, #{pid}, #{expire}, datetime(CURRENT_TIMESTAMP,'localtime'), datetime(CURRENT_TIMESTAMP,'localtime'))")
    void add(SSHReverseClose reverse);

    /**
     * 更新已有数据
     * @param reverse
     */
    @Update("update ssh_reverse_close set " +
            "update_time=datetime(CURRENT_TIMESTAMP,'localtime'), expire=#{expire} where flag=#{flag}")
    void update(SSHReverseClose reverse);

    /**
     * 删除所有ssh关闭信息记录
     */
    @Delete("delete from ssh_reverse_close")
    void removeAll();
}
