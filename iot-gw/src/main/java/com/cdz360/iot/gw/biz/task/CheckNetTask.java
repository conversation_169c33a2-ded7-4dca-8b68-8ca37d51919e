package com.cdz360.iot.gw.biz.task;

import com.cdz360.iot.gw.north.server.IotUpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 离线后启动定时确认网络状态
 */
public class CheckNetTask implements Runnable {
    private final Logger logger = LoggerFactory.getLogger(CheckNetTask.class);

    private final IotUpService iotUpService;

    public CheckNetTask(IotUpService iotUpService) {
        this.iotUpService = iotUpService;
    }

    @Override
    public void run() {
        logger.info(">> ");
        this.iotUpService.checkNet();
        logger.info("<<");
    }
}
