package com.cdz360.iot.gw.biz.task;

import com.cdz360.iot.gw.north.server.IotUpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 登录任务
 */
public class GwLoginTask implements Runnable {
    private final Logger logger = LoggerFactory.getLogger(GwLoginTask.class);

    private final IotUpService iotUpService;

    public GwLoginTask(IotUpService iotUpService) {
        this.iotUpService = iotUpService;
    }

    @Override
    public void run() {
        logger.info(">> ");
        iotUpService.login();
        logger.info("<<");
    }
}
