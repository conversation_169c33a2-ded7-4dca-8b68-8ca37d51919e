package com.cdz360.iot.gw.biz.task;

import com.cdz360.iot.gw.model.type.ScheduledTaskFlag;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ScheduledFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Component;

/**
 * 动态任务管理
 */
@Component
public class IotDynamicTaskManager {

    // 暂时只有网关心跳和网关登录token刷新定时任务，定时时间暂定如下
    private static final ConcurrentMap<ScheduledTaskFlag, String> scheduledTimerMap = new ConcurrentHashMap<ScheduledTaskFlag, String>() {{
        // 秒 分钟 小时 天 月 星期 年
        // put(ScheduledTaskFlag.ONLINE, "0 */1 * * * ?");  // 每分钟1次
        put(ScheduledTaskFlag.LOGIN, "0 0 */1 * * ?");   // 每小时1次
        //put(ScheduledTaskFlag.LOGIN, "0 0/20 * * * ?");   // 每3分钟1次
        //put(ScheduledTaskFlag.LOGIN, "0 0 */1 * * ?"); // 每天1次
        // put(ScheduledTaskFlag.HB, "0/5 * * * * *");      // 每5秒1次
        put(ScheduledTaskFlag.CHECK_NET, "0/10 * * * * *");      // 每10秒1次
        // put(ScheduledTaskFlag.CHECK_CFG, "0/10 * * * * *");      // 每10秒1次
        // put(ScheduledTaskFlag.CHECK_LOST_PACKAGE, "0 0/10 * * * ? ");      // 每10分钟1次
        // put(ScheduledTaskFlag.CHECK_STOPED_ORDER_DEL, "0 0 */1 * * ?");      // 每小时1次
        // put(ScheduledTaskFlag.SAVE_PERSISTENT, "0/1 * * * * *");      // 每1秒1次
    }};
    // 创建一个管理类 map,管理future
    private static ConcurrentMap<ScheduledTaskFlag, ScheduledFuture> scheduledMap = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(IotDynamicTaskManager.class);
    @Autowired
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;
    ;

    @Bean
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        return new ThreadPoolTaskScheduler();
    }

    /**
     * 开始新的定时任务
     *
     * @param taskFlag
     * @param taskRunnable
     */
    public void startCron(ScheduledTaskFlag taskFlag, Runnable taskRunnable) {
        logger.info("start scheduled: {}.", taskFlag);

        // 定时时间
        String cron = "0/10 * * * * *"; // 每10秒1次
        if (scheduledTimerMap.containsKey(taskFlag)) {
            cron = scheduledTimerMap.get(taskFlag);
        }

        // 如果已经存在，需要停止
        if (scheduledMap.containsKey(taskFlag)) {
            logger.info("<< scheduled {} is exit.", taskFlag);
            //            this.stopCron(taskFlag);
            return;
        }

        // 添加定时任务到map
        scheduledMap.put(taskFlag,
            threadPoolTaskScheduler.schedule(taskRunnable, new CronTrigger(cron)));
        logger.info("<< start success");
    }

    /**
     * 停止定时任务
     *
     * @param taskFlag
     */
    public void stopCron(ScheduledTaskFlag taskFlag) {
        logger.info("stop scheduled: {}.", taskFlag);

        // 判断定时任务是否存在
        if (scheduledMap.containsKey(taskFlag) && null != scheduledMap.get(taskFlag)) {
            ScheduledFuture<?> future = scheduledMap.get(taskFlag);
            future.cancel(true);
            scheduledMap.remove(taskFlag);
        }

        logger.info("<< stop success");
    }

    public boolean isAlive(ScheduledTaskFlag taskFlag) {
        return scheduledMap.containsKey(taskFlag);
    }
}
