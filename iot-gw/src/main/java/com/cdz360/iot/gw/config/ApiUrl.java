package com.cdz360.iot.gw.config;

public interface ApiUrl {

    String URL_GW_REGISTER = "/iot/gw/register?v={version}&n={number}";

    String URL_LOGIN = "/iot/gw/login?v={version}&n={number}";

    String URL_PROCESS = "/iot/process?v={version}&n={number}";

    String URL_CFG_EVSE = "/iot/cfg/evse?v={version}&n={number}";
    String URL_GET_PRICE = "/iot/ce/evse/price?v={version}&n={number}";

    String URL_GET_EVSE_OP_INFO = "/iot/ce/evse/getOpInfo?v={version}&n={number}";

    String URL_CFG_EVSE_INFO = "/iot/cfg/evse/info?v={version}&n={number}";

    String URL_CFG_EVSE_RESULT = "/iot/cfg/evse/result?v={version}&n={number}";

    String URL_EVSE_PASSCODE = "/iot/evse/passcode?v={version}&n={number}";
    String URL_EVSE_REGISTER = "/iot/evse/register?v={version}&n={number}";
    String URL_EVSE_STATUS = "/iot/evse/status?v={version}&n={number}";
    String URL_EVSE_REPORT_MODULE = "/iot/evse/reportModule?v={version}&n={number}";

    String URL_SITE_GET_TRANSFORMER_LIST = "/iot/site/getTransformerList?v={version}&n={number}";
    String URL_SITE_GET_EVSE_LIST = "/iot/site/getEvseList?v={version}&n={number}";
    String URL_SITE_GET_PLUG_LIST = "/iot/site/getPlugList?v={version}&n={number}";

    String URL_CREATE_ORDER = "/iot/order/create?v={version}&n={number}";
    // String URL_STARTING_ORDER = "/iot/order/starting?v={version}&n={number}";
    String URL_START_RESULT = "/iot/cmd/result?v={version}&n={number}";
    String URL_START_ORDER = "/iot/order/start?v={version}&n={number}";
    String URL_STOP_ORDER = "/iot/order/stop?v={version}&n={number}";
    String URL_UPDATE_ORDER = "/iot/order/update?v={version}&n={number}";

    String URL_REQUEST_AUTH = "/iot/cus/auth?v={version}&n={number}";

    String URL_ORDER_FEE_REFRESH = "/iot/order/fee/refresh?v={version}&n={number}";

    String URL_EVSE_UPGRADE = "/iot/upgrade/evse/result?v={version}&n={number}";

    String URL_CMD_RESULT = "/iot/cmd/result?v={version}&n={number}";
}