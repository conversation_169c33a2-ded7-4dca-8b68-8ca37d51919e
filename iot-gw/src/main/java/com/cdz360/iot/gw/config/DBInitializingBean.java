package com.cdz360.iot.gw.config;

import com.cdz360.iot.gw.biz.GwInfoService;
import com.cdz360.iot.gw.biz.IotTaskService;
import com.cdz360.iot.gw.model.GwInfo;
import com.cdz360.iot.gw.north.server.IotUpService;
import com.cdz360.iot.gw.util.DBUtil;
import java.io.File;
import java.util.Timer;
import java.util.TimerTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Description
 * @Date 2019/1/10
 **/
@Component
@EnableConfigurationProperties({SqliteProperties.class})
@Configuration
public class DBInitializingBean implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(DBInitializingBean.class);

    @Autowired
    private SqliteProperties sqliteProperties;

    @Autowired
    private GwConfigProperties gwConfig;

    @Autowired
    private GwInfoService gwInfoService;

    @Autowired
    private IotTaskService taskService;

    @Autowired
    private IotUpService iotUpService;

    /**
     * 初始化db
     *
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() throws Exception {

        //先判断硬盘上的sqlite db目录是否存在,不存在则新建目录
        createIfNotExistsDir();

        //初始化sqlite db表结构
        initDB(sqliteProperties.getUrl1(), sqliteProperties.getInitBusinessSql());
        initDB(sqliteProperties.getUrl2(), sqliteProperties.getInitCoreDependSql());

        //网关初始化、登录
        initRegisterAndLogin();

    }

    private void initRegisterAndLogin() {
        logger.info("网关出厂初始化。。。");

        boolean isRegister = checkIsRegister();

        new Timer("Timer-Register").schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    if (isRegister) {
                        logger.info("网关已注册，登录中。。。");
                        // 启动登录定时任务
                        taskService.loginTask();
                    } else {
                        logger.info("网关未注册，注册中。。。");
                        //网关初始化
                        if (iotUpService.register()) {
                            logger.info("网关初始化完成===========================");
                            // 启动登录定时任务
                            taskService.loginTask();
                        }
                    }
                } catch (Exception e) {
                    logger.error("initRegisterAndLogin fail: {}", e.getMessage(), e);
                }
            }
        }, 100);
    }

    private boolean checkIsRegister() {
        //上线网关初始化
        //先判断yml文件中是否有 密码
        boolean isRegister = false;
        if (StringUtils.isEmpty(gwConfig.getPasscode()) ||
                StringUtils.isEmpty(gwConfig.getGwno()) ||
                gwConfig.getPasscode().equals("factory001") ||
                gwConfig.getGwno().equals("factory001")) {
            // 没有网关密码时，从sqlite取网关编号、密码
            GwInfo gw = gwInfoService.getGwNotCancel();
            if (null != gw && !StringUtils.isEmpty(gw.getPasscode())) {
                gwConfig.setGwno(gw.getGwno());
                gwConfig.setPasscode(gw.getPasscode());
                isRegister = true;
            }
        } else {
            isRegister = true;
        }
        return isRegister;
    }

    /**
     * 先判断硬盘上的sqlite db目录是否存在,不存在则新建目录
     */
    private void createIfNotExistsDir() {
        File file1 = new File(sqliteProperties.getFullPath1());
        File file2 = new File(sqliteProperties.getFullPath2());
        if (!file1.exists() || !file2.exists()) {
            File folder = new File(sqliteProperties.getPath());
            //如果文件夹不存在则创建
            if (!folder.exists() && !folder.isDirectory()) {
                folder.mkdirs();
            }

        }
    }

    /**
     * 初始化sqlite db
     */
    private void initDB(String url, String sqlName) {
        DBUtil.setConnection(url);

        String path = "/" + sqlName;
        DBUtil.excuteByScriptExecuteor(path);

        DBUtil.endConnection();
    }


}
