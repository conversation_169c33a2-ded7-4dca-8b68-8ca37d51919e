package com.cdz360.iot.gw.config;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.sqlite.SQLiteDataSource;

@Component
@EnableConfigurationProperties({SqliteProperties.class})
@Configuration
public class DataSourceConfig {
    @Autowired
    private SqliteProperties sqliteProperties;


    @Bean(name = "ds1")
    @Primary
    public DataSource dataSource1() {
        SQLiteDataSource dataSource = new SQLiteDataSource();
        dataSource.setUrl(sqliteProperties.getUrl1());
        return dataSource;
    }

    @Bean(name = "ds2")
    public DataSource dataSource2() {
        SQLiteDataSource dataSource = new SQLiteDataSource();
        dataSource.setUrl(sqliteProperties.getUrl2());
        return dataSource;
    }

}
