package com.cdz360.iot.gw.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@PropertySource(name = "gw-info",
    value = {"${cdz360.gw-info.file}", "file:${cdz360.profiles.path}/gw-info.yml"},
    ignoreResourceNotFound = true)
public class GwConfigProperties {

    @Value("${version}")
    private int version;   // 版本号

    @Value("${gwno}")
    private String gwno;      // 网关编号

    @Value("${passcode}")
    private String passcode;  // 网关密码

    @Value("${realm}")
    private String realm;     // 授权校验，默认realm

//    @Value("${trans}")
//    private boolean trans;   // 透传模式

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getGwno() {
        return gwno;
    }

    public void setGwno(String gwno) {
        this.gwno = gwno;
    }

    public String getPasscode() {
        return passcode;
    }

    public void setPasscode(String passcode) {
        this.passcode = passcode;
    }

    public String getRealm() {
        return realm;
    }

    public void setRealm(String realm) {
        this.realm = realm;
    }

//    public boolean getTrans() {
//        return trans;
//    }

//    public void setTrans(boolean trans) {
//        this.trans = trans;
//    }

    @Override
    public String toString() {
        return "GwConfigProperties{" +
            "version='" + version + '\'' +
            ", gwno='" + gwno + '\'' +
            ", passcode='" + passcode + '\'' +
            ", realm='" + realm + '\'' +
            '}';
    }
}
