package com.cdz360.iot.gw.config;

import java.math.BigDecimal;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
public class IotConfigProperties {


    /**
     * 启动充电保证金
     */
    @Value("${cdz360.deposit.startOrder:0.50}")
    private BigDecimal startOrderDeposit;

    // 运营态临时配置缓存
    @Value("${cdz360.profiles.rtCfgPath:./data}")
    private String rtCfgPath;


}
