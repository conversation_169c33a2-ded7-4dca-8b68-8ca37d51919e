package com.cdz360.iot.gw.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class IotGwApiBeans {

    @Value("${cdz360.api}")
    private String baseUrl;

    @Bean
    public WebClient.Builder webClientBuilder() {
        WebClient.Builder builder = WebClient.builder();
        builder.baseUrl(baseUrl);
        return builder;
    }
}
