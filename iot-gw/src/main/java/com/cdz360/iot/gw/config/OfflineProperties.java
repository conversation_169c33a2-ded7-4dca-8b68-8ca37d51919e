package com.cdz360.iot.gw.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 离线保存文件 相关的配置参数
 */
@ConfigurationProperties(prefix = "offline")
public class OfflineProperties {

    private Boolean enable; // 是否启用
    private String path;//目录
    private String maxMB;//文件最大尺寸

    public boolean getEnable() {
        return enable == null ? false : enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMaxMB() {
        return maxMB;
    }

    public void setMaxMB(String maxMB) {
        this.maxMB = maxMB;
    }


}