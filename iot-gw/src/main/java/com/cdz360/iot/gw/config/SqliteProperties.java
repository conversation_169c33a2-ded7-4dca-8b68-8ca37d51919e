package com.cdz360.iot.gw.config;

import java.io.File;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * sqlite 相关的配置参数
 */
@ConfigurationProperties(prefix = "sqlite")
public class SqliteProperties {

    private String path;
    private String name1;
    private String name2;
    private String initBusinessSql;
    private String initCoreDependSql;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getName1() {
        return name1;
    }

    public void setName1(String name1) {
        this.name1 = name1;
    }

    public String getName2() {
        return name2;
    }

    public void setName2(String name2) {
        this.name2 = name2;
    }

    public String getInitBusinessSql() {
        return initBusinessSql;
    }

    public void setInitBusinessSql(String initBusinessSql) {
        this.initBusinessSql = initBusinessSql;
    }

    public String getInitCoreDependSql() {
        return initCoreDependSql;
    }

    public void setInitCoreDependSql(String initCoreDependSql) {
        this.initCoreDependSql = initCoreDependSql;
    }

    public  String getFullPath1() {
        return  path + File.separator + name1 + ".db";
    }

    public  String getFullPath2() {
        return  path + File.separator + name2 + ".db";
    }

    public String getUrl1() {
        return "jdbc:sqlite:" + path + File.separator + name1 + ".db";
    }

    public String getUrl2() {
        return "jdbc:sqlite:" + path + File.separator + name2 + ".db";
    }

}