package com.cdz360.iot.gw.config.ws;

import eu.chargetime.ocpp.JSONServer;
import eu.chargetime.ocpp.feature.profile.ServerCoreProfile;
import eu.chargetime.ocpp.feature.profile.ServerFirmwareManagementProfile;
import eu.chargetime.ocpp.feature.profile.ServerLocalAuthListProfile;
import eu.chargetime.ocpp.feature.profile.ServerRemoteTriggerProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class JsonServerConfig {

    @Bean
    public JSONServer jsonServer(ServerCoreProfile core,
        ServerFirmwareManagementProfile firmwareManagement,
        ServerLocalAuthListProfile localAuthList,
        ServerRemoteTriggerProfile serverRemoteTrigger) {

        JSONServer jsonServer = new JSONServer(core);
        jsonServer.addFeatureProfile(firmwareManagement);
        jsonServer.addFeatureProfile(localAuthList);
        jsonServer.addFeatureProfile(serverRemoteTrigger);
        return jsonServer;
    }

}
