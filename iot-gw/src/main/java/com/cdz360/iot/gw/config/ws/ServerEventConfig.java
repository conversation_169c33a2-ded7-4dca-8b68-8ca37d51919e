package com.cdz360.iot.gw.config.ws;

import static eu.chargetime.ocpp.utilities.TraceUtil.shortSession;

import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.model.base.SocketInfo;
import com.cdz360.iot.gw.south.server.EvseAdaptationService;
import com.cdz360.iot.gw.south.server.SocketRepository;
import eu.chargetime.ocpp.AuthenticationException;
import eu.chargetime.ocpp.ServerEvents;
import eu.chargetime.ocpp.model.SessionInformation;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.utilities.EvseUtil;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class ServerEventConfig {

    @Autowired
    private SocketRepository socketRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Autowired
    private EvseAdaptationService evseAdaptationService;

    @Bean
    public ServerEvents createServerCoreImpl() {
        return getNewServerEventsImpl();
    }

    private ServerEvents getNewServerEventsImpl() {
        return new ServerEvents() {

            @Override
            public void authenticateSession(SessionInformation information, String username,
                String password) throws AuthenticationException {
                String tid = information.getTid();
                String evseNo = EvseUtil.getEvseNo(information.getIdentifier());

                /*
                // 在这里可以进行基于真实IP的认证逻辑
                String realIp = information.getProxiedAddress();
                if (realIp != null && !realIp.equals("unknown")) {
                    log.info("[{} {}] 客户端真实IP: {} 通过认证", tid, evseNo, realIp);
                     handleClientConnection(sessionIndex, tid, realIp, evseNo);
                }
                */

                if (!evseStatusReportService.authenticateSessionProcess(tid, evseNo, password)) {
                    throw new AuthenticationException(401, "Invalid username or password");
                }
            }

            @Override
            public void newSession(UUID sessionIndex, SessionInformation information) {
                // sessionIndex is used to send messages.
                String tid = information.getTid();
                String socketAddr = information.getSocketAddress();
                String evseNo = EvseUtil.getEvseNo(information.getIdentifier());

                SocketInfo info = new SocketInfo();
                info.setSocketAddr(socketAddr);
                info.setSessionIndex(sessionIndex);
                info.setEvseNo(evseNo);
                socketRepository.put(socketAddr, info);

                // 在WS连接建立时，若桩存在，则下发MessageTrigger：StatusNotification
                EvseMsgBase base = new EvseMsgBase(tid, socketAddr);
                base.setEvseNo(evseNo);
                evseAdaptationService.triggerStatusNotification(base, sessionIndex);

                /*
                EvseMsgBase base = new EvseMsgBase(sessionIndex.toString(), socketAddr);
                base.setCmdCode(EvseMessageType.EVSE_REGISTER)
                    .setEvseNo(evseNo);
                BaseEvseMsgUp request = new BaseEvseMsgUp(base);
                evseProcessor.preprocess(request);
                */
            }

            @Override
            public void lostSession(UUID sessionIndex, String socketAddr) {
                log.info("[{} {}] Lost session.", shortSession(sessionIndex), socketAddr);

                //桩断线后实时上报桩/枪状态 & 更新本地桩缓存的状态
                evseStatusReportService.reportWhenTimeout(socketAddr);
            }
    
            /**
             * 处理客户端连接
             * 
             * @param sessionIndex 会话ID
             * @param realIp 客户端真实IP
             * @param evseNo 设备标识
             */
            private void handleClientConnection(UUID sessionIndex, String tid, String realIp, String evseNo) {
                log.info("处理来自 {} 的设备 {} 连接", realIp, evseNo);
                
                // 示例：基于IP的业务逻辑
                if (realIp != null && !realIp.equals("unknown")) {
                    // 1. IP地理位置查询
                    String location = getIpLocation(tid, evseNo, realIp);
                    log.info("[{} {}]客户端IP {} 地理位置: {}", tid, evseNo, realIp, location);
                    
                    // 2. IP白名单检查
                    if (isIpWhitelisted(tid, evseNo, realIp)) {
                        log.info("[{} {}]IP {} 在白名单中", tid, evseNo, realIp);
                    } else {
                        log.warn("[{} {}]IP {} 不在白名单中", tid, evseNo, realIp);
                    }
                    
                    // 3. 连接频率限制
                    checkConnectionRateLimit(tid, evseNo, realIp);
                    
                    // 4. 记录连接日志
                    logConnectionInfo(tid, sessionIndex, realIp, evseNo);
                }
            }
    
            /**
             * 获取IP地理位置（示例方法）
             * 
             * @param ip IP地址
             * @return 地理位置信息
             */
            private String getIpLocation(String tid, String evseNo, String ip) {
                // 这里可以集成IP地理位置查询服务
                // 例如：GeoIP2、IP2Location等
                return "未知位置";
            }
            
            /**
             * 检查IP是否在白名单中（示例方法）
             * 
             * @param ip IP地址
             * @return 是否在白名单中
             */
            private boolean isIpWhitelisted(String tid, String evseNo, String ip) {
                // 这里可以实现IP白名单检查逻辑
                // 可以从数据库、配置文件或缓存中读取白名单
                return true; // 示例：默认允许
            }
            
            /**
             * 检查连接频率限制（示例方法）
             * 
             * @param ip IP地址
             */
            private void checkConnectionRateLimit(String tid, String evseNo, String ip) {
                // 这里可以实现连接频率限制逻辑
                // 可以使用Redis等缓存来记录连接次数和时间
                log.debug("[{} {}]检查IP {} 的连接频率限制", tid, evseNo, ip);
            }
            
            /**
             * 记录连接信息（示例方法）
             * 
             * @param sessionIndex 会话ID
             * @param realIp 真实IP
             * @param evseNo 设备标识
             */
            private void logConnectionInfo(String tid, UUID sessionIndex, String realIp, String evseNo) {
                // 这里可以记录连接信息到数据库或日志系统
                log.info("[{} {}]记录连接信息 - 会话: {}, IP: {}, 设备: {}", tid, evseNo, sessionIndex, realIp, evseNo);
            }
        };
    }
}
