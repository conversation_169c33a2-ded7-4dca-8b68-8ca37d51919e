package com.cdz360.iot.gw.config.ws.profile;

import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp;
import com.cdz360.iot.gw.model.type.ChargePointModel;
import com.cdz360.iot.gw.model.type.NetType;
import com.cdz360.iot.gw.south.server.IotEvseProcessor;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import eu.chargetime.ocpp.ProtocolVersion;
import eu.chargetime.ocpp.feature.profile.ServerCoreEventHandler;
import eu.chargetime.ocpp.feature.profile.ServerCoreProfile;
import eu.chargetime.ocpp.model.core.AuthorizeConfirmation;
import eu.chargetime.ocpp.model.core.AuthorizeRequest;
import eu.chargetime.ocpp.model.core.BootNotificationConfirmation;
import eu.chargetime.ocpp.model.core.BootNotificationRequest;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.HeartbeatConfirmation;
import eu.chargetime.ocpp.model.core.HeartbeatRequest;
import eu.chargetime.ocpp.model.core.MeterValue;
import eu.chargetime.ocpp.model.core.MeterValuesConfirmation;
import eu.chargetime.ocpp.model.core.MeterValuesRequest;
import eu.chargetime.ocpp.model.core.StartTransactionConfirmation;
import eu.chargetime.ocpp.model.core.StartTransactionRequest;
import eu.chargetime.ocpp.model.core.StatusNotificationConfirmation;
import eu.chargetime.ocpp.model.core.StatusNotificationRequest;
import eu.chargetime.ocpp.model.core.StopTransactionConfirmation;
import eu.chargetime.ocpp.model.core.StopTransactionRequest;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class ServerCoreProfileConfig {

    @Autowired
    private IotEvseProcessor evseProcessor;

    @Autowired
    private EvseRepository evseRepository;

    @Bean
    public ServerCoreEventHandler getCoreEventHandler() {
        return new ServerCoreEventHandler() {
            @Override
            public AuthorizeConfirmation handleAuthorizeRequest(UUID sessionIndex,
                AuthorizeRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleAuthorizeRequest. request: {}", tid, evseNo, request);

                return (AuthorizeConfirmation) evseProcessor.authorizeProcess(
                    request); // returning null means unsupported feature
            }

            @Override
            public BootNotificationConfirmation handleBootNotificationRequest(UUID sessionIndex,
                BootNotificationRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleBootNotificationRequest. request: {}", tid, evseNo,
                    request);

                EvseRegisterUp msg = new EvseRegisterUp(request.getBase());
                msg.setProtocolVersion(ProtocolVersion.OCPP1_6.getVersion()); // 协议版本号

                ChargePointModel model = ChargePointModel.parseOf(request.getChargePointModel());
                msg.setSupplyType(model.getType());
                msg.setNetType(NetType.ETHERNET);// TODO: 2024/11/22 WZFIX 待确认网络接入方式
                msg.setIccid(request.getIccid());
                msg.setImsi(request.getImsi());
                Optional.ofNullable(request.getFirmwareVersion()).map(String::toUpperCase)
                    .ifPresent(msg::setFirmwareVersion);
                return (BootNotificationConfirmation) evseProcessor.process(
                    msg); // returning null means unsupported feature
            }

            @Override
            public DataTransferConfirmation handleDataTransferRequest(UUID sessionIndex,
                DataTransferRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleDataTransferRequest. request: {}", tid, evseNo, request);
                return (DataTransferConfirmation) evseProcessor.dataTransferProcess(
                    request); // returning null means unsupported feature
            }

            @Override
            public HeartbeatConfirmation handleHeartbeatRequest(UUID sessionIndex,
                HeartbeatRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleHeartbeatRequest. request: {}", tid, evseNo, request);
                return evseProcessor.heartbeatProcess(request);
            }

            @Override
            public MeterValuesConfirmation handleMeterValuesRequest(UUID sessionIndex,
                MeterValuesRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleMeterValuesRequest. request: {}", tid, evseNo, request);

                if (request.getMeterValue() != null && request.getMeterValue().length > 0) {
                    for (MeterValue mv : request.getMeterValue()) {
                        mv.setTimestamp(convertTimeIfNeeded(evseNo, mv.getTimestamp()));
                    }
                }

                return (MeterValuesConfirmation) evseProcessor.meterProcess(
                    request); // returning null means unsupported feature
            }

            @Override
            public StartTransactionConfirmation handleStartTransactionRequest(UUID sessionIndex,
                StartTransactionRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleStartTransactionRequest. request: {}", tid, evseNo,
                    request);

                // 使用统一方法处理时间
                request.setTimestamp(convertTimeIfNeeded(evseNo, request.getTimestamp()));

                return (StartTransactionConfirmation) evseProcessor.startTransProcess(
                    request); // returning null means unsupported feature
            }

            @Override
            public StatusNotificationConfirmation handleStatusNotificationRequest(UUID sessionIndex,
                StatusNotificationRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleStatusNotificationRequest. request: {}", tid, evseNo,
                    request);

                // 使用统一方法处理时间
                request.setTimestamp(convertTimeIfNeeded(evseNo, request.getTimestamp()));

                return (StatusNotificationConfirmation) evseProcessor.statusNotificationProcess(
                    request); // returning null means unsupported feature
            }

            @Override
            public StopTransactionConfirmation handleStopTransactionRequest(UUID sessionIndex,
                StopTransactionRequest request) {
                String tid = request.getBase().getTid();
                String evseNo = request.getBase().getEvseNo();
                log.info("[{} {}] handleStopTransactionRequest. request: {}", tid, evseNo, request);

                // 使用统一方法处理时间
                request.setTimestamp(convertTimeIfNeeded(evseNo, request.getTimestamp()));
                if (request.getTransactionData() != null
                    && request.getTransactionData().length > 0) {
                    for (MeterValue mv : request.getTransactionData()) {
                        mv.setTimestamp(convertTimeIfNeeded(evseNo, mv.getTimestamp()));
                    }
                }

                return (StopTransactionConfirmation) evseProcessor.stopChargeProcess(
                    request); // returning null means unsupported feature
            }
        };
    }

    /**
     * 统一处理时间转换
     *
     * @param evseNo   充电桩编号
     * @param dateTime 需要转换的时间
     * @return 转换后的时间
     */
    private ZonedDateTime convertTimeIfNeeded(String evseNo, ZonedDateTime dateTime) {
        GwEvseVo evse = evseRepository.getEvse(evseNo);
        return OcppZonedDateTime.fromUtcTime(dateTime, evse.getBrand(), evse.getTimeZone());
    }

    @Bean
    public ServerCoreProfile createCore(ServerCoreEventHandler serverCoreEventHandler) {
        return new ServerCoreProfile(serverCoreEventHandler);
    }
}
