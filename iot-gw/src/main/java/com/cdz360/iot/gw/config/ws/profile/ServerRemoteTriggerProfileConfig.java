package com.cdz360.iot.gw.config.ws.profile;

import eu.chargetime.ocpp.feature.profile.ServerRemoteTriggerProfile;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class ServerRemoteTriggerProfileConfig {

//    @Autowired
//    private IotEvseProcessor evseProcessor;

    @Bean
    public ServerRemoteTriggerProfile createServerRemoteTrigger() {
        return new ServerRemoteTriggerProfile();
    }
}
