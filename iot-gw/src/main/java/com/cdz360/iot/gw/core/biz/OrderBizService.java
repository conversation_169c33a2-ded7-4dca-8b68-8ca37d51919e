package com.cdz360.iot.gw.core.biz;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.SequenceCache;
import com.cdz360.iot.gw.model.base.CloudCommonResponse;
import com.cdz360.iot.gw.model.evse.protocol.Charge4RemoteRes;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStopDown;
import com.cdz360.iot.gw.model.type.ChargeEvseStatus;
import com.cdz360.iot.gw.north.model.ChargeStartData;
import com.cdz360.iot.gw.north.model.ChargeStartDetail;
import com.cdz360.iot.gw.north.model.GetConfig;
import com.cdz360.iot.gw.north.model.OrderCreateData;
import com.cdz360.iot.gw.north.model.OrderCreateResponse;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderResponse;
import com.cdz360.iot.gw.north.model.OrderStartData;
import com.cdz360.iot.gw.north.model.OrderStartingData;
import com.cdz360.iot.gw.north.model.OrderStopData;
import com.cdz360.iot.gw.north.mq.model.MqStartChargeData;
import com.cdz360.iot.gw.north.server.ConfigService;
import com.cdz360.iot.gw.north.server.NorthOrderService;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.util.PriceUtil;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OrderBizService {

    @Autowired
    private NorthOrderService northOrderService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private SequenceCache sequenceCache;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Value("${cdz360.timer.charge-idle-timeout:60000}")
    private long chargeIdleTimeout;

    /**
     * 云端创建充电订单
     */
    public void createOrderFromCloud(String tid, MqStartChargeData mqStartChargeData) {
        log.info("[{}] 开启订单。 data: {}.", tid, mqStartChargeData);

        ChargeStartData data = mqStartChargeData.getChargeStartData();

        if (data == null) {
            log.info("[{}] 开启充电时平台未响应。 response is null.", tid);
            throw new IllegalArgumentException("开启充电时平台未响应");
        }

        if (StringUtils.isEmpty(data.getOrderNo()) || data.getOrderNo().length() > 32) {
            log.info("[{}] 平台生成的订单号非法。orderNo: {}", tid, data.getOrderNo());
            throw new IllegalArgumentException("订单号非法");
        }

//        String key = MessageFormat.format("{0}-{1}-{2}", data.getEvseNo(), data.getPlugId(), mqStartChargeData.getSeqNoToEvse());
        sequenceCache.put(data.getOrderNo(), data.getOrderNo());
        log.info("[{}] 缓存订单号。 orderNo: {}.", tid, data.getOrderNo());

        EvseMsgBase base = new EvseMsgBase();
        base.setEvseNo(data.getEvseNo())
            .setPlugIdx(data.getPlugId())
            .setTid(tid)
            .setCmdCode(EvseMessageType.CHARGE_FROM_CLOUD)
            .setSeq(mqStartChargeData.getSeqNoToEvse());
        Charge4RemoteRes dMsg = new Charge4RemoteRes(base);
        dMsg.setOrderNo(data.getOrderNo())
            .setOrderStartType(data.getStartType())
            .setChargeMode(data.getStopMode().getType())
            .setAccount(data.getAccountNo())
            .setBalance(data.getTotalAmount())
            .setAmount(data.getFrozenAmount())
            .setStopCode(data.getStopCode())
            .setCarNo(data.getCarNo())
//                .setPower(data.getPower())
            .setSoc(data.getSoc())
            .setPrice(data.getPrice())
        ;

        boolean result = jsonServer.sendRemoteStartTrans(dMsg);

        if (!result) {
            log.error("[{}] 云端开启充电失败，下发桩数据时发生错误。", tid);
            reportOrderStartResult(tid, data, ChargeEvseStatus.EVSE_OFFLINE, data.getOrderNo());
        }

        jsonServer.sendRcdEvsePriceByConditions(base, false, data.getPrice());

        Timer timer = new Timer("Timer-Charge");
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (sequenceCache.containsKey(data.getOrderNo())) {
                    reportOrderStartResult(tid, data, ChargeEvseStatus.EVSE_NOT_RESPONSE,
                        data.getOrderNo());
                }
                timer.cancel();
            }
        }, chargeIdleTimeout);

    }


    public void reportOrderStartResult(String tid, ChargeStartData req, int evseStatus,
        String key) {
        try {

            log.info("[{}] 上报订单状态。 req: {}, evseStatus: {}, seqNo: {}", tid, req, evseStatus,
                key);

            String seqNo = SeqGeneratorUtil.newStringId();

            ChargeStartDetail chargeStartDetail = new ChargeStartDetail();
            chargeStartDetail.setEvseNo(req.getEvseNo());
            chargeStartDetail.setPlugId(req.getPlugId());
            chargeStartDetail.setOrderNo(req.getOrderNo());
            chargeStartDetail.setResult(evseStatus);

            OrderStartingData orderStartingRequest = new OrderStartingData();
            orderStartingRequest.setSeq(seqNo);
            orderStartingRequest.setCmd(IotGwCmdType2.CE_CHARGE_START);
            orderStartingRequest.setStatus(evseStatus);
            orderStartingRequest.setMsg("");
            orderStartingRequest.setDetail(chargeStartDetail);

            northOrderService.startResult(tid, req.getEvseNo(), req.getPlugId(),
                orderStartingRequest).subscribe();

            sequenceCache.remove(key);

        } catch (Exception ex) {
            log.error("[{}] 上报订单启动失败. error: {}", tid, ex.getMessage(), ex);
        }
    }


    /**
     * 桩端创建订单
     */
    public Mono<OrderCreateResponse> createOrderFromEvse(String tid, OrderCreateData data) {

        Mono<Boolean> booleanTodo = Mono.just(Boolean.TRUE); // TODO: 2021/9/14 创建订单前检查是否有剩余功率可供分配
        return booleanTodo
            .filter(Boolean.TRUE::equals)
            .flatMap(e -> northOrderService.createOrder(tid, data))
            .switchIfEmpty(Mono.just("无剩余功率可供分配，创建订单失败").map(i -> {
                log.warn(i);
                OrderCreateResponse res = new OrderCreateResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_AUTH_FAIL_SITE_POWER_LIMIT_REACHED);
                return res;
            }));
    }

    /**
     * 桩端上报充电开始
     *
     * @return
     */
    public Mono<OrderResponse> orderStarted(String tid, OrderStartData data) {

        return northOrderService.startOrder(tid, data);
    }


    /**
     * 桩上报充电结束
     *
     * @param tid
     * @param data
     * @return
     */
    public Mono<OrderResponse> orderStopped(String tid, OrderStopData data) {

        return northOrderService.stopOrder(tid, data);
    }


    public Mono<OrderStopData> checkOrderFee(String tid, OrderStopData dataIn) {
        if (dataIn.getKwh() != null
            && DecimalUtils.gt(dataIn.getKwh(), BigDecimal.ONE)
            && (dataIn.getElecFee() == null || DecimalUtils.isZero(dataIn.getElecFee()))
            && (dataIn.getServFee() == null || DecimalUtils.isZero(dataIn.getServFee()))
            && dataIn.getEvseNo().startsWith("57")  // 循道的桩
        ) {
            // 电量超过1kwh, 但是金额为0, 循道桩存在计费问题, 需要重新从云端获取计费信息, 然后重新计算
        } else {
            return Mono.just(dataIn);
        }
        GetConfig config = new GetConfig();
        config.setEvseNo(dataIn.getEvseNo());
        // 当前异常的订单数量不多,暂时不考虑将计费信息缓存到网关本地
        return configService.getPriceCfg(tid, config)
            .map(priceRes -> this.accountFee(tid, dataIn, priceRes.getData()));
    }


    private OrderStopData accountFee(String tid, OrderStopData orderIn, ChargePriceVo priceCfg) {
        log.debug("[{}] 桩的计费模板信息 evseNo = {}, price = {}", tid, orderIn.getEvseNo(),
            priceCfg);
        BigDecimal elecFee = BigDecimal.ZERO;
        BigDecimal servFee = BigDecimal.ZERO;
        for (var dt : orderIn.getDetail()) {
            if (priceCfg == null || CollectionUtils.isEmpty(
                priceCfg.getItemList())) {
                log.warn("[{}] 缺少计费模板信息，不做金额修正 evseNo = {}, price = {}", tid,
                    orderIn.getEvseNo(),
                    priceCfg);
                return orderIn;
            } else if (dt.getKwh() == null) {
                continue;
            }
            long dtStart = dt.getStartTime() / 60 % (24 * 60) + 8 * 60; // 转换成当天的分钟数， +8小时
            long dtEnd = dt.getStopTime() / 60 % (24 * 60) + 8 * 60; // 转换成当天的分钟数， +8小时
//            boolean nextDay = dtStart > dtEnd;  // 是否跨天
            for (var pi : priceCfg.getItemList()) {

                long piStart = toMinutes(pi.getStartTime());
                long piEnd = toMinutes(pi.getEndTime());
//                if (piStart <= dtStart && piEnd >= dtEnd) {
                // 注: 此处仅使用订单时段开始时间,原因为循道桩上报的时段不正确,会跨越多个时段. 吴江地区交流桩为单一价,可使用该补救方式,后续如果使用峰平谷价格则不再适用
                if (dtStart >= piStart && dtStart < piEnd) {
                    log.info(
                        "[{}] 分时匹配成功. 订单号 = {}, 订单分时 {} - {}, 计费模板分时 {} -{}, 电费 = {}, 服务费 = {}, evseNo = {}",
                        tid, orderIn.getOrderNo(), dtStart, dtEnd, piStart, piEnd,
                        pi.getElecPrice(), pi.getServPrice(), orderIn.getEvseNo());

                    orderIn.setPriceCode(priceCfg.getId());
                    if (pi.getElecPrice() == null) {
                        log.warn("[{}] 计费模板配置有问题，电费单价为null 。 evseNo = {}, price = {}",
                            tid, orderIn.getEvseNo(), priceCfg);
                        continue;
                    } else {
                        dt.setElecPrice(pi.getElecPrice());
                        dt.setElecFee(dt.getKwh().multiply(pi.getElecPrice())
                            .setScale(2, RoundingMode.HALF_UP));
                    }
                    if (pi.getServPrice() == null) {
                        log.warn(
                            "[{}] 计费模板配置有问题，服务费单价为null 。 evseNo = {}, price = {}",
                            tid, orderIn.getEvseNo(), priceCfg);
                        continue;
                    } else {
                        dt.setServPrice(pi.getServPrice());
                        dt.setServFee(dt.getKwh().multiply(pi.getServPrice())
                            .setScale(2, RoundingMode.HALF_UP));
                    }
                }
            }
            elecFee = elecFee.add(dt.getElecFee());
            servFee = servFee.add(dt.getServFee());
        }
        orderIn.setElecFee(elecFee);
        orderIn.setServFee(servFee);
        log.info("[{}] 修正后订单 {} 详情 = {}", tid, orderIn.getOrderNo(), orderIn.getDetail());
        return orderIn;
    }

    private long toMinutes(String strTime) {
        try {
//            String[] toks = strTime.split(":");
            String strStart = strTime.substring(0, 2);
            String strEnd = strTime.substring(2, 4);
            return Long.valueOf(strStart) * 60 + Long.valueOf(strEnd);
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 云端停止充电订单
     */
    public void stopOrderFromCloud(ChargeStopDown dMsg) {
        String tid = dMsg.getBase().getTid();
        String orderNo = dMsg.getOrderNo();

        if (StringUtils.isEmpty(orderNo) || orderNo.length() > 32) {
            log.info("[{}] 平台生成的订单号非法。orderNo: {}", tid, orderNo);
            throw new IllegalArgumentException("订单号非法");
        }

        OrderData orderData = evseOrderRepository.getOrder(orderNo);
        if (orderData == null) {
            throw new DcServiceException("订单不存在：" + orderNo);
        }

        boolean result = jsonServer.sendRemoteStopTrans(dMsg, orderData.getTransId());

        if (!result) {
            log.error("[{}] 云端停止充电失败，下发桩数据时发生错误。", tid);
        }

    }

    /**
     * 从云端获取桩已绑定的计费信息
     *
     * @param tid
     * @param evseNo
     * @return
     */
    public Mono<ChargePriceVo> fetchEvseCloudPrice(String tid, String evseNo) {
        GetConfig config = new GetConfig();
        config.setEvseNo(evseNo);
        return configService.getPriceCfg(tid, config)
            .map(CloudCommonResponse::getData)
            .map(priceVo -> {

                List<ChargePriceItem> collect = priceVo.getItemList().stream().map(pi -> {
                    pi.setStartTime(PriceUtil.formatTimeString(pi.getStartTime()))
                        .setEndTime(PriceUtil.formatTimeString(pi.getEndTime()));
                    return pi;
                }).collect(Collectors.toList());
                priceVo.setItemList(collect);
                return priceVo;
            });
    }

}
