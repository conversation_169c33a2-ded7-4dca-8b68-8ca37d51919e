package com.cdz360.iot.gw.core.biz;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderNoGenerator {

    private static final Random RANDOM = new Random();

    private static final String KEY_REDIS_ORDER_NO_IDX = "ocpp:order:idx";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 生成20位订单号 格式：
     * <p>前2位: 固定前缀(42)</p>
     * <p>接下来10位: 年月日时分</p>
     * <p>接下来4位: 序列号</p>
     * <p>最后4位: 随机字母数字组合</p>
     */
    public Pair<String, Integer> generateOrderNumber() {
        synchronized (this) {
            StringBuilder sb = new StringBuilder();

            // 1. 添加固定前缀
            sb.append("42");

            // 2. 添加日期（年月日时分）
            LocalDateTime now = LocalDateTime.now();
            String dateStr = now.format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
            sb.append(dateStr);

            // 3. 添加4位序列号
            Long suffix = stringRedisTemplate.opsForValue().increment(KEY_REDIS_ORDER_NO_IDX, 1L);
            String sequence = String.format("%04d", suffix % 9999);
            sb.append(sequence);

            // 4. 添加4位随机字母数字组合
            String randomEnd = generateRandomHex(4);
            sb.append(randomEnd);

            String orderNo = sb.toString();
            Integer transId = Integer.valueOf(orderNo.substring(7, 16)); // "dHHmm" + 4位序列号
            return Pair.of(orderNo, transId);
        }
    }

    /**
     * "dHHmm" + 4位序列号 能满足Integer的类型限制
     *
     * @param orderNo
     * @return
     */
    public Integer orderNo2TransId(String orderNo) {
        String str = orderNo.substring(5); // "dHHmm" + 4位序列号
        return Integer.valueOf(str);
    }

    /**
     * 生成指定长度的随机十六进制字符串
     */
    private String generateRandomHex(int length) {
        StringBuilder sb = new StringBuilder();
//        String chars = "0123456789ABCDEF";
        String chars = "0123456789";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(RANDOM.nextInt(chars.length())));
        }
        return sb.toString();
    }

}
