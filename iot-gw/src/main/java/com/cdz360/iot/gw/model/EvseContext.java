package com.cdz360.iot.gw.model;

import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class EvseContext {

    private String traceId;

    private String evseNo;

    private int plugIdx;

    /**
     * 桩端消息
     */
    private BaseEvseMsgUp evseMsg;

    /**
     * 桩缓存数据结构
     */
    private GwEvseVo evse;



}
