package com.cdz360.iot.gw.model;


import com.cdz360.base.utils.JsonUtils;

public class EvseSeq {

    private String mixId;// evseId # plugId
    private int seq;
    private long revTime;//接收到的时间

    private long lostTime;//丢失的时间


    public EvseSeq() {
    }

    public EvseSeq(String mixId, int seq, long revTime) {
        this.mixId = mixId;
        this.seq = seq;
        this.revTime = revTime;
    }

    public EvseSeq(String mixId, int seq) {
        this.mixId = mixId;
        this.seq = seq;
    }

    public String getMixId() {
        return mixId;
    }

    public void setMixId(String mixId) {
        this.mixId = mixId;
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public long getRevTime() {
        return revTime;
    }

    public void setRevTime(long revTime) {
        this.revTime = revTime;
    }

    public long getLostTime() {
        return lostTime;
    }

    public void setLostTime(long lostTime) {
        this.lostTime = lostTime;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
