package com.cdz360.iot.gw.model;

import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;

public class GwConfig implements Serializable {

    private String key;
    private String value;
    private String valueType;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public int getIntValue() {
        if (!"INT".equalsIgnoreCase(this.getValueType())) {
            throw new RuntimeException("此配置项的类型不是INT类型");
        }

        return Integer.parseInt(this.getValue());
    }

    public long getLongValue() {
        if (!"LONG".equalsIgnoreCase(this.getValueType())) {
            throw new RuntimeException("此配置项的类型不是LONG类型");
        }

        return Long.parseLong(this.getValue());
    }

    public String getStringValue() {
        if (!"STRING".equalsIgnoreCase(this.getValueType())) {
            throw new RuntimeException("此配置项的类型不是STRING类型");
        }

        return this.getValue();
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
