package com.cdz360.iot.gw.model;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.model.type.NetType;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Nullable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GwEvseVo extends EvseVo {

    // 是否在启动中: true -- 启动中; false -- 启动完成
    // 这里的判断相对于注册报文来说的，注册后为 true, 全部枪心跳上报后为 false
    private Boolean booting = Boolean.TRUE;

    // 品牌
    @Nullable
    private EvseBrand brand;

    /**
     * 时区. 例如 +8 或 +02:00
     */
    @Nullable
    private String timeZone;

    // 网络类型. 网线: ETHENET, wifi: WIFI
    private NetType net;

    // 是否支持 VIN 充电
    private Boolean supportVin;

    // 桩异常问题描述
    private String error;

    private ChargePriceVo price;

//    // 故障码
//    private Integer errorCode = 0;

    // 枪头信息列表
    private List<GwPlugVo> plugs = new ArrayList<>();

    public boolean opInfoValidate() {
        return brand != null && this.getSupplyType() != null && this.getPlugNum() != null
            && StringUtils.isNotBlank(timeZone) && price != null && CollectionUtils.isNotEmpty(
            price.getItemList());
    }

    public static EvseVo toEvseVo(GwEvseVo vo) {
        if (null == vo) {
            return null;
        }

        EvseVo result = new EvseVo();
        result.setEvseNo(vo.getEvseNo())
            .setGwno(vo.getGwno())
            .setStatus(vo.getStatus())
            .setPlugNum(vo.getPlugNum())
            .setSupplyType(vo.getSupplyType())
            .setProtocolVer(vo.getProtocolVer())
            .setTemperature(vo.getTemperature())
            .setErrorCode(vo.getErrorCode())
            .setAlertCode(vo.getAlertCode())
            .setUpdateTime(vo.getUpdateTime());

        return result;
    }

    public static GwEvseVo toGwEvseVo(EvseVo vo) {
        if (null == vo) {
            return null;
        }

        GwEvseVo result = new GwEvseVo();
        BeanUtils.copyProperties(vo, result);
        return result;
    }

    public GwEvseVo updateExtProperty(GwEvseVo srcVo) {
        if (null != srcVo.getBooting() &&
            srcVo.getBooting() ^ this.booting) {
            this.booting = srcVo.getBooting();
        }

        if (srcVo.getNet() != null && srcVo.getNet() != this.net) {
            this.net = srcVo.getNet();
        }

        if (null != srcVo.getSupportVin() &&
            srcVo.getSupportVin() ^ this.supportVin) {
            this.supportVin = srcVo.getSupportVin();
        }

        if (StringUtils.isNotBlank(srcVo.getError()) &&
            !srcVo.getError().equals(this.error)) {
            this.error = srcVo.getError();
        }

        if (null != srcVo.getErrorCode() &&
            srcVo.getErrorCode().equals(this.getErrorCode())) {
            this.setErrorCode(srcVo.getErrorCode());
        }

        // 这里做覆盖处理: 存在问题，如果更新的数据源不正确则会造成BUG
        if (CollectionUtils.isNotEmpty(srcVo.getPlugs())) {
            this.plugs = srcVo.getPlugs();
        }

        return this;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
