package com.cdz360.iot.gw.model;

import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;
import java.util.Date;

/**
 * 网关持久实体
 */
public class GwInfo implements Serializable {

    private static final long serialVersionUID = -6643709967237452451L;

    private boolean cancel;
    private int version;
    private String gwno;
    private String passcode;
    private Date createTime;
    private Date updateTime;

    public boolean isCancel() {
        return cancel;
    }

    public void setCancel(boolean cancel) {
        this.cancel = cancel;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getGwno() {
        return gwno;
    }

    public void setGwno(String gwno) {
        this.gwno = gwno;
    }

    public String getPasscode() {
        return passcode;
    }

    public void setPasscode(String passcode) {
        this.passcode = passcode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
