package com.cdz360.iot.gw.model;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.PlugNoUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.model.converter.ErrorCodeConverter;
import com.cdz360.iot.gw.model.evse.ErrorCodeMsg;
import com.cdz360.iot.gw.model.evse.protocol.EvseHbMsgUp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GwPlugVo extends PlugVo {

    // 枪状态值: 报文上传值
    private Integer msgStatus;

    // 心跳序列号
    private Long lastHbSeq;

    public static PlugVo toPlugVo(GwPlugVo vo) {
        if (null == vo) {
            return null;
        }

        PlugVo result = new PlugVo();
        result.setGwno(vo.getGwno()).setEvseNo(vo.getEvseNo()).setPlugNo(vo.getPlugNo())
            .setIdx(vo.getIdx()).setStatus(vo.getStatus()).setSupply(vo.getSupply())
            // .setOrderNo(vo.getOrderNo())
            .setTemperature(vo.getTemperature()).setErrorCode(vo.getErrorCode())
            .setErrorMsg(vo.getErrorMsg()).setAlertCode(vo.getAlertCode())
            .setMinVoltage(vo.getMinVoltage()).setMaxVoltage(vo.getMaxVoltage())
            .setMinCurrent(vo.getMinCurrent()).setMaxCurrent(vo.getMaxCurrent())
            .setUpdateTime(vo.getUpdateTime()).setPower(vo.getPower());
        //BeanUtils.copyProperties(vo, result);
        if ((StringUtils.isBlank(result.getPlugNo()) || StringUtils.equals(result.getPlugNo(),
            "null")) && result.getIdx() != null) {
            result.setPlugNo(PlugNoUtils.formatPlugNo(result.getEvseNo(), result.getIdx()));
        }
        return result;
    }

    public static GwPlugVo toGwPlugVo(PlugVo vo) {

        if (null == vo) {
            return null;
        }

        GwPlugVo result = new GwPlugVo();
        BeanUtils.copyProperties(vo, result);
        return result;
    }

    public static GwPlugVo toGwPlugVo(SupplyType type, EvseHbMsgUp hb) {
        GwPlugVo plugVo = new GwPlugVo();

        plugVo.setMsgStatus(Byte.valueOf(hb.getPlugStatus().getCode()).intValue())
            .setLastHbSeq(hb.getBase().getSeq()).setStatus(hb.getPlugStatus().getCloudStatus())
            .setIdx(hb.getBase().getPlugIdx()).setErrorCode(hb.getErrorCode())//故障码
            .setAlertCode(hb.getAlertCode())//告警码
//                .setTemperature(hb.getPlugTemp())
            .setEvseNo(hb.getBase().getEvseNo());

        if (NumberUtils.gtZero(plugVo.getErrorCode())) {
            ErrorCodeMsg convert = ErrorCodeConverter.convert(type, plugVo.getErrorCode());
            plugVo.setErrorMsg(convert.getMsg()).setErrorCode(convert.getCode());
        } else {
            plugVo.setErrorMsg("");
        }

        return plugVo;
    }

    public GwPlugVo updateExtProperty(GwPlugVo srcVo) {
        if (null != srcVo.msgStatus && srcVo.msgStatus.equals(this.msgStatus)) {
            this.msgStatus = srcVo.getErrorCode();
        }
        return this;
    }

    public void overrideProperty(GwPlugVo srcVo) {
        BeanUtils.copyProperties(this, srcVo);
    }

    public GwPlugVo copy() {
        GwPlugVo newVo = new GwPlugVo();
        BeanUtils.copyProperties(this, newVo);
        return newVo;
    }

}