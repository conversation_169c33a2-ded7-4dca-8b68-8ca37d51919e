package com.cdz360.iot.gw.model;

/**
 * 网关和桩间通信消息的基础类
 */
public class IotEvseMessage {

    private String channelKey;
    private String evseIp;
    //private EvseMessageType code;

    public String getChannelKey() {
        return channelKey;
    }

    public void setChannelKey(String channelKey) {
        this.channelKey = channelKey;
        //return this;
    }



    public String getEvseIp() {
        return evseIp;
    }

    public IotEvseMessage setEvseIp(String evseIp) {
        this.evseIp = evseIp;
        return this;
    }

//    public EvseMessageType getCode() {
//        return code;
//    }
//
//    public IotEvseMessage setCode(EvseMessageType code) {
//        this.code = code;
//        return this;
//    }
}
