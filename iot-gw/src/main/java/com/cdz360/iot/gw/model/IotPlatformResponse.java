package com.cdz360.iot.gw.model;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IotPlatformResponse<T> extends BaseParam {
    // 0 表示成功; 其他都表示失败
    private int status;

    private boolean success;
    private String msg;

    private T data;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
