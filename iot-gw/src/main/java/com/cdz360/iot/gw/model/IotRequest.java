package com.cdz360.iot.gw.model;

import com.cdz360.iot.gw.util.ByteUtil;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 网关和桩间通信的请求消息[桩上行]
 */
public class IotRequest extends IotEvseMessage {

    //private final Logger logger = LoggerFactory.getLogger(IotRequest.class);

    private byte[] fullMsg;//报文的全部字节
    private EvseMsgBase baseMsg;    // 通用头部
    //   private byte header;//帧头
    //private byte flag;//控制位
//    private byte[] length = new byte[2];//报文长度
//    private byte[] sign = new byte[4];//报文的签名

    //private BaseDecoder decoder;
    private long decodeTimestamp;//收到报文的开始时间
    private long readTimestamp;//请求开始处理的时间

    private String traceId;//标志一次请求的数据流转过过程。对每个单独的报文请求唯一

    public IotRequest(EvseMsgBase commonMsg, byte[] fullMsg) {
        this.fullMsg = fullMsg;
        //       this.header = fullMsg[0];
        this.baseMsg = commonMsg;
        //this.flag = fullMsg[1];
        //super.setCode(EvseMessageType.codeOf(this.flag));// 设置code
        //this.decoder = getDecoder();
        //this.baseMsg = BaseDecoder.parse(fullMsg);
    }

    public byte[] getFullMsg() {
        return fullMsg;
    }

    public EvseMsgBase getBaseMsg() {
        return this.baseMsg;
    }

    public void setBaseMsg(EvseMsgBase baseMsg) {
        this.baseMsg = baseMsg;
    }

//    public byte getHeader() {
//        return header;
//    }
//
//    public byte getFlag() {
//        return flag;
//    }

    //桩号
    public String getEvseNo() {
        return this.baseMsg.getEvseNo();
    }



    // 枪号
    public byte getPlugNo() {

        return getFullMsg()[8];
    }


    //报文序列号
    public int getSeqNo() {
        byte[] net = new byte[4];
        System.arraycopy(getFullMsg(), 9, net, 0, 4);

        return ByteBuffer.wrap(net).order(ByteOrder.LITTLE_ENDIAN).getInt();
    }

    //报文时间戳
    public int getTimeStamp() {
        byte[] net = new byte[4];
        System.arraycopy(getFullMsg(), 13, net, 0, 4);
        return ByteBuffer.wrap(net).order(ByteOrder.LITTLE_ENDIAN).getInt();
    }


    public byte[] getBytesWithoutSign() {
        byte[] clientBytes = new byte[fullMsg.length - 4];
        System.arraycopy(fullMsg, 0, clientBytes, 0, clientBytes.length);
        return clientBytes;
    }



    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public long getDecodeTimestamp() {
        return decodeTimestamp;
    }

    public void setDecodeTimestamp(long decodeTimestamp) {
        this.decodeTimestamp = decodeTimestamp;
    }

    public long getReadTimestamp() {
        return readTimestamp;
    }

    public void setReadTimestamp(long readTimestamp) {
        this.readTimestamp = readTimestamp;
    }



    /**
     * 支持序列号按枪唯一
     *
     * @return
     */
    public String getSeqNo4Plug() {
        StringBuilder buf = new StringBuilder();
        buf.append(super.getChannelKey())
                .append("-")
                .append(this.getSeqNo()).append("-")
                .append(Byte.toUnsignedInt(this.getPlugNo())).append("-")
                .append(ByteUtil.byteToHexStr(this.baseMsg.getCmdCode().getCode()));
        return buf.toString();
    }

    @Override
    public String toString() {
        return ByteUtil.byteArrayToHexStr(getFullMsg());
    }
}
