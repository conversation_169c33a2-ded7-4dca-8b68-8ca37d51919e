package com.cdz360.iot.gw.model;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * SSH 反向通道关闭信息
 */
public class SSHReverseClose implements Serializable {
    private static final long serialVersionUID = -4188642252839553236L;

    private String flag; // RemoteAddress + ":" + RemotePort
    private Integer pid; // 执行 ssh 反向通道返回的进程 id
    private Integer expire; // 时效
    private String createTime; // 创建时间
    private String updateTime; // 更新时间

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(updateTime);
        } catch (ParseException e) {
            return null;
        }
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SSHReverseClose{" +
                "flag='" + flag + '\'' +
                ", pid=" + pid +
                ", expire=" + expire +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
