package com.cdz360.iot.gw.model.base;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BasePlatformPackage {

    @JsonProperty(value = "v")
    private Integer ver;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String seq;


    @JsonProperty(value = "n")
    private String gwno;

    @JsonProperty(required = false)
    private IotPackageType type;

    public BasePlatformPackage() {

    }

    public BasePlatformPackage(IotPackageType type) {
        this.type = type;
    }

    public BasePlatformPackage(IotPackageType type, String seq) {
        this.type = type;
        this.seq = seq;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
