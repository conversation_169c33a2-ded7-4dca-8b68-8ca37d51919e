package com.cdz360.iot.gw.model.base;

import com.fasterxml.jackson.annotation.JsonInclude;

public class BasePlatformResponse extends BasePlatformPackage {

    private int status = 0;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public String error = null;

    public static BasePlatformResponse newInstance() {
        return new BasePlatformResponse();
    }

    public BasePlatformResponse() {
        super(IotPackageType.RES);
    }

    public BasePlatformResponse(String seq) {
        super(IotPackageType.RES, seq);
    }

    public BasePlatformResponse(int status, String seq, String error) {
        super(IotPackageType.RES, seq);
        this.status = status;
        this.error = error;
    }

    public int getStatus() {
        return status;
    }

    public BasePlatformResponse setStatus(int status) {
        this.status = status;
        return this;
    }

    public String getError() {
        return error;
    }

    public BasePlatformResponse setError(String error) {
        this.error = error;
        return this;
    }
}
