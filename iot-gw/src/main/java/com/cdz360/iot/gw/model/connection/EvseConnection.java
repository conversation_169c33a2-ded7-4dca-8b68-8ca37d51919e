package com.cdz360.iot.gw.model.connection;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

@Data
public class EvseConnection {

    private String clientAddress;

    private String evseNo;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date registerTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date heartbeatTime;

    public EvseConnection() {
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
