package com.cdz360.iot.gw.model.connection;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;
import lombok.Data;

@Data
public class PlugConnection {

    private String evseId;
    private int plugNo;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date registerTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date heartbeatTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date lastReportTime;

    private AtomicLong seqNo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
