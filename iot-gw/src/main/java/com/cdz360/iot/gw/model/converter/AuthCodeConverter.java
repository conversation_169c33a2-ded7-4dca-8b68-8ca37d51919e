package com.cdz360.iot.gw.model.converter;

import com.cdz360.base.model.base.constants.DcConstants;
import eu.chargetime.ocpp.model.core.AuthorizationStatus;

public class AuthCodeConverter {

    public static AuthorizationStatus convert(Integer status) {
        if (status == null) {
            return AuthorizationStatus.Invalid;
        }
        AuthorizationStatus result;
        // TODO: 2025/6/17 WZFIX 转换返回码
        switch (status) {
            case 0:
                result = AuthorizationStatus.Accepted;
                break;
            case DcConstants.KEY_RES_CODE_BALANCE_ERROR,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL_RESERVED,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_UNBOUND,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_NO_TEMPLATE,
                 DcConstants.KEY_RES_CODE_AUTH_FAIL_SITE_POWER_LIMIT_REACHED:
                result = AuthorizationStatus.Blocked;
                break;
            default:
                result = AuthorizationStatus.Invalid;
                break;
        }

        return result;
    }
}
