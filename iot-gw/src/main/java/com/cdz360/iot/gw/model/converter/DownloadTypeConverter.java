package com.cdz360.iot.gw.model.converter;

public class DownloadTypeConverter {
    public static byte convert(String downloadType) {

        byte result;
        switch (downloadType.toUpperCase()) {
            case "TFTP":
                result = (byte) 0x02;
                break;

            case "HTTP":
                result = (byte) 0x03;
                break;

            default:
                //默认都为FTP
                result = (byte) 0x01;
                break;
        }
        return result;
    }
}
