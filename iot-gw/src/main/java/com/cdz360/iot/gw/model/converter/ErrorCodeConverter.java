package com.cdz360.iot.gw.model.converter;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.iot.gw.model.evse.ErrorCodeMsg;
import eu.chargetime.ocpp.model.dc.type.DeviceStatusCodeType;

public class ErrorCodeConverter {

    public static ErrorCodeMsg convert(SupplyType type, Integer errorCode) {

        if (null == errorCode || errorCode == 0) {
            return new ErrorCodeMsg(0, "");
        }
        return new ErrorCodeMsg(errorCode, DeviceStatusCodeType.getDescByCode(errorCode));
    }

}
