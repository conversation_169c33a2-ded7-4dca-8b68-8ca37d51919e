package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

/**
 * 发送给云端的下行指令执行反馈
 */
@Data
public class CloudCmdResultReq {

    /**
     * 下行指令码
     */
    private IotGwCmdType2 cmd;

    /**
     * 下行指令的seq号
     */
    private String seq;

    /**
     * 指令执行结果. 0表示成功. 其他都表示失败
     */
    private int status;

    /**
     * 失败的文案
     */
    private String msg;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
