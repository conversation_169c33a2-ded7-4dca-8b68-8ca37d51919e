package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.EvseCfgGetResponse;
import lombok.Data;

@Data
public class CloudConfig {

    //获取的桩配置信息
    private EvseCfgGetResponse response;

    private String evseId;
    private String traceId;
    private String cfgVer;
    private byte[] longCipherBytes;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

