package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.CloudResultType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 桩远程重启结果
 */
@Data
@Accessors(chain = true)
public class CloudEvseRebootResult {
    private String evseNo;
    private String taskNo;
    private CloudResultType result;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
