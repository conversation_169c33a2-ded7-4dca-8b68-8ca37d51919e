package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.CloudResultType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 桩远程重启结果
 */
@Data
@Accessors(chain = true)
public class CloudEvseUpgradeResult {
    private String evseNo;
    private String taskNo;
    private CloudResultType result;
    /**
     * 失败的文案
     */
    private String msg;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
