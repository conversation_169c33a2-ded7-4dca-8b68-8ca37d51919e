package com.cdz360.iot.gw.model.evse;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Classname CloudSocCtrlResult
 * @Description soc限制结果反馈给云端
 * @Date 7/16/2021 5:11 PM
 * @Created by Rafael
 */
@Data
@Accessors(chain = true)
public class CloudSocCtrlResult {
    private String orderNo;
    private String seq;

    /**
     * 0: 成功
     * 其他: 失败
     */
    private Integer result;
}