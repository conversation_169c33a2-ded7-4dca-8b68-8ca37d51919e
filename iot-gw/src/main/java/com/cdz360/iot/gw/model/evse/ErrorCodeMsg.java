package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.utils.JsonUtils;

public class ErrorCodeMsg {

    private int code;
    private String msg;

    public ErrorCodeMsg(int code) {
        this.code = code;
    }

    public ErrorCodeMsg(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
