package com.cdz360.iot.gw.model.evse;

import com.cdz360.base.model.base.vo.BaseObject;

/**
 * 云端请求建立SSH反向通道
 */
public class TunnelRequest extends BaseObject {
    private String method = "TUNNEL";
    private String cmd;           // 指令类型: START, STOP
    private String remoteAddress; // 远端地址, IP或域名
    private Integer remotePort;   // 远端端口号
    private Integer localPort;    // 本地端口号, 默认为 22
    private Integer expire;       // 有效期, 单位分钟, 默认1小时. 过期后网关自动关闭通道

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getRemoteAddress() {
        return remoteAddress;
    }

    public void setRemoteAddress(String remoteAddress) {
        this.remoteAddress = remoteAddress;
    }

    public Integer getRemotePort() {
        return remotePort;
    }

    public void setRemotePort(Integer remotePort) {
        this.remotePort = remotePort;
    }

    public Integer getLocalPort() {
        return localPort;
    }

    public void setLocalPort(Integer localPort) {
        this.localPort = localPort;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }
}
