package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.WhiteCard;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CardQueryUp extends BaseEvseMsgUp {

    /**
     * 返回码
     */
    private int result;

    /**
     * 紧急充电卡个数
     */
    private int cardCount;

    private List<WhiteCard> whiteCardList;

    public CardQueryUp() {
        // default
    }

    public CardQueryUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
