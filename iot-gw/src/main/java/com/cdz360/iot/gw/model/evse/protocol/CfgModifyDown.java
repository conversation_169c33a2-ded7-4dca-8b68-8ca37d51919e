package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.Charge;
import com.cdz360.iot.gw.north.model.StopModeCfg;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 桩配置下发
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CfgModifyDown extends BaseEvseMsgDown{

    //长效密钥版本号
    private int evsePasscodeVer;

    //长效密钥
    private String evsePasscode;

    private byte[] longCipherBytes;

    // 不传为null，判断数组为空 CollectionUtils.isEmpty
    // startTime为null时，默认为00:00; stopTime 为null时默认为24:00

    //桩端管理员登录密码, 数字型, 6~8位. 不传表示不做修改
    // private String adminCode;
    private String adminCodeA;
    private String adminCodeB;

    // 国标协议
    private String bmsVer;



    //是否支持 VIN 码充电. 不传表示不做修改
    private Boolean vin;

    //桩端显示的二维码 URL. 不传表示不做修改
    private String qrUrl;

    // 均/轮充设置 0均充 1轮充
    private Integer avgOrTurnCharge;

    // 合充开关
    private Boolean combination;

    // 辅电手动切换开关
    private Boolean heating;

    // 辅电电压设置
    private Integer heatingVoltage;

    // 电池反接检测开关
    private Boolean batteryCheck;

    // 是否支持充电记录查询. 不传表示不做修改
    private Boolean queryChargeRecord;

    // 主动安全检测开关
    private Boolean securityCheck;

    // 不拔枪充电开关(二次充电)
    private Boolean constantCharge;

    // 插枪获取VIN开关
    private Boolean vinDiscover;

    // 订单信息隐私设置开关（null不做更改 true开启 false关闭）
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    private Integer accountDisplayType;

    // 是否支持扫码充电. 不传表示不做修改
    private Boolean qrCharge;

    // 是否支持刷卡充电. 不传表示不做修改
    private Boolean cardCharge;

    // 是否支持无卡充电. 不传表示不做修改
    private Boolean noCardCharge;

    // 是否支持定时充电. 不传表示不做修改
    private Boolean timedCharge;

    // 白天音量. 不传表示不做修改;
    private Integer dayVolume;

    // 夜间音量. 不传表示不做修改
    private Integer nightVolume;

    // 充电停止方式, 不传表示不做变更
    private StopModeCfg stopMode;



    // 计费模板ID. 不传表示不修改价格
    private Integer priceCode;

    //计费方案. 不传表示不做修改
    private List<Charge> price;

    public CfgModifyDown() {
        // default
    }


    public CfgModifyDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
