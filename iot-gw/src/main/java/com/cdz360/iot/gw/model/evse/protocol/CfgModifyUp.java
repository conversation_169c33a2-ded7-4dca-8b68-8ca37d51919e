package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩配置下发
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CfgModifyUp extends BaseEvseMsgUp {

    /**
     * 返回码
     *
     * @return 0x00 -- 成功; 其他 -- 失败
     */
    private int returnCode;

    /**
     * 管理员密码返回码
     *
     * @return 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
     */
    private Integer adminReturnCode;

    /**
     * 配置开关项返回码
     *
     * @return 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
     */
    private Integer switchReturnCode;

    /**
     * 配置价格返回码
     *
     * @return 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
     */
    private Integer priceReturnCode;

    /**
     * 配置二维码返回码
     *
     * @return 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
     */
    private Integer qrReturnCode;

    public CfgModifyUp() {
        // default
    }


    public CfgModifyUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
