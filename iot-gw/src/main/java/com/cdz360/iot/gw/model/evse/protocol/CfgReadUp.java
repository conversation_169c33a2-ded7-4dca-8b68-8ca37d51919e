package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.Charge;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取桩配置响应消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CfgReadUp extends BaseEvseMsgUp {


    /**
     * 返回码
     */
    private int returnCode;
    //长效密钥版本号
    private String clipherVerNo;


    /**
     * 桩端一级管理员登录密码
     */
    private String adminCodeA;

    /**
     * 桩端二级管理员登录密码
     */
    private String adminCodeB;

    /**
     * 国际协议
     */
    private byte bmsVer;
    /**
     * 自动停充
     */
    private Boolean autoStop;

    /**
     * 启动类型[bit表示]
     * bit位的含义：
     * 0： 预留
     * 1： 是否开启扫码充电。0 不开启；1开启
     * 2： 是否VIN充电。0 不开启；1开启
     * 3： 是否开启无卡充电。0 不开启；1开启
     * 4： 是否开启刷卡充电。0 不开启；1开启
     * 5： 预留
     * 6： 预留
     * 7： 预留
     */
    private byte startUpTypes;

    /**
     * 充电模式开关[bit型]
     */
    private byte chargingModeSwitch;

//    /**
//     * 均/轮充设置
//     */
//    private byte balanceMode;
//    /**
//     * 合充开关
//     */
//    private byte combination;

//    /**
//     * 辅电手动切换开关
//     */
//    private byte heating;
//    /**
//     * 电池反接检测开关
//     */
//    private byte batteryCheck;
//    /**
//     * 充电记录显示开关
//     * 0x00 -- 不做更改; 0x01 -- 禁用; 0x02 -- 启用
//     */
//    private byte chargingRecordDisplaySwitch;

    // 均/轮充设置
    private byte balanceMode;

    // 合充开关
    private Boolean combination;

    // 辅电手动切换开关
    private Boolean heating;

    // 辅电电压设置
    private Integer heatingVoltage;

    // 电池反接检测开关
    private Boolean batteryCheck;

    // 是否支持充电记录查询. 不传表示不做修改
    private Boolean queryChargeRecord;

    // 主动安全检测开关
    private Boolean securityCheck;

    // 不拔枪充电开关(二次充电)
    private Boolean constantCharge;

    // 插枪获取VIN开关
    private Boolean vinDiscover;

    // 订单信息隐私设置开关（null不做更改 true开启 false关闭）
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    private Byte accountDisplayType;

    // /**
    //  * 定时充电开关
    //  * 0x00 -- 不做更改; 0x01 -- 禁用; 0x02 -- 启用
    //  */
    //private byte TimedChargingSwitch {
    //     return this.Data[18];
    // }

    /**
     * 白天音量
     * bit0: 0 -- 不做变更; 1 -- 变更
     * 后7bit为音量值[0 - 10]
     */
    private byte daytimeVolume;

    /**
     * 黑夜音量
     * bit0: 0 -- 不做变更; 1 -- 变更
     * 后7bit为音量值[0 - 10]
     */
    private byte nightVolume;
    /**
     * 绝缘监测类型
     * 该参数仅在桩端配置: 0x01 -- 泰兴; 0x02 -- DWISE; 0x03 -- 共元
     */
    private byte isolation;
    /**
     * 手动充电开关
     */
    private Boolean manualMode;

    /**
     * 电价模板编码
     */
    private long templateId;

    /**
     * 电价时段个数
     */
    private byte timeSlotCnt;
    /**
     * 电价时段列表
     */
    private List<Charge> timePriceList;


    /**
     * 二维码长度
     */
    private int qRCodeLen;

    /**
     * 二维码URL
     */
    private String qRCodeURL;

    public CfgReadUp() {
        // default
    }


    public CfgReadUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
