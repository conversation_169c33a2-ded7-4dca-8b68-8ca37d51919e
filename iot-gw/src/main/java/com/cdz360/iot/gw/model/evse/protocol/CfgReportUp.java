package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.evse.BmsVer;
import com.cdz360.iot.gw.model.type.BalanceMode;
import com.cdz360.iot.gw.model.type.Isolation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CfgReportUp extends BaseEvseMsgUp {




    private BmsVer bmsVer;
    private Boolean autoStop;
    private BalanceMode balanceMode;
    private Boolean combination;
    private Boolean heating;
    private Integer heatingVoltage;
    private Boolean batteryCheck;
    private Isolation isolation;
    private Boolean manualMode;


    //	是否支持充电记录查询. 不传表示不做修改
    private Boolean queryChargeRecord;
    // 主动安全检测开关
    private Boolean securityCheck;



    public CfgReportUp() {
        // default
    }


    public CfgReportUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
