package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.ChargeMode;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩端发起充电请求上行报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 10:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Charge4EvseReq extends BaseEvseMsgUp {

//        private EvseMsgBase base;

    // 充电启动方式
    private OrderStartType orderStartType;

    // 车辆VIN
    private String vin;

    // 账号长度
    private int accountLen;

    // 账户: 逻辑卡号或VIN码. 卡号使用BCD编码
    private String account;

    // 充电模式
    private ChargeMode chargeMode;

    private Long startTime;

    // 交易开始时枪头的电表读数(kWh)
    private BigDecimal startMeter;

    /**
     * 充电数量
     * <p>
     * 充满模式: 填 00 00 00
     * 按金额充: 16进制数值, 单位 0.01元
     * 按电量充: 16进制数, 单位0.01KWh
     * 按时间充: 16进制数, 单位分钟
     * 用户选择数量与云端返回数量不一致时, 以云端下发为准.
     */
    private long chargeQ;

    // 费用抵扣模式
    private int feeDM;

    public Charge4EvseReq() {
        // default
    }

    public Charge4EvseReq(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
