package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩端发起充电请求响应报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 10:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Charge4EvseRes extends BaseEvseMsgDown {

//    private EvseMsgBase base;

    /**
     * 返回码
     */
    private int result;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 费用抵扣模式
     */
    private int feeDM;

    /**
     * 桩端做展示用, 现金帐户总余额, 单位: 0.01元
     */
    private Long balance;

    // 桩端用于实时扣费, 现金帐户可用余额, 单位: 0.01元
    private Long amount;

    // 桩端做展示用, 电量帐户可用余额, 单位: 0.0001kwh
    private Long electBalance;

    // 桩端用于实时扣费, 电量帐户可用电量, 单位: 0.0001kwh
    private Long electAmount;

    private OrderStopMode stopMode;
    private String carNo;

    private Integer power;

    private Integer soc;

    private ChargePriceVo price;

    public Charge4EvseRes() {
        // default
    }

    public Charge4EvseRes(EvseMsgBase base) {
        super( base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
