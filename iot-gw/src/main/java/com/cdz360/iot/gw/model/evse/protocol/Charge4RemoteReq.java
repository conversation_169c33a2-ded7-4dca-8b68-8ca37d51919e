package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 云端发起充电请求
 * 桩端相应报文
 * 上行
 *
 * @Author: <PERSON>
 * @Date: 2019/10/28 12:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Charge4RemoteReq extends BaseEvseMsgUp {

//    private EvseMsgBase base;

    /**
     * 返回码
     */
    private int result;

//    private ByteBuf orderNoBytes;
    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];

    private String orderNo;

    public Charge4RemoteReq() {
        // default
    }


    public Charge4RemoteReq(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
