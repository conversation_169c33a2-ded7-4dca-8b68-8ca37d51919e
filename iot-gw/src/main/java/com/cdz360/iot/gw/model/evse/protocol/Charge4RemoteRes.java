package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.ChargeMode;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云端发起充电请求下行报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 12:01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class Charge4RemoteRes extends BaseEvseMsgDown{
//    private EvseMsgBase base;

//    private long seqNoToEvse;

    // 订单号
    private String orderNo;

    // 费用抵扣模式
    private int feeDM;

    /**
     * 桩端做展示用, 现金帐户总余额, 单位: 1元
     */
    private BigDecimal balance;

    // 桩端用于实时扣费, 现金帐户可用余额, 单位: 1元
    private BigDecimal amount;

    // 桩端做展示用, 电量帐户可用余额, 单位: 1kWh
    private BigDecimal electBalance;

    // 桩端用于实时扣费, 电量帐户可用电量, 单位: 1kWh
    private BigDecimal electAmount;

    // 充电启动方式
    private OrderStartType orderStartType;

    // 账号长度
    private int accountLen;

    // 账户: 逻辑卡号或VIN码. 卡号使用BCD编码
    private String account;

    // 充电模式
    private ChargeMode chargeMode;

    private String stopCode;    // 停充码

    // 车牌号
    private String carNo;

    // 功率限制
    private Integer power;

    // SOC限制
    private Integer soc;
    /**
     * 充电数量
     * <p>
     * 充满模式: 填 00 00 00
     * 按金额充: 16进制数值, 单位 0.01元
     * 按电量充: 16进制数, 单位0.01KWh
     * 按时间充: 16进制数, 单位分钟
     * 用户选择数量与云端返回数量不一致时, 以云端下发为准.
     */
    private int chargeQ;


    // 差异化价格信息
    private ChargePriceVo price;

    public Charge4RemoteRes() {
        // default
    }


    public Charge4RemoteRes(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
