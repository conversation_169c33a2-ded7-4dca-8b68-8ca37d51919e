package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 鉴权响应消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeAuthDown extends BaseEvseMsgDown {

    public ChargeAuthDown() {
        // default
    }

    public ChargeAuthDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}