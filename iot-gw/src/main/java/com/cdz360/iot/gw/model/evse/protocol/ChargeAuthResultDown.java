package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 鉴权结果下发
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeAuthResultDown extends BaseEvseMsgDown {

    /**
     * 返回码
     */
    private int result;

    private BigDecimal totalAmount;

    private BigDecimal totalPower;

    private String carNo;

    public ChargeAuthResultDown() {
        // default
    }


    public ChargeAuthResultDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
