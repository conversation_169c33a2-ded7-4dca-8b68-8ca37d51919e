package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 鉴权结果下发
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeAuthResultUp extends BaseEvseMsgUp {

    public ChargeAuthResultUp() {
        // default
    }


    public ChargeAuthResultUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
