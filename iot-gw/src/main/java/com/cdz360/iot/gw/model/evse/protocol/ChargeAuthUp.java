package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩端上行在线卡/VIN码鉴权请求
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeAuthUp extends BaseEvseMsgUp {

//    private EvseMsgBase base;

    /**
     * 鉴权类型
     *
     * @return 0x11 -- 在线卡鉴权; 0x12 -- VIN码鉴权
     */
    private OrderStartType authType;

    private String vin;

    /**
     * 账号长度
     *
     * @return
     */
    private int accountLen;

    /**
     * 账号
     *
     * @return
     */
    private String account;

    public ChargeAuthUp() {
        // default
    }


    public ChargeAuthUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
