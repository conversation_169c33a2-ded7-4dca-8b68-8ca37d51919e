package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 枪头数据上报 6807
 */


@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeHbDown extends BaseEvseMsgDown {

    /**
     * 返回码
     */
    private int result;

//    private ByteBuf orderNoBytes;
    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];

    public ChargeHbDown() {
        // default
    }


    public ChargeHbDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
