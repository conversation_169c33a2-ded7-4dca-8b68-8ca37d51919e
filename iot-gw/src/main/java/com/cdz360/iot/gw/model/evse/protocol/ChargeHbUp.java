package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.dto.BatteryDynamicDto;
import com.cdz360.base.model.charge.dto.BmsDynamicDto;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上行 枪头数据上报 6807
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeHbUp extends BaseEvseMsgUp {

    //    private ByteBuf orderNoBytes;
//    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];

    // 获取订单号
    private String orderNo;

    // MeterValues 上报的时间
    private ZonedDateTime mvHbTime;

    /**
     * 获取消费电量(单位：kwh)
     *
     * @return
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;


    /**
     * 获取SOC
     *
     * @return
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer soc;

    /**
     * 当前充电功率, 单位 kw 3.5.3开始支持
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal power;

    // 车端实时需求功率
    private BigDecimal requestPower;


    /**
     * 获取已充时长（单位分钟）
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer duration;

    /**
     * 获取剩余时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer remainTime;

    /**
     * 实时电费(单位：元)
     *
     * @return
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecFee;

    /**
     * 实时服务费(单位：元)
     *
     * @return
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servFee;

//
//
//    /**
//     * 获取电池类型
//     *
//     * @return
//     */
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer batteryType;


    /**
     * 获取桩体温度
     * <p>单位是1摄氏度(℃)</p>
     *
     * @return
     */
    private Integer evseTemp;

    /**
     * 获取枪温度
     * <p>交直流的单位是1摄氏度(℃)</p>
     *
     * @return
     */
    private Integer plugTemp;

    /**
     * 交流A相电压
     */
    private BigDecimal voltageA;

    /**
     * 交流A相电流
     */
    private BigDecimal currentA;

    /**
     * 交流B相电压/直流A相输入电压
     */
    private BigDecimal voltageB;

    /**
     * 交流B相电流/直流B相输入电压
     */
    private BigDecimal currentB;

    /**
     * 交流C相电压/直流C相输入电压
     */
    private BigDecimal voltageC;

    /**
     * 交流C相电流
     */
    private BigDecimal currentC;

    /**
     * 直流桩输出电流
     */
    private BigDecimal outputCurrent;

    /**
     * 直流桩输出电压
     */
    private BigDecimal outputVoltage;

    /**
     * BMS动态数据
     * <p>3.5.3开始支持</p>
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDynamicDto bms;

    /**
     * 电池动态信息
     * <p>3.5.3开始支持</p>
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDynamicDto battery;


    public ChargeHbUp() {
        // default
    }


    public ChargeHbUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonTimeString(this);
    }
}
