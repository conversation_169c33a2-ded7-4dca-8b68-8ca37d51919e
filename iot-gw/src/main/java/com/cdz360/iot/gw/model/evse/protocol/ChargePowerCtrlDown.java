package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargePowerCtrlDown extends BaseEvseMsgDown {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 本次充电的最大输出功率, 单位 kw
     */
    private Integer powerCtrl;


    public ChargePowerCtrlDown() {
        // default
    }


    public ChargePowerCtrlDown(EvseMsgBase base) {
        super(base);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
