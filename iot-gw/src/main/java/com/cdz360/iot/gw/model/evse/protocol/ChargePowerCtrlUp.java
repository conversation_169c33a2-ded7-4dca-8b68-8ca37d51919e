package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargePowerCtrlUp extends BaseEvseMsgUp {

    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];

    /**
     * 订单号
     */
    private String orderNo;

    private int result;

    /**
     * 本次充电的最大输出功率，单位kW
     */
    private Integer powerCtrl;

    public ChargePowerCtrlUp() {
        // default
    }

    public ChargePowerCtrlUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
