package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上行 在线订单续费 6806
 *
 * @Author: <PERSON>
 * @Date: 2019/10/28 13:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeProlongDown extends BaseEvseMsgDown {

    /**
     * 返回码
     */
    private int result;

    // 订单号
    private String orderNo;

    /**
     * 桩端做展示用, 现金帐户总余额, 单位: 元
     */
    private BigDecimal totalAmount;

    // 总冻结金额, 单位: 元
    private BigDecimal totalFrozenAmount;

    // 桩端用于实时扣费, 现金账户增量余额, 单位: 元
    private BigDecimal frozenAmount;

    // 桩端做展示用, 电量帐户可用余额, 单位: kwh
    private BigDecimal totalPower;

    // 桩端用于实时扣费, 电量账户增量电量, 单位: kwh
    private BigDecimal frozenPower;

    public ChargeProlongDown() {
        // default
    }


    public ChargeProlongDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
