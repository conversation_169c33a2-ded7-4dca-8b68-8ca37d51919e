package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上行 在线订单续费 6806
 *
 * @Author: Nathan
 * @Date: 2019/10/28 13:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeProlongUp extends BaseEvseMsgUp {
//    private EvseMsgBase base;

    // 充电启动方式
    private OrderStartType orderStartType;

    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];
    // 订单号
    private String orderNo;

    // 费用抵扣模式
    private int feeDM;

    // 桩端用于实时扣费, 现金帐户可用余额, 单位: 0.01元
    private BigDecimal amount;

    // 剩余金额: 单位, 0.01元
    private BigDecimal remainAmount;

    // 剩余电量: 单位, 0.0001kwh
    private BigDecimal remainPower;


    public ChargeProlongUp() {
        // default
    }


    public ChargeProlongUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
