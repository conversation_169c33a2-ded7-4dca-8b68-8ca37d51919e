package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import io.netty.buffer.ByteBuf;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeSocCtrlDown extends BaseEvseMsgDown {

    private ByteBuf orderNoByteBuf;

    private String orderNo;

    private Integer limitSoc;

    public ChargeSocCtrlDown() {
        // default
    }

    public ChargeSocCtrlDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
