package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeSocCtrlUp extends BaseEvseMsgUp {

    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];

    private String orderNo;

    private int result;

    public ChargeSocCtrlUp() {
        // default
    }

    public ChargeSocCtrlUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
