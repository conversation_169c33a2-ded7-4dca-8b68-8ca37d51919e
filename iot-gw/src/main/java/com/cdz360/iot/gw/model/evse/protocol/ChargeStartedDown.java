package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩上报充电开始下行报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 13:26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStartedDown extends BaseEvseMsgDown {

    /**
     * 返回码
     */
    private int result;

    private String orderNo;

    public ChargeStartedDown() {
        // default
    }

    public ChargeStartedDown(EvseMsgBase base) {
        super(base);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
