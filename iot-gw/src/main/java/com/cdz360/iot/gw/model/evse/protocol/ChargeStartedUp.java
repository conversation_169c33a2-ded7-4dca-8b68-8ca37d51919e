package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.dto.BatteryDto;
import com.cdz360.base.model.charge.dto.BmsDto;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.type.ChargeMode;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.cdz360.iot.gw.util.DecimalUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩上报充电开始上行报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 13:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStartedUp extends BaseEvseMsgUp {
//    private EvseMsgBase base;

    private boolean startTransReq;

    // 充电启动方式
    private OrderStartType orderStartType;

    // 账号长度
    private int accountLen;

    // 账户: 逻辑卡号或VIN码. 卡号使用BCD编码
    private String accountNo;

    // 交易ID
    private Integer transId;

    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];
    // 订单号
    private String orderNo;

    // 订单启动结果 0x00表示正常启动充电，非0走充电结束逻辑
    private int orderStartResult;

    // 费用抵扣模式
    private int feeDM;

    //    // 充电模式
    //    private ChargeMode chargeMode;
    //
    //    /**
    //     * 充电数量
    //     * <p>
    //     * 充满模式: 填 00 00 00
    //     * 按金额充: 16进制数值, 单位 0.01元
    //     * 按电量充: 16进制数, 单位0.01KWh
    //     * 按时间充: 16进制数, 单位分钟
    //     * 用户选择数量与云端返回数量不一致时, 以云端下发为准.
    //     */
    //    private int chargeQ;
    private OrderStopMode stopMode;

    // 	功率限制
    private Integer power;

    // SOC: 当前SOC的16进制数, 百分制表示. 无SOC时传 0xFF
    private Integer soc;

    // 充电开始时间
    private long startTime;

    // 充电开始电表读数
    private BigDecimal startMeter;

    /**
     * 3.5.3协议开始支持
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    /**
     * 绝缘检测
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Insulation insulation;

    /**
     * BMS信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDto bms;

    /**
     * 电池信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDto battery;

    public ChargeStartedUp() {
        // default
    }


    public ChargeStartedUp(EvseMsgBase base) {
        super(base);
    }

    public void fillStopMode(ChargeMode mode, long chargeQ) {
        this.stopMode = new OrderStopMode();
        this.stopMode.setType(mode);
        switch (mode) {
            case FULL: // 充满模式
                break;
            case AMOUNT:
                this.stopMode.setAmount(DecimalUtils.divide100(chargeQ));
                break;
            case KWH:
                this.stopMode.setKwh(DecimalUtils.divide100(chargeQ));
                break;
            case TIME:
                this.stopMode.setTime(chargeQ);
                break;
        }
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class Insulation {

        /**
         * 绝缘检测结果
         *
         * <p>0正常 1故障 2告警</p>
         */
        private Integer result;

        //@ApiModelProperty(value = "DC+绝缘检测值. 单位1Ω/V", example = "123")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer positive;

        //@ApiModelProperty(value = "DC-绝缘检测值. 单位1Ω/V", example = "123")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer negative;

        /**
         * 绝缘检测电压 单位V
         */
        private BigDecimal voltage;
    }
}
