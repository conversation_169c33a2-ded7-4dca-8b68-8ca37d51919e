package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 云端请求停止充电
 * 下行报文
 *
 * @Author: <PERSON>
 * @Date: 2019/10/28 14:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStopDown extends BaseEvseMsgDown {
//    private EvseMsgBase base;

    // 充电启动方式
    private OrderStartType orderStartType;

    // 订单号
    private String orderNo;

    public ChargeStopDown() {
        // default
    }

    public ChargeStopDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
