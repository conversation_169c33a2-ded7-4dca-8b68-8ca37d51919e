package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 云端请求停止充电
 * 桩端响应报文
 * 上行
 *
 * @Author: <PERSON>
 * @Date: 2019/10/28 14:15
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStopUp extends BaseEvseMsgUp {
//    private EvseMsgBase base;

    /**
     * 返回码
     */
    private int result;

    private String orderNo;

    public ChargeStopUp() {
        // default
    }


    public ChargeStopUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
