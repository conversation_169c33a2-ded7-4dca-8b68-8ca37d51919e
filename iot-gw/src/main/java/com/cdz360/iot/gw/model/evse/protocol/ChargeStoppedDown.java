package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩上报充电完成
 * 响应报文
 * 下行
 *
 * @Author: <PERSON>
 * @Date: 2019/10/28 14:20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStoppedDown extends BaseEvseMsgDown {


    /**
     * 订单号
     */
    private String orderNo;

    public ChargeStoppedDown() {
        // default
    }

    public ChargeStoppedDown(EvseMsgBase base) {
        super( base);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
