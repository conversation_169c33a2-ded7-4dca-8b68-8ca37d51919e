package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.north.model.OrderDetail;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩上报充电完成上行报文
 *
 * @Author: Nathan
 * @Date: 2019/10/28 14:19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeStoppedUp extends BaseEvseMsgUp {
//    private EvseMsgBase base;

    // 充电启动方式
    private OrderStartType orderStartType;

    // 交易ID
    private Integer transId;

//    private ByteBuf orderNoBytes;
    private byte[] orderNoBytes = new byte[IotGwConstants.ORDER_NO_BYTES];
    // 订单号
    private String orderNo;

    // 账号长度
    private int accountLen;

    // 账户: 逻辑卡号或VIN码. 卡号使用BCD编码
    private String account;

    // 费用抵扣模式
    private int feeDM;

    // 订单完成原因
    private int completeCode;

    // 故障停充码
    private int errorCode;

    // 开始SOC
    private Integer startSoc;

    // 结束SOC
    private Integer stopSoc;

    // 开始充电时间: unix时间戳
    private long startTime;

    // 结束充电时间: unix时间戳
    private long stopTime;

    // 充电前电表读数: 单位kWh
    private BigDecimal startMeter;

    // 充电结束电表读数: 单位kWh
    private BigDecimal stopMeter;

    // 订单总电量: 单位，0.0001kwh
    private BigDecimal kwh;

    // 合充的辅枪枪号
    private Integer secondPlugIdx;

    // 辅枪充电前电表读数
    private BigDecimal secondStartMeter;

    // 辅枪充电后电表读数
    private BigDecimal secondStopMeter;

    // 辅枪订单总电量
    private BigDecimal secondKwh;

    // 订单总金额: 单位，0.01元
    private BigDecimal orderFee;

    // 订单总电费: 单位，0.01元
    private BigDecimal elecFee;

    // 订单总服务费: 单位，0.01元
    private BigDecimal servFee;

    // VIN码
    private String vin;

    // 电池单体最低电压
    private BigDecimal minBatteryVoltage;

    // 电价模板编号
    private Long priceCode;

    // 时段个数
    private int timeCnt;

    // 账单详情, 充电订单结束时上报.
    private List<OrderDetail> detail;

    public ChargeStoppedUp() {
        // default
    }

    public ChargeStoppedUp(EvseMsgBase base) {
        super( base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
