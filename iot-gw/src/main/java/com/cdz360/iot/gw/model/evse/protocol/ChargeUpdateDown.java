package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充电中分时数据上报
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeUpdateDown extends BaseEvseMsgDown {

    /**
     * 订单号
     */
    private String orderNo;

    public ChargeUpdateDown() {
        // default
    }


    public ChargeUpdateDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
