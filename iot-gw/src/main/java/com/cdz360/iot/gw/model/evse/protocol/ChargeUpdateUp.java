package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.OrderDetail;
import com.fasterxml.jackson.annotation.JsonInclude;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 充电中分时数据上报
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeUpdateUp extends BaseEvseMsgUp {

//    private EvseMsgBase base;

    private String orderNo;

    /**
     * 当前SOC
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer soc;

    /**
     * 订单总电量
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;

    /**
     * 订单总金额
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderFee;

    /**
     * 订单总电费
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecFee;

    /**
     * 订单总服务费
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servFee;


    // 电价模板编号
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;

    // 时段个数
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timeCnt;

    // 账单详情, 充电订单结束时上报.
    private List<OrderDetail> detail;

    public ChargeUpdateUp() {
        // default
    }


    public ChargeUpdateUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
