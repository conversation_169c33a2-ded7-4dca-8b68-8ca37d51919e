package com.cdz360.iot.gw.model.evse.protocol;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 直流模块信息
 */
@Data
public class DcModuleInfo {

    /**
     * 序号
     */
    private Integer idx;
    /**
     * 模块编号
     */
    private String deviceNo;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 关联枪头
     */
    private Integer plugId;
    /**
     * 进风口温度
     */
    private Integer intakeTemp;
    /**
     * 设定电压，单位V
     */
    private BigDecimal voltage;
    /**
     * 实际电压，单位V
     */
    private BigDecimal actualVoltage;
    /**
     * 设定电流，单位A
     */
    private BigDecimal current;
    /**
     * 实际电流，单位A
     */
    private BigDecimal actualCurrent;

}
