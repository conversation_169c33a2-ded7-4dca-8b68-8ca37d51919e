package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;

/**
 * 桩debug数据上报
 * 上行消息
 */
@Data
public class EvseDebugDataReq {

    private EvseMsgBase base;

    private int msgLength;

    private String msg;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
