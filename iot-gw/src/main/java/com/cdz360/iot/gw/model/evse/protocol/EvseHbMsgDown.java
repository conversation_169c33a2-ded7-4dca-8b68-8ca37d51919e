package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心跳响应报文
 * 下行
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvseHbMsgDown extends BaseEvseMsgDown {
//    private EvseMsgBase base;
    /**
     * 返回码
     */
    private int result;

    public EvseHbMsgDown() {
        // default
    }

    public EvseHbMsgDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
