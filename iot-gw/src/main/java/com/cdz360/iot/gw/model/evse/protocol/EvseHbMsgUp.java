package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.EvseGunStatus;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桩上行心跳报文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvseHbMsgUp extends BaseEvseMsgUp {

//    private EvseMsgBase base;

    /**
     * 获取桩枪状态
     *
     * @return
     * @see EvseGunStatus
     */
    private EvseGunStatus plugStatus;


    /**
     * 获取故障码
     *
     * @return
     */
    private int errorCode;

    /**
     * 获取告警码
     *
     * @return
     */
    private int alertCode;

    /**
     * 车辆VIN
     */
    private String vin;


    public EvseHbMsgUp() {
        // default
    }


    public EvseHbMsgUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
