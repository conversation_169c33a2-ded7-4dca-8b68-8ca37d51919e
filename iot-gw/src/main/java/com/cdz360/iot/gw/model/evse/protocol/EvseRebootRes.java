package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;

/**
 * 桩重启的响应消息
 */
@Data
public class EvseRebootRes {

    private EvseMsgBase base;

    /**
     * 返回码
     */
    private int result;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
