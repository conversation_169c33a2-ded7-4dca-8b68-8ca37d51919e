package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseRegisterDown extends BaseEvseMsgDown{

    private EvseMsgBase base;

    private Long passcodeVer;
    /**
     * 返回码
     */
    private int result;

    /**
     * 加密后的临时密钥
     */
    private byte[] encKey;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
