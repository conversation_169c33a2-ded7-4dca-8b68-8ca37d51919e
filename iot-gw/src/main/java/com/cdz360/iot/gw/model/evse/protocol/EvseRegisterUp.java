package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.NetType;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 桩注册上行请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EvseRegisterUp extends BaseEvseMsgUp {

//    private EvseMsgBase base;
    /**
     * 协议版本
     */
    private int protocolVersion;

    // 设备功能型号
    private int functionModelNo;

    //设备整体版本
    private int evseVersion;

    /**
     * 桩类型: 0x00 -- 交流; 0x01 -- 直流; 0x02 -- 交直流
     *
     * @return
     */
    private SupplyType supplyType;


    /**
     * 获取枪数量
     *
     * @return
     */
    private int plugNum;


    // 功率, 单位 kw
    private int power;

    // 电价模板编号
    private Long priceCode;

    // 网络接入方式
    private NetType netType;

    // DTU类型
    private DtuType dtuType;

    /**
     * 桩IP地址
     */
//    private String evseIp;

    /**
     * ICCID, SIM卡号， 20位的数字字母
     */
    private String iccid;

    /**
     * 国际移动用户识别码,15位数字
     */
    private String imsi;

    /**
     * 移动设备国际识别码,15位数字
     */
    private String imei;

    /**
     * 上电标志位(注册原因)
     */
    private EvseRegisterReason registerReason;

    private String firmwareVersion;

    /**
     * PC01定制号 (vendor code)
     *
     * @return
     */
    private int pc01Vc;


    /**
     * PC01软件版本号
     *
     * @return
     */
    private int pc01Sw;


    /**
     * PC01硬件版本号
     *
     * @return
     */
    private int pc01Hw;


    /**
     * PC02定制号 (vendor code)
     *
     * @return
     */
    private int pc02Vc;


    /**
     * PC02软件版本号
     *
     * @return
     */
    private int pc02Sw;


    /**
     * PC02硬件版本号
     *
     * @return
     */
    private int pc02Hw;

    /**
     * PC03定制号 (vendor code)
     *
     * @return
     */
    private int pc03Vc;


    /**
     * PC03软件版本号
     *
     * @return
     */
    private int pc03Sw;


    /**
     * PC03硬件版本号
     *
     * @return
     */
    private int pc03Hw;

    /**
     * 长效密钥版本号
     */
    private long passcodeVer;

    /**
     * 枪头最低电压, 单位 0.1V. 仅直流桩有值
     */
    private Integer minVoltage;

    /**
     * 枪头最高电压, 单位 0.1V. 仅直流桩有值
     */
    private Integer maxVoltage;

    private List<DcPlugInfo> dcPlugs;
    private List<AcPlugInfo> acPlugs;

    public EvseRegisterUp() {
        // default
    }


    public EvseRegisterUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    @Accessors(chain = true)
    public static class DcPlugInfo {

        /**
         * 枪头编号
         */
        private int plugIdx;

        /**
         * 枪头最大功率，单位kw
         */
        private Integer power;
        /**
         * 最低电流,单位 0.1A
         */
        private Integer minCurrent;
        /**
         * 最大电流, 单位0.1A
         */
        private Integer maxCurrent;


        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class AcPlugInfo {

        /**
         * 枪头编号
         */
        private int plugIdx;
        /**
         * 枪头功率, 单位 kw
         */
        private Integer power;
        /**
         * 枪头电压, 单位0.1V
         */
        private Integer voltage;

        /**
         * 枪头电流，单位0.1A
         */
        private Integer current;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

}
