package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseReportDeviceDetailUp extends BaseEvseMsgUp {

    /**
     * 直流模块总数
     */
    private Integer dcModuleTotal;
    /**
     * 直流模块型号
     */
    private String dcModuleModel;
    /**
     * 直流模块信息
     */
    private List<DcModuleInfo> dcModuleInfoList;

    private EvseReportDeviceDetailUp() {
        // default
    }

    public EvseReportDeviceDetailUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
