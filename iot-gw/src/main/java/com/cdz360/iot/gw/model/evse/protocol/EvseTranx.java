package com.cdz360.iot.gw.model.evse.protocol;

import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class EvseTranx<U extends BaseEvseMsgUp, D extends Confirmation> {

    private String evseNo;

    /**
     * 上行消息 桩 -> 网关
     */
    private U upMsg;

    /**
     * 下行消息 网关 -> 桩
     */
    private D downMsg;
}
