package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseUpgradeDown extends BaseEvseMsgDown{


    private long seqNo;
    private String evseId;

    private byte downloadType;
    private String accountNo;
    private String password;

    private int pc01VendorCode;
    private int pc01SwVer;
    private String pc01Url;

    private int pc02VendorCode;
    private int pc02SwVer;
    private String pc02Url;

    private int pc03VendorCode;
    private int pc03SwVer;
    private String pc03Url;


    public EvseUpgradeDown() {
        // default
    }


    public EvseUpgradeDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
