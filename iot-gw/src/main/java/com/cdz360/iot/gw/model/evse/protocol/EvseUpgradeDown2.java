package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.mq.model.EvseVersion;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseUpgradeDown2 extends BaseEvseMsgDown {

    private long seqNo;
    private String evseId;

    private String accountNo;
    private String password;

    private List<EvseVersion> evseVersionList;

    public EvseUpgradeDown2() {
        // default
    }

    public EvseUpgradeDown2(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
