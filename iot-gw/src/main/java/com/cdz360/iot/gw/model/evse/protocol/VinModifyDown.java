package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.WhiteVin;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname VinModifyDown
 * @Description
 * @Date 11/22/2021 3:55 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class VinModifyDown extends BaseEvseMsgDown{



    private List<WhiteVin> whiteVins;

    public VinModifyDown() {
        // default
    }


    public VinModifyDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}