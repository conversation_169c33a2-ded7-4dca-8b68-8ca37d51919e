package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Classname VinModifyUp
 * @Description
 * @Date 11/22/2021 3:54 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VinModifyUp extends BaseEvseMsgUp {

    /**
     * 修改结果. 0x00表示成功
     */
    private int result;

    public VinModifyUp() {
        // default
    }


    public VinModifyUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
