package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Classname VinQueryDown
 * @Description
 * @Date 11/22/2021 3:43 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VinQueryDown extends BaseEvseMsgDown {

    public VinQueryDown() {
        // default
    }

    public VinQueryDown(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
