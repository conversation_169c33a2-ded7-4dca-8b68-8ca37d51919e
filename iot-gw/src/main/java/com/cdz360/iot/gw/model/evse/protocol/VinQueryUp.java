package com.cdz360.iot.gw.model.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.WhiteVin;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Classname VinQueryUp
 * @Description
 * @Date 11/22/2021 3:38 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VinQueryUp extends BaseEvseMsgUp {

    /**
     * 返回码
     */
    private int result;

    /**
     *
     */
    private int vinCount;

    private List<WhiteVin> whiteVinList;

    public VinQueryUp() {
        // default
    }

    public VinQueryUp(EvseMsgBase base) {
        super(base);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
