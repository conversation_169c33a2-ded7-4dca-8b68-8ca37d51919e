package com.cdz360.iot.gw.model.evse.rcd;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class RcdBillReqDto {

    private Integer connector;

    /**
     * 交易ID
     */
    private Integer serial;

    private String account;

    /**
     * 总费用（元）
     */
    private BigDecimal amount;

    /**
     * 总电量（度）
     */
    private BigDecimal electricEnergy;

    private String reason;

    // 订单完成原因
    private Integer completeCode;

    // 故障停充码
    private Integer errorCode;

    private String type;

    /**
     * 充电方式
     * <p>0: 自动充满</p>
     * <p>1: 定额充电</p>
     * <p>2: 定度充电</p>
     * <p>3: 定时充电</p>
     * <p>4: 预约充电</p>
     */
    private Integer chargeType;

    private Integer chargeArguments;

    private Date startTime;

    private Date stopTime;

    /**
     * 充电时长（分钟）
     */
    private Integer chargeTime;

    /**
     * 尖电量（k·Wh）
     */
    private BigDecimal sharpEnergy;

    /**
     * 峰电量（k·Wh）
     */
    private BigDecimal peakEnergy;

    /**
     * 平电量（k·Wh）
     */
    private BigDecimal flatEnergy;

    /**
     * 谷电量（k·Wh）
     */
    private BigDecimal valleyEnergy;

    /**
     * 充电费用（元）
     */
    private BigDecimal energyAmount;

    /**
     * 服务费用（元）
     */
    private BigDecimal serverAmount;

    private Integer startSoc;

    private Integer stopSoc;

    /**
     * 开始电表读数（kW·h）
     */
    private BigDecimal startEnergy;

    /**
     * 结束电表读数
     */
    private BigDecimal stopEnergy;

}