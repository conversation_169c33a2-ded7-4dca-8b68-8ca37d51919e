package com.cdz360.iot.gw.model.evse.rcd;

import eu.chargetime.ocpp.model.dc.type.DeviceStatusCodeType;
import jakarta.annotation.Nullable;
import lombok.Getter;

@Getter
public enum RcdErrorCode {

    UNKNOWN("未知", null),
    NetworkAbnormally("网络异常", DeviceStatusCodeType.C5200),
    CableOvertemperature("电缆过温", DeviceStatusCodeType.C5201),
    LEDBoardALost("LED板A丢失", DeviceStatusCodeType.C5202),
    LEDBoardBLost("LED板B丢失", DeviceStatusCodeType.C5203),
    EMSDisconnected("EMS断开", DeviceStatusCodeType.C5204),
    LightningArresterAlarm("避雷器告警", DeviceStatusCodeType.C5205),
    ScreenNotconnected("屏幕未连接", DeviceStatusCodeType.C5206),
    NetworkCableFailure("网线故障", DeviceStatusCodeType.C5207),
    UnconnectedServer("未连接服务器", DeviceStatusCodeType.C5208),
    PileOvertemperature("充电桩过温", DeviceStatusCodeType.C5209),
    ACPowerMeterFailure("交流电表通讯故障", DeviceStatusCodeType.C5210),
    PowerMeterFailure("电表故障", DeviceStatusCodeType.C5211),
    OverCurrentFailure("交流输入过流报警", DeviceStatusCodeType.C5212),
    UnderVoltage("欠压", DeviceStatusCodeType.C5213),
    OverVoltage("过压", DeviceStatusCodeType.C5214),
    DCOverCurrentFailure("直流过流故障", DeviceStatusCodeType.C5215),
    DCOverVoltage("直流输出过压报警", DeviceStatusCodeType.C5216),
    ACInputContactorFailure("交流输入接触器故障", DeviceStatusCodeType.C5217),
    ContactorFailure("接触器故障", DeviceStatusCodeType.C5218),
    LinkContactorFailure("直连并联接触器故障", DeviceStatusCodeType.C5219),
    BatShortFailure("电池短路故障", DeviceStatusCodeType.C5220),
    Cable0vertemperature_0x01("线头过温", DeviceStatusCodeType.C5221),
    Cable0vertemperature_0x02("充电枪超温故障", DeviceStatusCodeType.C5222),
    GroundLockFailure("地锁通讯故障", DeviceStatusCodeType.C5223),
    BatteryReversed("电池反接", DeviceStatusCodeType.C5224),
    ElectricLockerFailure("电子锁故障", DeviceStatusCodeType.C5225),
    LiquidCableCommFailure("液冷枪通信故障", DeviceStatusCodeType.C5226),
    DeviceDisable("设备停用", DeviceStatusCodeType.C5227),
    PowerModule0ccupied("模块被占用", DeviceStatusCodeType.C5228),
    PMCommFailure("电源模块通信故障", DeviceStatusCodeType.C5229),
    SECCCommFailure("SECC通信故障", DeviceStatusCodeType.C5230),
    EmergencyStopFailure("急停故障", DeviceStatusCodeType.C5231),
    AccessControllerFailure("门禁控制器故障", DeviceStatusCodeType.C5232),
    FanFailure("风扇故障", DeviceStatusCodeType.C5233),
    PowerDownFailure("掉电故障", DeviceStatusCodeType.C5234),
    LeakCurrentFailure("漏电故障", DeviceStatusCodeType.C5235),
    SmokeFailure("烟雾报警", DeviceStatusCodeType.C5236),
    PileOvertemperatureFailure("充电桩过温故障", DeviceStatusCodeType.C5237),
    CableOvertemperatureFailure("线头过温故障", DeviceStatusCodeType.C5238),
    FloodFailure("水浸故障", DeviceStatusCodeType.C5239),
    PMCommFailure_deprecate("PMCommFailure 'deprecate'", "电源模块通信故障(已废弃)",
        DeviceStatusCodeType.C5240),
    AddressConflict("从机地址冲突", DeviceStatusCodeType.C5241),
    M_S_CommFailure("M-S CommFailure", "主从通讯故障", DeviceStatusCodeType.C5242),
    Power_stack_Failure("Power Stack Failure", "功率堆故障", DeviceStatusCodeType.C5243),
    PENBrokenFailure("接地故障", DeviceStatusCodeType.C5244),
    ;

    private String desc;
    @Nullable
    private String checkName;
    @Nullable
    private DeviceStatusCodeType codeType;

    RcdErrorCode(String checkName, String desc, DeviceStatusCodeType codeType) {
        this.checkName = checkName;
        this.desc = desc;
        this.codeType = codeType;
    }

    RcdErrorCode(String desc, DeviceStatusCodeType codeType) {
        this.desc = desc;
        this.codeType = codeType;
    }

    public static RcdErrorCode valueOfByStr(String str) {
        if (str == null || str.isEmpty()) {
            return UNKNOWN;
        }

        for (RcdErrorCode code : values()) {
            if (str.equalsIgnoreCase(code.name()) || str.equalsIgnoreCase(code.checkName)) {
                return code;
            }
        }
        return UNKNOWN;
    }

}
