package com.cdz360.iot.gw.model.evse.rcd;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class RcdQrCodeReqVo {

    /**
     * 需明确指定枪头ID
     */
    private int connector_id;

    /**
     * 充电桩ID
     */
    private String evse_id;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 单位（EUR/kWh）
     */
    private String unit;

    /**
     * 完整URL（https://backstage.rainbowcharging.com/h5/524337121060362901）
     */
    private String url;

}