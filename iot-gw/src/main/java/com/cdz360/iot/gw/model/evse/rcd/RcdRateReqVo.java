package com.cdz360.iot.gw.model.evse.rcd;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.Data;

@Data
public class RcdRateReqVo {

    /**
     * 尖电费
     */
    private BigDecimal sharpPrice;

    /**
     * 尖服务费
     */
    private BigDecimal sharpService;

    /**
     * 峰电费
     */
    private BigDecimal peakPrice;

    /**
     * 峰服务费
     */
    private BigDecimal peakService;

    /**
     * 平电费
     */
    private BigDecimal flatPrice;

    /**
     * 平服务费
     */
    private BigDecimal flatService;

    /**
     * 谷电费
     */
    private BigDecimal valleyPrice;

    /**
     * 谷服务费
     */
    private BigDecimal valleyService;

    private Integer periodNum;

    @JsonIgnore
    private Map<String, Object> periodFields = new LinkedHashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getPeriodFields() {
        return periodFields;
    }

}