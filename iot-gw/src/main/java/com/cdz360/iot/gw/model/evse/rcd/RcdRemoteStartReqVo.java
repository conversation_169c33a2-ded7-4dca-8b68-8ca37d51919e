package com.cdz360.iot.gw.model.evse.rcd;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RcdRemoteStartReqVo {

    /**
     * 填orderNo即可
     */
    private String account;

    /**
     * 单位：0.01元/位
     */
    private Long balance;

    /**
     * 0:12V，1:24V
     * <p>仅对直流充电枪有效，交流充电枪此处填 0</p>
     */
    private Integer bmsSelect;

    /**
     * 0:自动充满;1:按电度;2:按金额;3:按时间;
     */
    private Integer chargeArg;

    /**
     * 1、普通; 2、多枪同充;
     */
    private Integer chargeMode;

    /**
     * <p>自动充满时，填 0。<p/>
     * <p>按电度，单位:1kwh/位:<p/>
     * <p>按金额，单位:0.01元/位，</p>
     * <p>按时间，单位:1min/位。</p>
     */
    private Integer chargeType;

    /**
     * 按实际填写，0代表整桩，1代表A枪·2·代表B枪,
     */
    private Integer connector;

    /**
     * 填orderNo即可
     */
    private Integer serial;

    /**
     * 无符号数。用户可在桩上输入此密码，直接停电
     */
    private String stopPassword;

    /**
     * 0:调试模式，1:扫码，2:刷卡，3:密码启动，4:鉴权卡·"
     */
    private Integer type;

}
