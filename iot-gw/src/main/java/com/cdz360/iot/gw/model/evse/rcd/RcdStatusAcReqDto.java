package com.cdz360.iot.gw.model.evse.rcd;

import java.util.Date;
import lombok.Data;

@Data
public class RcdStatusAcReqDto {

    private Integer connector;

    private Date currentTime;

    /**
     * 交易ID
     */
    private Integer transactionId;

    /**
     * 充电费用（0.01元）
     */
    private Integer energyAmount;

    /**
     * 服务费用（0.01元）
     */
    private Integer serverAmount;

    /**
     * 总费用（0.01元）
     */
    private Integer amount;

    /**
     * 总电量（0.01度）
     */
    private Integer electricEnergy;

    /**
     * 尖电量（1 Wh）
     */
    private Integer sharpEnergy;

    /**
     * 峰电量（1 Wh）
     */
    private Integer peakEnergy;

    /**
     * 平电量（1 Wh）
     */
    private Integer flatEnergy;

    /**
     * 谷电量（1 Wh）
     */
    private Integer valleyEnergy;

    /**
     * 充电时长（分钟）
     */
    private Integer chargeTime;

    private Integer power;

    /**
     * 0.01 V
     */
    private Integer aVoltage;

    /**
     * 0.01 A
     */
    private Integer aCurrent;

}