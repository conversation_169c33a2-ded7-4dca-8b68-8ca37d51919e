package com.cdz360.iot.gw.model.evse.winline;

import eu.chargetime.ocpp.model.dc.type.DeviceStatusCodeType;
import lombok.Getter;

@Getter
public enum WinlineErrorCode {

    UNKNOWN(null),
    C0(null),
    C1002(DeviceStatusCodeType.C4_1002),
    C1003(DeviceStatusCodeType.C4_1003),
    C1004(DeviceStatusCodeType.C4_1004),
    C1005(DeviceStatusCodeType.C4_1005),
    C1006(DeviceStatusCodeType.C4_1006),
    C1007(DeviceStatusCodeType.C4_1007),
    C1008(DeviceStatusCodeType.C4_1008),
    C1009(DeviceStatusCodeType.C4_1009),
    C100B(DeviceStatusCodeType.C4_100B),
    C2008(DeviceStatusCodeType.C4_2008),
    C3002(DeviceStatusCodeType.C4_3002),
    C3003(DeviceStatusCodeType.C4_3003),
    C3004(DeviceStatusCodeType.C4_3004),
    C3007(DeviceStatusCodeType.C4_3007),
    C300A(DeviceStatusCodeType.C4_300A),
    C300D(DeviceStatusCodeType.C4_300D),
    C300E(DeviceStatusCodeType.C4_300E),
    C300F(DeviceStatusCodeType.C4_300F),
    C3010(DeviceStatusCodeType.C4_3010),
    C3011(DeviceStatusCodeType.C4_3011),
    C3014(DeviceStatusCodeType.C4_3014),
    C3015(DeviceStatusCodeType.C4_3015),
    C3016(DeviceStatusCodeType.C4_3016),
    C3017(DeviceStatusCodeType.C4_3017),
    C301A(DeviceStatusCodeType.C4_301A),
    C301B(DeviceStatusCodeType.C4_301B),
    C301D(DeviceStatusCodeType.C4_301D),
    C302A(DeviceStatusCodeType.C4_302A),
    C302C(DeviceStatusCodeType.C4_302C),
    C302D(DeviceStatusCodeType.C4_302D),
    C302E(DeviceStatusCodeType.C4_302E),
    C302F(DeviceStatusCodeType.C4_302F),
    C3031(DeviceStatusCodeType.C4_3031),
    C3032(DeviceStatusCodeType.C4_3032),
    C303C(DeviceStatusCodeType.C4_303C),
    C303E(DeviceStatusCodeType.C4_303E),
    C3044(DeviceStatusCodeType.C4_3044),
    C3046(DeviceStatusCodeType.C4_3046),
    C3047(DeviceStatusCodeType.C4_3047),
    C3048(DeviceStatusCodeType.C4_3048),
    C3049(DeviceStatusCodeType.C4_3049),
    C304A(DeviceStatusCodeType.C4_304A),
    C3053(DeviceStatusCodeType.C4_3053),
    C3054(DeviceStatusCodeType.C4_3054),
    C3055(DeviceStatusCodeType.C4_3055),
    C3056(DeviceStatusCodeType.C4_3056),
    C3057(DeviceStatusCodeType.C4_3057),
    C3058(DeviceStatusCodeType.C4_3058),
    C3059(DeviceStatusCodeType.C4_3059),
    C305a(DeviceStatusCodeType.C4_305a),
    C305b(DeviceStatusCodeType.C4_305b),
    C305c(DeviceStatusCodeType.C4_305c),
    C305d(DeviceStatusCodeType.C4_305d),
    C305e(DeviceStatusCodeType.C4_305e),
    C305f(DeviceStatusCodeType.C4_305f),
    C3070(DeviceStatusCodeType.C4_3070),
    C3080(DeviceStatusCodeType.C4_3080),
    C3081(DeviceStatusCodeType.C4_3081),
    C3082(DeviceStatusCodeType.C4_3082),
    C3083(DeviceStatusCodeType.C4_3083),
    C3084(DeviceStatusCodeType.C4_3084),
    C4001(DeviceStatusCodeType.C4_4001),
    C4002(DeviceStatusCodeType.C4_4002),
    C4004(DeviceStatusCodeType.C4_4004),
    C4005(DeviceStatusCodeType.C4_4005),
    C4006(DeviceStatusCodeType.C4_4006),
    C400C(DeviceStatusCodeType.C4_400C),
    C5001(DeviceStatusCodeType.C4_5001),
    C5002(DeviceStatusCodeType.C4_5002),
    C5003(DeviceStatusCodeType.C4_5003),
    C5004(DeviceStatusCodeType.C4_5004),
    C5007(DeviceStatusCodeType.C4_5007),
    C5008(DeviceStatusCodeType.C4_5008),
    C5009(DeviceStatusCodeType.C4_5009),
    C500A(DeviceStatusCodeType.C4_500A),
    C5011(DeviceStatusCodeType.C4_5011),
    C501A(DeviceStatusCodeType.C4_501A),
    C502B(DeviceStatusCodeType.C4_502B),
    C502C(DeviceStatusCodeType.C4_502C),
    C503C(DeviceStatusCodeType.C4_503C),
    C503D(DeviceStatusCodeType.C4_503D),
    C503e(DeviceStatusCodeType.C4_503e),
    C503F(DeviceStatusCodeType.C4_503F),
    C5043(DeviceStatusCodeType.C4_5043),
    C5510(DeviceStatusCodeType.C4_5510),
    C5511(DeviceStatusCodeType.C4_5511),
    C5512(DeviceStatusCodeType.C4_5512),
    C5513(DeviceStatusCodeType.C4_5513),
    C5514(DeviceStatusCodeType.C4_5514),
    C5520(DeviceStatusCodeType.C4_5520),
    C5521(DeviceStatusCodeType.C4_5521),
    C5522(DeviceStatusCodeType.C4_5522),
    C5523(DeviceStatusCodeType.C4_5523),
    C5524(DeviceStatusCodeType.C4_5524),
    C5525(DeviceStatusCodeType.C4_5525),
    C5526(DeviceStatusCodeType.C4_5526),
    C5527(DeviceStatusCodeType.C4_5527),
    C5528(DeviceStatusCodeType.C4_5528),
    C5529(DeviceStatusCodeType.C4_5529),
    C552A(DeviceStatusCodeType.C4_552A),
    C552B(DeviceStatusCodeType.C4_552B),
    C552C(DeviceStatusCodeType.C4_552C),
    C552D(DeviceStatusCodeType.C4_552D),
    C552E(DeviceStatusCodeType.C4_552E),
    C552F(DeviceStatusCodeType.C4_552F),
    C5530(DeviceStatusCodeType.C4_5530),
    C5531(DeviceStatusCodeType.C4_5531),
    C5532(DeviceStatusCodeType.C4_5532),
    C5540(DeviceStatusCodeType.C4_5540),
    C5541(DeviceStatusCodeType.C4_5541),
    C5542(DeviceStatusCodeType.C4_5542),
    C5543(DeviceStatusCodeType.C4_5543),
    C5544(DeviceStatusCodeType.C4_5544),
    C5545(DeviceStatusCodeType.C4_5545),
    C5546(DeviceStatusCodeType.C4_5546),
    C5547(DeviceStatusCodeType.C4_5547),
    C5548(DeviceStatusCodeType.C4_5548),
    C5550(DeviceStatusCodeType.C4_5550),
    C5551(DeviceStatusCodeType.C4_5551),
    C5552(DeviceStatusCodeType.C4_5552),
    C5553(DeviceStatusCodeType.C4_5553),
    C5554(DeviceStatusCodeType.C4_5554),
    C5555(DeviceStatusCodeType.C4_5555),
    C5556(DeviceStatusCodeType.C4_5556),
    C5557(DeviceStatusCodeType.C4_5557),
    C5558(DeviceStatusCodeType.C4_5558),
    C5559(DeviceStatusCodeType.C4_5559),
    C555A(DeviceStatusCodeType.C4_555A),
    C555B(DeviceStatusCodeType.C4_555B),
    C555C(DeviceStatusCodeType.C4_555C),
    C555D(DeviceStatusCodeType.C4_555D),
    C555E(DeviceStatusCodeType.C4_555E),
    C555F(DeviceStatusCodeType.C4_555F),
    C5560(DeviceStatusCodeType.C4_5560),
    C5601(DeviceStatusCodeType.C4_5601),
    C5602(DeviceStatusCodeType.C4_5602),
    C5603(DeviceStatusCodeType.C4_5603),
    C5604(DeviceStatusCodeType.C4_5604),
    C5605(DeviceStatusCodeType.C4_5605),
    C5606(DeviceStatusCodeType.C4_5606),
    C5607(DeviceStatusCodeType.C4_5607),
    C5608(DeviceStatusCodeType.C4_5608),
    C5609(DeviceStatusCodeType.C4_5609),
    C5610(DeviceStatusCodeType.C4_5610),
    C5611(DeviceStatusCodeType.C4_5611),
    C5612(DeviceStatusCodeType.C4_5612),
    C5613(DeviceStatusCodeType.C4_5613),
    C5614(DeviceStatusCodeType.C4_5614),
    C5615(DeviceStatusCodeType.C4_5615),
    C560A(DeviceStatusCodeType.C4_560A),
    C560B(DeviceStatusCodeType.C4_560B),
    C560C(DeviceStatusCodeType.C4_560C),
    C560D(DeviceStatusCodeType.C4_560D),
    C560E(DeviceStatusCodeType.C4_560E),
    C560F(DeviceStatusCodeType.C4_560F),
    C5650(DeviceStatusCodeType.C4_5650),
    C5651(DeviceStatusCodeType.C4_5651),
    C5652(DeviceStatusCodeType.C4_5652),
    C5653(DeviceStatusCodeType.C4_5653),
    C5654(DeviceStatusCodeType.C4_5654),
    C800A(DeviceStatusCodeType.C4_800A),
    C800B(DeviceStatusCodeType.C4_800B),
    C800C(DeviceStatusCodeType.C4_800C),
    C800D(DeviceStatusCodeType.C4_800D),
    C800E(DeviceStatusCodeType.C4_800E),
    C800F(DeviceStatusCodeType.C4_800F),
    C8010(DeviceStatusCodeType.C4_8010),
    C8011(DeviceStatusCodeType.C4_8011),
    C8012(DeviceStatusCodeType.C4_8012),
    C8013(DeviceStatusCodeType.C4_8013),
    C8014(DeviceStatusCodeType.C4_8014),
    C8015(DeviceStatusCodeType.C4_8015),
    C8016(DeviceStatusCodeType.C4_8016),
    C8017(DeviceStatusCodeType.C4_8017),
    C8018(DeviceStatusCodeType.C4_8018),
    C8019(DeviceStatusCodeType.C4_8019),
    C801A(DeviceStatusCodeType.C4_801A),
    C801B(DeviceStatusCodeType.C4_801B),
    C801C(DeviceStatusCodeType.C4_801C),
    C801D(DeviceStatusCodeType.C4_801D),
    C801E(DeviceStatusCodeType.C4_801E),
    C801F(DeviceStatusCodeType.C4_801F),
    C8020(DeviceStatusCodeType.C4_8020),
    C8021(DeviceStatusCodeType.C4_8021),
    C8022(DeviceStatusCodeType.C4_8022),
    C8023(DeviceStatusCodeType.C4_8023),
    C8024(DeviceStatusCodeType.C4_8024),
    C8025(DeviceStatusCodeType.C4_8025),
    C8026(DeviceStatusCodeType.C4_8026),
    C8027(DeviceStatusCodeType.C4_8027),
    C8028(DeviceStatusCodeType.C4_8028),
    C8029(DeviceStatusCodeType.C4_8029),
    C802A(DeviceStatusCodeType.C4_802A),
    C802B(DeviceStatusCodeType.C4_802B),
    C802C(DeviceStatusCodeType.C4_802C),
    C802D(DeviceStatusCodeType.C4_802D),
    C802E(DeviceStatusCodeType.C4_802E),
    C802F(DeviceStatusCodeType.C4_802F),
    C8030(DeviceStatusCodeType.C4_8030),
    C8031(DeviceStatusCodeType.C4_8031),
    C8032(DeviceStatusCodeType.C4_8032),
    C8033(DeviceStatusCodeType.C4_8033),
    C8034(DeviceStatusCodeType.C4_8034),
    C8035(DeviceStatusCodeType.C4_8035),
    C8036(DeviceStatusCodeType.C4_8036),
    C8037(DeviceStatusCodeType.C4_8037),
    C8038(DeviceStatusCodeType.C4_8038),
    C8039(DeviceStatusCodeType.C4_8039),
    C803A(DeviceStatusCodeType.C4_803A),
    C803B(DeviceStatusCodeType.C4_803B),
    C803C(DeviceStatusCodeType.C4_803C),
    C803D(DeviceStatusCodeType.C4_803D),
    C803E(DeviceStatusCodeType.C4_803E),
    C803F(DeviceStatusCodeType.C4_803F),
    C8040(DeviceStatusCodeType.C4_8040),
    C8041(DeviceStatusCodeType.C4_8041),
    C8042(DeviceStatusCodeType.C4_8042),
    C8043(DeviceStatusCodeType.C4_8043),
    C8044(DeviceStatusCodeType.C4_8044),
    C8045(DeviceStatusCodeType.C4_8045),
    C8046(DeviceStatusCodeType.C4_8046),
    C8047(DeviceStatusCodeType.C4_8047),
    C8048(DeviceStatusCodeType.C4_8048),
    C8049(DeviceStatusCodeType.C4_8049),
    C804A(DeviceStatusCodeType.C4_804A),
    C804B(DeviceStatusCodeType.C4_804B),
    C804C(DeviceStatusCodeType.C4_804C),
    C804D(DeviceStatusCodeType.C4_804D),
    C804E(DeviceStatusCodeType.C4_804E),
    C804F(DeviceStatusCodeType.C4_804F),
    C8050(DeviceStatusCodeType.C4_8050),
    C8051(DeviceStatusCodeType.C4_8051),
    C8052(DeviceStatusCodeType.C4_8052),
    C8053(DeviceStatusCodeType.C4_8053),
    C8054(DeviceStatusCodeType.C4_8054),
    C8055(DeviceStatusCodeType.C4_8055),
    C8056(DeviceStatusCodeType.C4_8056),
    C8057(DeviceStatusCodeType.C4_8057),
    C8058(DeviceStatusCodeType.C4_8058),
    C8059(DeviceStatusCodeType.C4_8059),
    C805A(DeviceStatusCodeType.C4_805A),
    C9011(DeviceStatusCodeType.C4_9011),
    ;

    private final DeviceStatusCodeType codeType;

    WinlineErrorCode(DeviceStatusCodeType codeType) {
        this.codeType = codeType;
    }

    public static WinlineErrorCode valueOfByStr(String codeStr) {
        if (codeStr == null || codeStr.isEmpty()) {
            return UNKNOWN;
        }

        for (WinlineErrorCode code : values()) {
            if (codeStr.equalsIgnoreCase(code.name().substring(1))) {
                return code;
            }
        }
        return UNKNOWN;
    }

}
