package com.cdz360.iot.gw.model.gw;

import com.cdz360.base.utils.JsonUtils;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@Accessors(chain = true)
public class MqttProperties {

    private boolean enable = false; // 暂时禁用
    private String url;
    private String clientId;
    private String username;
    private String password;
    private String topic;

//    public String getUrl() {
//        return url;
//    }
//
//    public MqttProperties setUrl(String url) {
//        this.url = url;
//        return this;
//    }
//
//    public String getClientId() {
//        return clientId;
//    }
//
//    public MqttProperties setClientId(String clientId) {
//        this.clientId = clientId;
//        return this;
//    }
//
//    public String getUsername() {
//        return username;
//    }
//
//    public MqttProperties setUsername(String username) {
//        this.username = username;
//        return this;
//    }
//
//    public String getPassword() {
//        return password;
//    }
//
//    public MqttProperties setPassword(String password) {
//        this.password = password;
//        return this;
//    }
//
//    public String getTopic() {
//        return topic;
//    }
//
//    public MqttProperties setTopic(String topic) {
//        this.topic = topic;
//        return this;
//    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MqttProperties that = (MqttProperties) o;
        return Objects.equals(url, that.url) &&
            Objects.equals(clientId, that.clientId) &&
            Objects.equals(username, that.username) &&
            Objects.equals(password, that.password) &&
            Objects.equals(topic, that.topic);
    }

    @Override
    public int hashCode() {
        return Objects.hash(url, clientId, username, password, topic);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
