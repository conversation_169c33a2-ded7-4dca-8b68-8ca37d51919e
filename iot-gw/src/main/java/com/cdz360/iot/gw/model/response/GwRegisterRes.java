package com.cdz360.iot.gw.model.response;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

public class GwRegisterRes {
    @JsonProperty(value = "n")
    String gwno;

    @JsonProperty(value = "password")
    String passcode;

    String mqClientId;


    String mqUrl;

    String mqUsername;

    @JsonProperty(value = "mqPassword")
    String mqPasscode;

    String mqTopic;


    public String getGwno() {
        return gwno;
    }

    public GwRegisterRes setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public String getPasscode() {
        return passcode;
    }

    public GwRegisterRes setPasscode(String passcode) {
        this.passcode = passcode;
        return this;
    }

    public String getMqClientId() {
        return mqClientId;
    }

    public GwRegisterRes setMqClientId(String mqClientId) {
        this.mqClientId = mqClientId;
        return this;
    }

    public String getMqUrl() {
        return mqUrl;
    }

    public GwRegisterRes setMqUrl(String mqUrl) {
        this.mqUrl = mqUrl;
        return this;
    }

    public String getMqUsername() {
        return mqUsername;
    }

    public GwRegisterRes setMqUsername(String mqUsername) {
        this.mqUsername = mqUsername;
        return this;
    }

    public String getMqPasscode() {
        return mqPasscode;
    }

    public GwRegisterRes setMqPasscode(String mqPasscode) {
        this.mqPasscode = mqPasscode;
        return this;
    }

    public String getMqTopic() {
        return mqTopic;
    }

    public GwRegisterRes setMqTopic(String mqTopic) {
        this.mqTopic = mqTopic;
        return this;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
