package com.cdz360.iot.gw.model.type;

public enum ACErrorCode {

    ERROR_CODE_5(5, "交流-急停故障"),
    ERROR_CODE_10(10, "交流-枪高温"),
    ERROR_CODE_12(12, "交流-枪连接异常"),
    ERROR_CODE_14(14, "交流-枪锁异常"),
    ERROR_CODE_18(18, "交流-交流接触器"),
    ERROR_CODE_21(21, "交流-枪体高温"),
    ERROR_CODE_23(23, "交流-枪过流"),
    ERROR_CODE_29(29, "交流-充电电流为0"),
    ERROR_CODE_58(58, "交流-枪低温"),
    ERROR_CODE_59(59, "交流-桩温度传感器故障"),
    ERROR_CODE_68(68, "交流-枪温度传感器故障"),
    ERROR_CODE_69(69, "交流-S2开关异常"),
    ERROR_CODE_73(73, "交流-门禁"),
    ERROR_CODE_74(74, "交流-网络通讯"),
    ERROR_CODE_75(75, "交流-DWIN通讯故障"),
    ERROR_CODE_76(76, "交流-读卡器"),
    ERROR_CODE_79(79, "交流-交流欠压"),
    ERROR_CODE_80(80, "交流-交流过压"),
    ERROR_CODE_83(83, "交流-电表通讯"),
    ERROR_CODE_84(84, "交流-CP电压采样异常"),
    ERROR_CODE_86(86, "交流-充电中桩掉电");

    private final int code;
    private final String msg;

    ACErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ACErrorCode valueOf(int code) {
        for (ACErrorCode type : ACErrorCode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }

        return null;
    }

    public static String getMsgByCode(int code) {
        ACErrorCode errorCode = ACErrorCode.valueOf(code);
        if (errorCode != null) {
            return errorCode.msg;
        }

        return "";
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
