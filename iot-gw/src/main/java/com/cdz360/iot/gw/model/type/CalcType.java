package com.cdz360.iot.gw.model.type;

/**
 * 费用抵扣模式
 *
 * @Author: <PERSON>
 * @Date: 2019/10/30 16:41
 */
public enum CalcType {
    UNKNOWN((byte) 0x00),

    // 仅金额抵扣
    MONEY((byte) 0x01),
    // 仅电量抵扣
    POWER((byte) 0x03),
    // 金额抵扣服务费, 电量抵扣电费
    BOTH((byte) 0x02);

    private final byte code;

    CalcType(byte code) {
        this.code = code;
    }

    public static CalcType valueOf(byte code) {
        for (CalcType type : CalcType.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }

        return UNKNOWN;
    }

    public byte getCode() {
        return code;
    }
}
