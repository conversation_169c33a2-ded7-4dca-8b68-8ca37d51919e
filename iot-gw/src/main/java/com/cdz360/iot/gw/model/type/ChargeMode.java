package com.cdz360.iot.gw.model.type;

public enum ChargeMode {
    FULL(0x00, "充满"),
    AMOUNT(0x01, "按金额充"),
    KWH(0x02, "按电量充"),
    TIME(0x03, "按时间充");

    private int code;
    private String desc;

    ChargeMode(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ChargeMode getByCode(byte code) {
        for (ChargeMode type : ChargeMode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return null;
    }
}
