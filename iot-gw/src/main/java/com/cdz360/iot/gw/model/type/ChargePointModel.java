package com.cdz360.iot.gw.model.type;

import com.cdz360.base.model.base.type.SupplyType;

public enum ChargePointModel {
    UNKNOWN(SupplyType.BOTH, 0),
    Elmo_Virtual2(SupplyType.DC, 2),
    YLUXD160KE(SupplyType.DC, 2),
    ;

    private final SupplyType type;
    private final int plugNum;

    ChargePointModel(SupplyType type, int plugNum) {
        this.type = type;
        this.plugNum = plugNum;
    }

    public static ChargePointModel parseOf(Object modelIn) {
        String model = null;
        if (modelIn instanceof String) {
            model = ((String) modelIn);
        }

        for (ChargePointModel temp : values()) {
            if (temp.name().equals(model)) {
                return temp;
            }
        }
        return ChargePointModel.UNKNOWN;
    }

    public SupplyType getType() {
        return type;
    }
}
