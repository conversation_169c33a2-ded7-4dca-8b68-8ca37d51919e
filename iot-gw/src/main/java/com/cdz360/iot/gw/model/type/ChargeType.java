package com.cdz360.iot.gw.model.type;

public enum ChargeType {
    //从现金帐户扣费
    Cash((byte) 0x01),

    //如果是电量帐户则抵扣电量，如果是现金帐户则抵扣服务费
    AutoSwitch((byte) 0x02),

    //从电量帐户抵扣
    Electricity((byte) 0x03);


    private final byte code;

    ChargeType(byte code) {
        this.code = code;
    }

    public byte getCode() {
        return code;
    }

    public static ChargeType codeOf(byte code) {
        for (ChargeType type : ChargeType.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return null;
    }
}
