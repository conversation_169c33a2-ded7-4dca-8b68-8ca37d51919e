package com.cdz360.iot.gw.model.type;

public class ConstantCollections {

    public static final byte OK = 0x00;
    public static final byte ERROR = 0x01;

    // 使用枚举 SupplyType
//    public static final byte AC = 0x00;
//    public static final byte DC = 0x01;

    public static final byte AUTH_OK = 0x00;//鉴权成功
    public static final byte AUTH_FAILED = 0x10;//鉴权失败(账号拉黑/停用/账户异常)
    public static final byte AUTH_BALANCE_INSUFFICIENT = 0x11;//余额不足
    public static final byte AUTH_NO_PERMISSION = 0x12;//无权限充电（卡、VIN不能在当前场站使用）
    public static final byte AUTH_PLUG_IN_USING = 0x13;//枪头有预约或者被占用
    public static final byte AUTH_SITE_STATUS_ABNORMAL = 0x14;//场站状态异常(维护中)
    public static final byte AUTH_EVSE_NOT_BIND = 0x15;//桩未绑定到场站
    public static final byte AUTH_EVSE_NO_TEMPLATE = 0x16;//桩未下发计费模板
    public static final byte AUTH_SITE_POWER_LIMIT_REACHED = 0x17;//场站功率达到上限

    public static final int CLOUD_AUTH_UNKNOWN_FAILURE = 2102;//其他错误
    public static final int KEY_RES_CODE_ACCOUNT_ERROR = 2100;  // 通用的账号异常
    public static final int KEY_RES_CODE_BALANCE_ERROR = 2101;  // 余额异常(不足)
    public static final int KEY_RES_CODE_AUTH_FAIL = 2102;  // 鉴权失败(没有充电权限)
    public static final int KEY_RES_CODE_AUTH_FAIL_RESERVED = 2103; // 鉴权失败-枪头有其他预约

    public static final byte CHARGE_BY_OFFLINE_CARD = 0x01;
    public static final byte CHARGE_BY_NO_CARD = 0x02;
    public static final byte CHARGE_BY_ONLINE_CARD = 0x11;
    public static final byte CHARGE_BY_VIN = 0x12;
    // public static final byte CHARGE_BY_OTHER =  0x01;

    public static final int PLUG_FIRST_NO = 1;//桩1枪的索引

    public static final byte REGISTER_SIGN_WRONG = 0x01;//签名错误
    public static final byte REGISTER_NETWORK_EXCEPTION = 0x03;//通信异常


    public final static String GW_CFG_VERSION = "CFG:VERSION";
    public final static String GW_CFG_CARD = "CFG:CARD";
    public final static String GW_CFG_VIN = "CFG:VIN";

    public final static String GW_CLOUD_OPERATIONS_VERSION = "OPRATS:VERSION";

}
