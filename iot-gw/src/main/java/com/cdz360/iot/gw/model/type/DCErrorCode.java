package com.cdz360.iot.gw.model.type;

public enum DCErrorCode {
    ERROR_CODE_1(1, "充电机绝缘故障"),
    ERROR_CODE_2(2, "BMS绝缘"),
    ERROR_CODE_3(3, "充电机泄放电路故障"),
    ERROR_CODE_4(4, "漏电故障"),
    ERROR_CODE_05(5, "急停故障"),
    ERROR_CODE_9(9, ""),
    ERROR_CODE_10(10, "连接器故障（导引电路检测到故障）—— 枪高温"),
    ERROR_CODE_11(11, "连接器故障（导引电路检测到故障）—— 枪锁反馈"),
    ERROR_CODE_12(12, "连接器故障（导引电路检测到故障）—— CC1信号"),
    ERROR_CODE_13(13, "充电机其他故障"),
    ERROR_CODE_14(14, "电子锁锁定错误"),
    ERROR_CODE_15(15, "K1K2外侧电压检测失败1"),
    ERROR_CODE_16(16, "K1K2外侧电压检测失败2"),
    ERROR_CODE_17(17, "预充电电压调整失败"),
    ERROR_CODE_18(18, "输出接触器故障检测"),
    ERROR_CODE_19(19, "电池反接"),
    ERROR_CODE_20(20, "充电机过温 —— 直流模块过温/通讯故障"),
    ERROR_CODE_21(21, "充电机内部过温 —— 箱体温度"),
    ERROR_CODE_22(22, "充电机电量不能传送"),
    ERROR_CODE_23(23, "充电机检测到充电电流不匹配"),
    ERROR_CODE_24(24, "充电机检测到充电电压异常"),
    ERROR_CODE_25(25, "输出短路"),
    ERROR_CODE_26(26, "主从机通讯故障"),
    ERROR_CODE_27(27, "桩体风扇故障"),
    // ERROR_CODE_27(27, ""),
    // ERROR_CODE_28(28, ""),
    ERROR_CODE_29(29, ""),
    ERROR_CODE_30(30, "输出连接器过温"),
    ERROR_CODE_31(31, "BMS元件，输出连接器过温"),
    ERROR_CODE_32(32, "连接器故障（导引电路检测到故障，充电连接器）—— BMS"),
    ERROR_CODE_33(33, "电池组温度过高"),
    ERROR_CODE_34(34, "车辆接触器粘连(高压继电器)"),
    ERROR_CODE_35(35, "CC2电压检测故障"),
    ERROR_CODE_36(36, "BST报告其他故障"),
    ERROR_CODE_37(37, "BMS检测到充电电流过大"),
    ERROR_CODE_38(38, "BMS检测到充电电压异常"),
    ERROR_CODE_39(39, "电池单体电压过高"),
    ERROR_CODE_40(40, "电池单体电压过低"),
    ERROR_CODE_41(41, "SOC过高"),
    ERROR_CODE_42(42, "SOC过低"),
    ERROR_CODE_43(43, "蓄电池输出连接器状态"),
    ERROR_CODE_44(44, "BMS电池过温（BSM）"),
    ERROR_CODE_45(45, "BMS过流（BSM）"),
    ERROR_CODE_46(46, "BMS绝缘"),
    ERROR_CODE_47(47, "BMS主动停充"),
    ERROR_CODE_49(49, ""),
    ERROR_CODE_50(50, "接收超时BRM"),
    ERROR_CODE_51(51, "接收超时 BCP"),
    ERROR_CODE_52(52, "接收超时 BRO"),
    ERROR_CODE_53(53, "接收超时 BCS"),
    ERROR_CODE_54(54, "接收超时 BCL"),
    ERROR_CODE_55(55, "接收超时 BST"),
    ERROR_CODE_56(56, "接收超时 BSD"),
    ERROR_CODE_57(57, "接收超时 BHM/BRM"),
    ERROR_CODE_60(60, "接收超时 CRM_00"),
    ERROR_CODE_61(61, "接收超时 CRM_AA"),
    ERROR_CODE_62(62, "接收超时 CTS， CML"),
    ERROR_CODE_63(63, "接收超时 CRO"),
    ERROR_CODE_64(64, "接收超时 CCS"),
    ERROR_CODE_65(65, "接收超时 CST"),
    ERROR_CODE_66(66, "接收超时 CSD"),
    ERROR_CODE_67(67, "其他"),
    ERROR_CODE_69(69, ""),
    ERROR_CODE_70(70, "烟雾报警"),
    ERROR_CODE_71(71, "水浸检测"),
    ERROR_CODE_72(72, "倾斜检测"),
    ERROR_CODE_73(73, "开门检测"),
    ERROR_CODE_74(74, "后台通讯"),
    ERROR_CODE_75(75, "屏通讯"),
    ERROR_CODE_76(76, "读卡器"),
    ERROR_CODE_77(77, "输入接触器检测"),
    ERROR_CODE_78(78, "功率分配接触器故障"),
    ERROR_CODE_79(79, "（模块）输入过欠压"),
    ERROR_CODE_80(80, "多枪，模块输出过欠压，不均流"),
    ERROR_CODE_81(81, "UI —— 安全管理板通讯中断"),
    ERROR_CODE_82(82, "安全管理板 —— UI通讯中断"),
    ERROR_CODE_83(83, "安全管理板 —— 接触器通讯中断"),
    ERROR_CODE_84(84, "防雷器故障"),
    ERROR_CODE_85(85, "UI板或安全管理板5V跌落"),
    ERROR_CODE_86(86, ""),
    ERROR_CODE_87(87, "UI —— 充电控制板通讯中断"),
    ERROR_CODE_88(88, "充电控制板 —— UI通讯中断"),
    ERROR_CODE_89(89, "安全管理板 —— 充电控制板通讯中断"),
    ERROR_CODE_90(90, "充电控制板 —— 安全管理板通讯中断"),
    ERROR_CODE_91(91, "直流功率表通讯故障"),
    ERROR_CODE_92(92, "绝缘检测表通讯故障"),
    ERROR_CODE_93(93, "电池充电安全监测"),
    ERROR_CODE_94(94, "泄放电阻温度"),
    ERROR_CODE_95(95, "泄放电路驱动故障"),
    ERROR_CODE_96(96, "与BMS通讯中断（3次超时）"),
    ERROR_CODE_97(97, "充电前功率组未就绪"),
    ERROR_CODE_98(98, "BMS辅助电源电压故障"),
    ERROR_CODE_99(99, "VIN码未找到"),
    ERROR_CODE_100(100, "PC03板内存不足"),
    ERROR_CODE_101(101, "交流进线断电"),
    ERROR_CODE_102(102, "电表电量异常"),
    ERROR_CODE_103(103, "SD卡故障"),
    ERROR_CODE_104(104, "FTP模式配置失败"),
    ERROR_CODE_105(105, "RFID通信超时"),
    ERROR_CODE_106(106, "压力传感器通信超时"),
    ERROR_CODE_107(107, "摄像头通信超时"),
    ERROR_CODE_108(108, "绝缘模块检测电压失败"),
    ERROR_CODE_109(109, "电表电量数据异常"),
    ERROR_CODE_110(110, "急停处理泄放超时"),
    ERROR_CODE_111(111, "急停处理解锁超时"),
    ERROR_CODE_112(112, "正常停止处理泄放超时"),
    ERROR_CODE_113(113, "正常停止处理解锁超时"),
    ERROR_CODE_114(114, "绝缘检测电压"),
    ERROR_CODE_115(115, "绝缘检测数据"),
    ERROR_CODE_116(116, "绝缘检测报警"),
    ERROR_CODE_117(117, "防盗检测"),
    ERROR_CODE_119(119, "单枪，模块输出过欠压，不均流"),
    ERROR_CODE_130(130, "PC01板内存不足"),
    ERROR_CODE_131(131, "PDU通信超时故障"),
    ERROR_CODE_132(132, "PDU控制命令错误"),
    ERROR_CODE_133(133, "调整功率组输出电压超时"),
    ERROR_CODE_134(134, "CTT通道执行断开操作超时"),
    ERROR_CODE_135(135, "CTT通道执行闭合操作超时"),
    ERROR_CODE_136(136, "CTT通道粘连"),
    ERROR_CODE_137(137, "CTT通道驱动失效"),
    ERROR_CODE_138(138, "CTT通道其他故障"),
    ERROR_CODE_139(139, "PDU故障"),
    ERROR_CODE_140(140, "UI板写订单记录故障"),
    ERROR_CODE_141(141, "主动充电安全故障"),
    ERROR_CODE_142(142, "熔断器故障"),
    ERROR_CODE_143(143, "压力过小故障"),
    ERROR_CODE_144(144, "压力过大故障"),
    ERROR_CODE_145(145, "CP 采样板通讯故障"),
    ERROR_CODE_146(146, "电动推杆通讯故障"),
    ERROR_CODE_148(148, "BRM 报文数据异常"),
    ERROR_CODE_149(149, "BCP 报文数据异常"),
    ERROR_CODE_150(150, "接收超时 BRO_00"),
    ERROR_CODE_151(151, "接收超时 BRO_AA"),
    ERROR_CODE_157(157, "充电中有电压无电流"),
    ERROR_CODE_158(158, "车辆最高允许充电电压低于充电机最低输出电压"),
    ERROR_CODE_159(159, "电池电压过低"),
    ERROR_CODE_160(160, "电池电压过高"),
    ERROR_CODE_161(161, "K1K2后端电压大于车辆最高允许电压");

    private final int code;
    private final String msg;

    DCErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static DCErrorCode valueOf(int code) {
        for (DCErrorCode type : DCErrorCode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }

        return null;
    }

    public static String getMsgByCode(int code) {
        DCErrorCode errorCode = DCErrorCode.valueOf(code);
        if (errorCode != null) {
            return errorCode.msg;
        }

        return "";
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
