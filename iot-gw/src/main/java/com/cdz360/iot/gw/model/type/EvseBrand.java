package com.cdz360.iot.gw.model.type;

import lombok.Getter;

@Getter
public enum EvseBrand {

    UNKNOWN("UNKNOWN", "未知"),
    RCD("rcd", "润诚达"),
    WINLINE("winline", "永联"),
    ;

    private final String vendorId;
    private final String desc;

    EvseBrand(String vendorId, String desc) {
        this.vendorId = vendorId;
        this.desc = desc;
    }

    public static EvseBrand getByDesc(String desc) {
        if (desc == null) {
            return UNKNOWN;
        }
        for (EvseBrand brand : EvseBrand.values()) {
            if (brand.getDesc().equals(desc) || brand.name().equals(desc.toUpperCase())) {
                return brand;
            }
        }
        return UNKNOWN;
    }

}
