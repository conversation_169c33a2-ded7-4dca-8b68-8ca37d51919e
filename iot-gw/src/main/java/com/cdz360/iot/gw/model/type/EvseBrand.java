package com.cdz360.iot.gw.model.type;

import java.util.Arrays;
import lombok.Getter;

@Getter
public enum EvseBrand {

    UNKNOWN("UNKNOWN", "未知", new String[]{}),
    RCD("rcd", "润诚达", new String[]{"RUNCHENGDA"}),
    WINLINE("winline", "永联", new String[]{"YONGLIAN", "YONGLIAN_GUOGAIOU"}),
    ;

    private final String vendorId;
    private final String desc;
    private final String[] mappingCodes;

    EvseBrand(String vendorId, String desc, String[] mappingCodes) {
        this.vendorId = vendorId;
        this.desc = desc;
        this.mappingCodes = mappingCodes;
    }

    public static EvseBrand getByDesc(String desc) {
        if (desc == null) {
            return UNKNOWN;
        }

        // 首先检查映射代码
        for (EvseBrand brand : EvseBrand.values()) {
            if (Arrays.asList(brand.getMappingCodes()).contains(desc)) {
                return brand;
            }
        }

        // 然后检查原有的desc和name匹配
        for (EvseBrand brand : EvseBrand.values()) {
            if (brand.getDesc().equals(desc) || brand.name().equals(desc.toUpperCase())) {
                return brand;
            }
        }
        return UNKNOWN;
    }

}
