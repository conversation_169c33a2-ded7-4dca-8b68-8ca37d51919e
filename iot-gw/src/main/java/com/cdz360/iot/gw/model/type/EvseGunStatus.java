package com.cdz360.iot.gw.model.type;


import com.cdz360.base.model.base.type.PlugStatus;

/**
 * <AUTHOR>
 * @since 2019-01-04
 * 桩/枪状态 :云端状态映射
 */
public enum EvseGunStatus {
    FREE("空闲", (byte) 0x00, PlugStatus.IDLE),
    RESERVATION("预约", (byte) 0x01, PlugStatus.CONNECT),
    DISCHARGE("放电", (byte) 0x02, PlugStatus.BUSY),
    CHARGING("充电中", (byte) 0x03, PlugStatus.BUSY),
//    OVER_VOLTAGE("过压", (byte) 0x04, PlugStatus.ERROR),
//    UNDER_VOLTAGE("欠压", (byte) 0x05, PlugStatus.ERROR),
//    OVER_CURRENT("过流", (byte) 0x06, PlugStatus.ERROR),
//    SCRAM("急停", (byte) 0x07, PlugStatus.ERROR),
    CHARGING_COMPLETED("充电完成", (byte) 0x08, PlugStatus.RECHARGE_END),
//    CHARGING_CONNECTION_ABNORMAL("充电连接异常", (byte) 0x09, PlugStatus.ERROR),
//    CHARGER_FAULT("充电机故障", (byte) 0x0A, PlugStatus.ERROR),
//    BMS__FAULT("BMS通信故障", (byte) 0x0B, PlugStatus.ERROR),
    GUN_ATTACHED_VEHICLE("枪已连接到车辆", (byte) 0x0C, PlugStatus.CONNECT),
//    LOCK__ABNORMAL("枪锁异常", (byte) 0x0D, PlugStatus.ERROR),
//    DOOR_ABNORMAL("桩门异常", (byte) 0x0E, PlugStatus.ERROR),
    JOIN("合充", (byte) 0x0F, PlugStatus.JOIN),

    ERROR("故障", (byte) 0xFF, PlugStatus.ERROR);

    private final String desc;
    private final byte code;//桩端状态码
    private final PlugStatus cloudStatus;//云端 枪/桩状态

    EvseGunStatus(String desc, byte code, PlugStatus cloudStatus) {
        this.desc = desc;
        this.code = code;
        this.cloudStatus = cloudStatus;
    }

    public static EvseGunStatus getByCode(byte code) {
        for (EvseGunStatus type : EvseGunStatus.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return ERROR;
    }

    public byte getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public PlugStatus getCloudStatus() {
        return this.cloudStatus;
    }


}
