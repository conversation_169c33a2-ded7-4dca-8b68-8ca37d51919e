package com.cdz360.iot.gw.model.type;

public enum ModuleType {

    UNKNOWN((byte) 0x00),
    DCM_TYPE_YKR_EVR700_15000((byte) 0x01),
    INFY_REG75030((byte) 0x02),
    YKR_EVR600_15000((byte) 0x03),
    HUAWEI_R75020G1((byte) 0x04),
    INFY_REG50040((byte) 0x05),
    INFY_REG75035((byte) 0x06),
    YKR_EVR500_15000((byte) 0x07),
    INFY_REG75050((byte) 0x08),
    EVR330_15000C((byte) 0x09),
    EVR700_20000C((byte) 0x0A),
    REG75040((byte) 0x0B),
    YKR_EVR430_20000C((byte) 0x0C),
    ZTE_ZXD030_T751((byte) 0x0D),
    HUAWEI_R95021G1((byte) 0x0E),
    EVR330_15000_DEGRADE((byte) 0x0F),
    WL_NXR75030H((byte) 0x10),
    ;
    private final byte code;

    ModuleType(byte code) {
        this.code = code;
    }

    public byte getCode() {
        return code;
    }

    public static ModuleType valueOf(byte code) {
        for (ModuleType type : ModuleType.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return UNKNOWN;
    }


}
