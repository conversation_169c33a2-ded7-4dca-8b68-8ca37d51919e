package com.cdz360.iot.gw.model.type;

public enum NetType {
    UNKNOWN((byte) 0x00, "UNKNOWN"),

    ETHERNET((byte) 0x01, "ETHENET"),//原来的版本有个错别字：ETHENET

    //  移动通讯技术：MCT(mobile communication technology)
    WIFI((byte) 0x05, "WIFI"),
    M2G((byte) 0x02, "M2G"),
    M3G((byte) 0x03, "M3G"),
    M4G((byte) 0x04, "M4G"),
    M5G((byte) 0x06, "M5G"),
    ;
    private byte code;
    private String ethName;

    NetType(byte code, String ethName) {
        this.code = code;
        this.ethName = ethName;
    }

    public byte getCode() {
        return code;
    }

    public String getEthName() {
        return ethName;
    }

    public static NetType codeOf(byte code) {
        for (NetType type : NetType.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return NetType.UNKNOWN;
    }
}
