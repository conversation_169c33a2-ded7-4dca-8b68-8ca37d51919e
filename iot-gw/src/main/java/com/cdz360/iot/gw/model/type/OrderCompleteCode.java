package com.cdz360.iot.gw.model.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum OrderCompleteCode implements DcEnum {

    FULL(0, "充满"),
    CAR_STOP(1, "车端停充"),
    MONEY_STOP(2, "金额停充"),
    SOC_STOP(3, "SOC停充"),
    POWER_STOP(4, "电量停充"),
    TIME_STOP(5, "时间停充"),
    MANUAL_STOP_CARD(6, "桩端手动停充-刷卡"),
    MANUAL_STOP_CODE(7, "桩端手动停充-停充码"),
    MANUAL_STOP_BUTTON(8, "桩端手动停充-停充按钮"),
    PLATFORM_STOP(9, "平台停充"),
    ABNORMAL_STOP(0xFF, "异常停充"),
    ;

    private final int code;
    private final String desc;

    OrderCompleteCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderCompleteCode codeOf(byte code) {
        for (OrderCompleteCode type : OrderCompleteCode.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return OrderCompleteCode.ABNORMAL_STOP;
    }

}
