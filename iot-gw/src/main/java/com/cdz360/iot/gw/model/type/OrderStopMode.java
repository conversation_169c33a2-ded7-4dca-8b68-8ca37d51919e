package com.cdz360.iot.gw.model.type;

import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderStopMode {
    /**
     * 停止方式
     */
    private ChargeMode type;

    /**
     * 按金额充时的金额，单位“元”
     */
    private BigDecimal amount;

    /**
     * 按电量充的耗电量，单位KWH
     */
    private BigDecimal kwh;

    /**
     * 按时间充的时间，单位分钟
     */
    private Long time;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
