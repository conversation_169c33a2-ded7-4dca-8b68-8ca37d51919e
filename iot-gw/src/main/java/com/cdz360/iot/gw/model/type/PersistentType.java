package com.cdz360.iot.gw.model.type;

/**
 * <AUTHOR>
 * @Description sqlite保存的数据分类
 * @Date 2019/1/16
 **/
public enum PersistentType {
    EVSE("桩枪","01"),
    ORDER_AUTH_ITEM("订单授权MixId","02"),
    ORDER_CONTEXT("订单详情", "03"),//云端和桩端订单映射（废弃）
    CHARGE_ORDER_FROM_EVASE("订单详情", "05")//订单
    ;

    private final String desc;
    private final String code;
    PersistentType(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }

    public final String getCode(){
        return this.code;
    }
    public final String getDesc(){
        return this.desc;
    }
}
