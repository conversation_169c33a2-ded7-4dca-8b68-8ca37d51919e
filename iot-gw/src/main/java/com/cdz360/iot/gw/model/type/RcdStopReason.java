package com.cdz360.iot.gw.model.type;

import java.util.List;
import lombok.Getter;

@Getter
public enum RcdStopReason {
    /**
     * 0~499 直流桩
     */
    ERROR_CODE_1(1, "到达设定条件停止", "Reach set condition"),
    ERROR_CODE_2(2, "手动停止", "Manual stop"),
    ERROR_CODE_3(3, "BMS请求(接收bst)", "BMS request(recv bst)"),
    ERROR_CODE_4(4, "BST到达soc", "BST reach soc"),
    ERROR_CODE_5(5, "BST达到总电压", "BST reach total voltage"),
    ERROR_CODE_6(6, "BST达到单电压", "BST reach single voltage"),
    ERROR_CODE_7(7, "充电桩主动中止", "The charger stops actively"),
    ERROR_CODE_8(8, "BST绝缘故障", "BST insulate fault"),
    ERROR_CODE_9(9, "输出连接器BST过温故障", "BST overtemperature fault of output connector"),
    ERROR_CODE_10(10, "BST过温", "BST over temperature"),
    ERROR_CODE_11(11, "BST充电连接器故障", "BST link fault"),
    ERROR_CODE_12(12, "BST电池组过温", "BST bat overtemperature"),
    ERROR_CODE_13(13, "高压继电器BST故障", "BST fault of high voltage relay"),
    ERROR_CODE_14(14, "BST检测点2电压检测故障", "BST detection point 2 voltage detection fault"),
    ERROR_CODE_15(15, "其他故障", "BST other fault"),
    ERROR_CODE_16(16, "BST过电流", "BST overcurrent"),
    ERROR_CODE_17(17, "BST电压异常", "BST voltage abnormal"),
    ERROR_CODE_18(18, "连接超时", "Connect timeout"),
    ERROR_CODE_19(19, "电子锁故障", "Electronic lock failure"),
    ERROR_CODE_20(20, "电压不匹配", "Voltage not match"),
    ERROR_CODE_21(21, "K1K2接触器故障", "K1K2 contactor fault"),
    ERROR_CODE_22(22, "剩余电压超限", "Residual voltage overrun"),
    ERROR_CODE_23(23, "检查绝缘超时", "Check insulation timeout"),
    ERROR_CODE_24(24, "绝缘故障", "Insulation fault"),
    ERROR_CODE_25(25, "放电超时", "Discharge timeout"),
    ERROR_CODE_26(26, "电池反接", "BAT reversed"),
    ERROR_CODE_27(27, "电池电压异常", "BAT voltage abnormal"),
    ERROR_CODE_28(28, "充电电压异常", "Charging voltage abnormal"),
    ERROR_CODE_29(29, "枪连接状态异常", "Cable connect state abnormal"),
    ERROR_CODE_30(30, "需求电压异常", "Voltage of demand abnormal"),
    ERROR_CODE_31(31, "需求电流异常", "Current of demand abnormal"),
    ERROR_CODE_32(32, "BSM异常", "BSM abnormal"),
    ERROR_CODE_33(33, "暂停充电超时", "Suspend charging timeout"),
    ERROR_CODE_34(34, "BRO还没准备好", "BRO not ready"),
    ERROR_CODE_35(35, "充电过压", "Charging overvoltage"),
    ERROR_CODE_36(36, "连接异常", "Connection exception"),
    ERROR_CODE_37(37, "VIN码无效", "VIN invalid"),
    ERROR_CODE_38(38, "接收BMS超时", "Recv BMS timeout"),
    ERROR_CODE_39(39, "Recv BEM", "Recv BEM"),
    ERROR_CODE_40(40, "急停停止", "Emergency stop"),
    ERROR_CODE_41(41, "门禁系统停止", "Access control system stopped"),
    ERROR_CODE_42(42, "充电桩过温", "Pile overtemperature"),
    ERROR_CODE_43(43, "枪温度过高", "Cable overtemperature"),
    ERROR_CODE_44(44, "枪温度超高", "Cable overtemperature 2"),
    ERROR_CODE_45(45, "交流过压停止", "AC Overvoltage stop"),
    ERROR_CODE_46(46, "交流欠压停止", "AC Undervoltage stop"),
    ERROR_CODE_47(47, "交流过流停止", "AC Overcurrent stop"),
    ERROR_CODE_48(48, "直流过压停止", "DC Overvoltage stop"),
    ERROR_CODE_49(49, "直流过流停止", "DC Overcurrent stop"),
    ERROR_CODE_50(50, "热敏电阻断开连接", "Thermistor disconnected"),
    ERROR_CODE_51(51, "接触器故障", "Contactor fault"),
    ERROR_CODE_52(52, "连接接触器故障", "Link contactor fault"),
    ERROR_CODE_53(53, "PM通信故障", "PM comm failure"),
    ERROR_CODE_54(54, "PM故障", "PM failure"),
    ERROR_CODE_55(55, "PM输出短路", "PM output short circuit"),
    ERROR_CODE_56(56, "PM地址错误(重复)", "PM address error (duplicate)"),
    ERROR_CODE_57(57, "分时计费错误", "Time period set error"),
    ERROR_CODE_58(58, "电表故障", "Electric meter failure"),
    ERROR_CODE_59(59, "地锁故障", "Parking lock failure"),
    ERROR_CODE_60(60, "桩掉电", "Pile powerdown"),
    ERROR_CODE_61(61, "雾霾探测异常", "Smog detection abnormal"),
    ERROR_CODE_62(62, "接线头超温", "Wiring head over temperature"),
    ERROR_CODE_63(63, "同充启动异常", "Double charge start abnormal"),
    ERROR_CODE_64(64, "远程软复位", "Remote software reset"),
    ERROR_CODE_65(65, "远程硬复位", "Remote hardware reset"),
    ERROR_CODE_66(66, "远程", "Remote"),
    ERROR_CODE_67(67, "由于startTransaction.conf中的授权状态，事务停止",
        "Start Transaction deauthorize"),
    ERROR_CODE_68(68, "平台下发解锁，接触器停止", "Unlock Connector command"),
    ERROR_CODE_69(69, "CC状态异常", "CC Status Error"),

    /**
     * >=500 交流桩
     */
    ERROR_CODE_500(500, "紧急停止", "EmergencyStop"),
    ERROR_CODE_501(501, "车辆失联", "EVDisconnected"),
    ERROR_CODE_502(502, "远程硬件复位", "HardReset"),
    ERROR_CODE_503(503, "本地停止", "Local"),
    ERROR_CODE_504(504, "其他", "Other"),
    ERROR_CODE_505(505, "掉电故障", "PowerLoss"),
    ERROR_CODE_506(506, "本地硬件重启", "Reboot"),
    ERROR_CODE_507(507, "远程控制", "Remote"),
    ERROR_CODE_508(508, "远程软复位", "SoftReset"),
    ERROR_CODE_509(509, "远程解锁接触器", "UnlockCommand"),
    ERROR_CODE_510(510, "未授权停止", "DeAuthorized"),
    ERROR_CODE_511(511, "电表故障停止", "MeterFault"),
    ERROR_CODE_512(512, "接触器故障停止", "ContactorFault"),
    ERROR_CODE_513(513, "漏电流保护停止", "EarthLeakageProtection"),
    ERROR_CODE_514(514, "输入过压停止", "InputOvervoltage"),
    ERROR_CODE_515(515, "输出过流停止", "OutputOverCurrent"),
    ERROR_CODE_516(516, "到达设定条件停止", "ReachSetCondition"),
    ERROR_CODE_517(517, "账户余额不足停止", "InsufficientBalance"),
    ERROR_CODE_518(518, "电子锁故障停止", "ElectronicLockFault"),
    ERROR_CODE_519(519, "枪容量识别出错停止", "CableRextFault"),
    ERROR_CODE_520(520, "欠压故障", "PowerQuality"),
    ERROR_CODE_521(521, "充电枪未连接", "CableNoConnected"),
    ERROR_CODE_522(522, "车辆请求停止", "EVRequestStop"),
    ERROR_CODE_523(523, "连接异常停止", "ConnectionException"),
    ERROR_CODE_524(524, "充电桩不可用", "PileDisable"),
    ERROR_CODE_525(525, "充电桩过温停止", "Overtemperature"),
    ERROR_CODE_526(526, "PTB数据错误停止", "PTBDataErrors"),
    ERROR_CODE_527(527, "PEN断线", "PENBroken"),
    ;

    private final int code;
    private final String desc;
    private final String msg;

    RcdStopReason(int code, String desc, String msg) {
        this.code = code;
        this.desc = desc;
        this.msg = msg;
    }

    public static boolean abnormalReason(RcdStopReason reason) {
        if (reason == null) {
            return false;
        }
        List<Integer> normal = List.of(ERROR_CODE_1.code, ERROR_CODE_66.code);
        return normal.contains(reason.code) ? false : true;
    }

    public static RcdStopReason valueOfMsg(String msg) {
        for (RcdStopReason type : RcdStopReason.values()) {
            if (type.getMsg().equals(msg)) {
                return type;
            }
        }
        return null;
    }

}
