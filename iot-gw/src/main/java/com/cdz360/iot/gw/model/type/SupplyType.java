/*
package com.cdz360.iot.gw.model.type;

public enum SupplyType {
    AC(0x00), // 交流

    DC(0x01), // 直流

    BOTH(0x02)    // 交直一体
    ;

    private final int code;

    SupplyType(int code) {
        this.code = code;
    }

    public static SupplyType codeOf(int code) {
        for (SupplyType cat : values()) {
            if (cat.code == code) {
                return cat;
            }
        }
        return null;
    }
}
*/
