package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.ApiUrl;
import lombok.Data;

@Data
public class AuthData {
    //请求api url
    private String url = ApiUrl.URL_REQUEST_AUTH;
    private String evseNo; // 桩编号
    private int plugId;   // 枪编号
    private String vin;
    private byte authType; // 鉴权类型
    private String accountNo; // 账号

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
