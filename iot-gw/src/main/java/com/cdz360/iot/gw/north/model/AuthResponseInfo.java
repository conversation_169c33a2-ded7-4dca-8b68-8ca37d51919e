package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class AuthResponseInfo {
    /**
     * 金额扣款账户总余额, 单位'元'
     */
    private BigDecimal amount;

    /**
     * 电量扣款账户总余额, 单位'kwh'
     */
    private BigDecimal power;

    /**
     * 车牌号. VIN码充电时云端要返回车牌号信息
     */
    private String carNo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}