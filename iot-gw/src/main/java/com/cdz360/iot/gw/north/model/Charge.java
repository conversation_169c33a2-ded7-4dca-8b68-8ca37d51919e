package com.cdz360.iot.gw.north.model;


import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class Charge {

    private int code;//价格编码, 取值范围 0 ~ 255, 不能重复

    // 计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.
    private String startTime;
    //	计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间
    private String stopTime;

    // 电费单价, 单位'元'
    private BigDecimal elecPrice;

    // 服务费单价, 单位'元'
    private BigDecimal servPrice;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}

