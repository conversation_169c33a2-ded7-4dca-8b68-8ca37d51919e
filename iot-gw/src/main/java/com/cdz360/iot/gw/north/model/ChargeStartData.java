package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Author: Nathan
 * @Date: 2019/10/31 13:38
 */
@Data
public class ChargeStartData {
    private String seqNo;

    private String evseNo;

    private int plugId;

    private String orderNo;

    // 开启类型
    private OrderStartType startType;

    // 账户
    private String accountNo;

    // 账户总可用余额, 单位'元'
    private BigDecimal totalAmount;

    // 可实时扣费金额, 单位'元'
    private BigDecimal frozenAmount;

    private OrderStopMode stopMode;

    // 停充码
    private String stopCode;

    // 车牌号
    private String carNo;

    // 功率限制
    private Integer power;

    // SOC限制
    private Integer soc;

    // 差异化价格信息
    private ChargePriceVo price;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
