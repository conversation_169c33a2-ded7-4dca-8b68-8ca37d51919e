package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.EvseRegisterReason;
import com.cdz360.iot.gw.model.base.DecimalRange;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * 发送给云端的网关注册消息
 */
@Data
@Accessors(chain = true)
public class CloudEvseRegisterReq {

    // 桩协议
    private final String protocol = EvseProtocolType.OCPP.name();

    //桩在云平台的唯一ID
    private String evseNo;

    /**
     * 桩端协议版本号
     */
    private int protocolVer;

    /**
     * 桩功率. 单位 kw
     */
    private BigDecimal power;

    /**
     * 电价模板编号
     */
    private Long priceCode;

    /**
     * 供电类型. 直流: DC; 交流: AC; 交直一体: BOTH
     */
    private String supplyType;

    /**
     * 网络类型 ETHENET: 网线 WIFI: 无线局域网 2G: 2G网络 3G: 3G网络 4G: 4G网络 5G: 5G网络
     */
    private String netType;

    /**
     * DTU类型
     */
    private DtuType dtuType;

    /**
     * 桩IP地址
     */
    private String evseIp;

    /**
     * ICCID, SIM卡号， 20位的数字字母
     */
    private String iccid;

    /**
     * 国际移动用户识别码,15位数字
     */
    private String imsi;

    /**
     * 移动设备国际识别码,15位数字
     */
    private String imei;

    /**
     * 上电标志位(注册原因)
     */
    private EvseRegisterReason registerReason;

    /**
     * 充电枪数量
     */
    private int plugNum;

    /**
     * 密钥版本号
     */
    private long passcodeVer;

    /**
     * 固件版本
     */
    private String firmwareVer;

    /**
     * PC板信息
     */
    private List<PcInfo> pcVer;

    private List<PlugInfo> plugs;

    @Getter
    @AllArgsConstructor
    public static class PcInfo {

        /**
         * PC板名称, 如: PC01
         */
        private String name;

        /**
         * PC板的软件定制号
         */
        private String vendorCode;

        /**
         * PC板的软件版本号
         */
        private String swVer;

        /**
         * PC板的硬件版本号
         */
        private String hwVer;
    }

    @Data
    @Accessors(chain = true)
    public static class PlugInfo {

        /**
         * 枪头序号
         */
        private int plugId;
        /**
         * 枪头功率. 仅交流桩有值.
         */
        private BigDecimal power;
        /**
         * 电压.
         * <p>交流桩枪头的最低电压和最高电压一样</p>
         */
        private DecimalRange voltage;
        /**
         * 电流. 仅直流桩有值
         */
        private DecimalRange current;
    }
}
