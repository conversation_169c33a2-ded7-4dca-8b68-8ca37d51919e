package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.evse.BmsVer;
import com.cdz360.iot.gw.model.type.BalanceMode;
import com.cdz360.iot.gw.model.type.Isolation;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ConfigInfo {

    private String evseNo;
    private String evsePasscodeVer;
    private String adminCodeA;
    private String adminCodeB;
    private Long priceCode;
    private List<Charge> price;

    private Boolean vin;

    private String qrUrl;
    // 紧急充电卡
    private List<WhiteCard> whiteCards;
    // 支持本地鉴权的在线卡
    private List<WhiteCard> localCards;
    private List<WhiteVin> whiteVinList;
    private BmsVer bmsVer;
    private Boolean autoStop;
    private BalanceMode balanceMode;
    private Boolean combination;
    private Boolean heating;
    // 12V or 24V
    private Integer heatingVoltage;
    private Boolean batteryCheck;
    private Isolation isolation;
    private Boolean manualMode;


    //	是否支持充电记录查询. 不传表示不做修改
    private Boolean queryChargeRecord;
    // 主动安全检测开关
    private Boolean securityCheck;

    // 不拔枪充电开关(二次充电)
    private Boolean constantCharge;

    // 插枪获取VIN开关
    private Boolean vinDiscover;

    // 订单信息隐私设置开关（null不做更改 true开启 false关闭）
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    private Integer accountDisplayType;

    // 是否支持扫码充电. 不传表示不做修改
    private Boolean qrCharge;

    //是否支持刷卡充电. 不传表示不做修改
    private Boolean cardCharge;

    // 是否支持无卡充电. 不传表示不做修改
    private Boolean noCardCharge;

    // 是否支持定时充电. 不传表示不做修改
    private Boolean timedCharge;

    // 白天音量. 不传表示不做修改
    private Integer dayVolume;

    //夜间音量. 不传表示不做修改
    private Integer nightVolume;

    //充电停止方式, 不传表示不做变更
    private StopModeCfg stopMode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
