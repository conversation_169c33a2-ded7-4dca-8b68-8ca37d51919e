package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

@Data
public class ConfigModifyResult {

    private String evseNo;
    private String cfgVer;//配置内容版本号. 该信息用于统计配置生效的数量
    private String result;//配置更新结果. SUCCESS: 成功; FAIL: 失败; TIMEOUT: 超时

    // 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
    private Integer adminCodeResult;//管理员账号配置结果
    private Integer triggerResult;//各种开关项配置结果
    private Integer whiteCardsResult;//紧急充电卡配置结果
    private Integer whiteVinsResult;//本地VIN配置结果
    private Integer chargeResult;//电价配置结果
    private Integer qrResult;//二维码配置结果

    // OCPP 云端配置下发结果
    private Integer getConfigResult; // 获取配置结果
    private OcppConfigResult ocppConfigResult; // 获取到的配置
    private Integer changeConfigResult; // 更改配置结果
    private Integer getDiagnosticsResult; // 获取诊断日志结果
    private String getDiagnosticsFileName; // 诊断日志文件名
    private Integer changeAvailabilityResult; // 变更桩可用性结果
    private Integer clearCacheResult; // 清除缓存结果
    private Integer triggerMessageResult; // 触发消息结果
    private Integer unlockConnectorResult; // 解锁桩结果

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
