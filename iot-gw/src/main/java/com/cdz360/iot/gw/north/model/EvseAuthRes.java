package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class EvseAuthRes {

    private EvseMsgBase base;
    /**
     * 返回码
     */
    private int result;

    private BigDecimal totalAmount;

    private BigDecimal totalPower;

    private String carNo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
