package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.evse.BmsVer;
import com.cdz360.iot.gw.model.type.BalanceMode;
import com.cdz360.iot.gw.model.type.Isolation;
import lombok.Data;

@Data
public class EvseConfig {

    // private String seq;
    private String evseNo;
    private BmsVer bmsVer;
    private Boolean autoStop;
    private BalanceMode balanceMode;
    private Boolean combination;
    private Boolean heating;
    private Boolean batteryCheck;
    private Isolation isolation;
    private Boolean manualMode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
