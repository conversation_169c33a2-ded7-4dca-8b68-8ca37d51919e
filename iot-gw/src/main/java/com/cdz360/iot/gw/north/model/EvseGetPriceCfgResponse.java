package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.base.CloudCommonResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseGetPriceCfgResponse extends CloudCommonResponse<ChargePriceVo> {
    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
