package com.cdz360.iot.gw.north.model;

import com.cdz360.iot.gw.model.evse.protocol.DcModuleInfo;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseModuleInfo {

    private String evseNo;

    private Integer dcModuleTotal;
    /**
     * 直流模块型号
     */
    private String dcModuleModel;
    /**
     * 直流模块信息
     */
    private List<DcModuleInfo> dcModuleInfoList;

}
