package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.type.ODMType;
import lombok.Data;

@Data
public class EvseOpInfoVo {

    private ODMType odm;

    private Integer plugNum;

    private SupplyType supply;

    private String timeZone;

    private ChargePriceVo priceVo;

}
