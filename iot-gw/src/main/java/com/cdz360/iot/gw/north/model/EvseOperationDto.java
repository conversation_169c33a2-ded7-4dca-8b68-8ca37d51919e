package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import eu.chargetime.ocpp.model.core.AvailabilityType;
import eu.chargetime.ocpp.model.remotetrigger.TriggerMessageRequestType;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvseOperationDto {

    private Integer plugId;

    /**
     * 获取配置
     */
    private List<String> getConfigKeyList;

    /**
     * 更改配置
     */
    private List<ConfigurationVo> changeConfigVoList;

    /**
     * 获取诊断日志
     */
    private GetDiagnosticsVo getDiagnosticsVo;

    /**
     * 变更桩可用性
     */
    private AvailabilityType availability;

    /**
     * 清除缓存
     */
    private Boolean clearCache;

    /**
     * 触发消息
     */
    private TriggerMessageRequestType messageTrigger;

    /**
     * 解锁连接器
     */
    private Boolean unlockConnector;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class ConfigurationVo {

        private String key;

        private String value;
    }

    @Data
    public static class GetDiagnosticsVo {

        private String location;

        private Integer retries = 3;

        private Integer retryInterval = 10;

        private Date startTime;

        private Date stopTime;
    }

}
