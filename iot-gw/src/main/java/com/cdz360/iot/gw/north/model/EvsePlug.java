package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvsePlug {

    private int plugId;             //是充电枪ID
    private String plugStatus;      //充电枪状态. 空闲, 等待开始充电, 充电中, 充电完成, 不可用 ???
    private Integer temp;               //充电枪温度
    private String error;           //当枪状态异常时, 上传异常原因
    private Integer errorCode;// 桩异常问题编码 ,对应桩端故障码 0C 等对应 整型
    private Integer alertCode;// 充电枪异常问题编码, 对应桩端协议的'告警码'
    private String orderNo; // 充电时充电订单号

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}