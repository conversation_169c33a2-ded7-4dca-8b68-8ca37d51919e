package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseStatusRequest {

    private String evseNo;          //桩在云平台的唯一ID
    private String evseStatus;      //桩状态: 正常, 异常 ???
    private Integer temp;               //桩温度
    private Integer errorCode;          // 桩异常问题编码 ,对应桩端故障码
    private String error;           //桩异常问题描述
    private List<EvsePlug> plugs;  //充电枪信息数组

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
