package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.ApiUrl;
import com.fasterxml.jackson.annotation.JsonProperty;

public class GwRegisterReq {
    String method = "REGISTER";//非注册时需要的字段
    //请求api url
    private String url = ApiUrl.URL_GW_REGISTER;//非注册时需要的字段

    String seq;

    @JsonProperty(value = "n")
    String gwno;

    String mac;

    String lanIp;

    public String getSeq() {
        return seq;
    }

    public GwRegisterReq setSeq(String seq) {
        this.seq = seq;
        return this;
    }

    public String getGwno() {
        return gwno;
    }

    public GwRegisterReq setGwno(String gwno) {
        this.gwno = gwno;
        return this;
    }

    public String getMac() {
        return mac;
    }

    public GwRegisterReq setMac(String mac) {
        this.mac = mac;
        return this;
    }

    public String getLanIp() {
        return lanIp;
    }

    public GwRegisterReq setLanIp(String localIp) {
        this.lanIp = localIp;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public GwRegisterReq setMethod(String method) {
        this.method = method;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
