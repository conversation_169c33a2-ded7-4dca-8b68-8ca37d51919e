package com.cdz360.iot.gw.north.model;


import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;

/**
 * 网关请求云端数据基础类
 */
public class IotPlatformRequest<T> implements Serializable {

    private String seq;
    private T data;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
