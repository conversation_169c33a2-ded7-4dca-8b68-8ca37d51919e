package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEvseParam extends BaseListParam {

    private List<String> siteIdList;

    /**
     * 变压器ID
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tfmId;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
