package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListPlugParam extends BaseListParam {

    private List<String> evseNoList;



    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }


}
