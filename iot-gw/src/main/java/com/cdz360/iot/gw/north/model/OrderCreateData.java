package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import lombok.Data;

@Data
public class OrderCreateData {

    //请求api url
    // private String url = ApiUrl.URL_CREATE_ORDER;

    //桩在云平台的唯一ID
    private String evseNo;

    //充电枪ID
    private int plugId;

    //开启方式 0x01:在线卡；0x12：车架号
    private int startType;

    private String vin;

    //账号：卡号（逻辑卡号）/17位VIN码
    private String accountNo;

    //停止方式
    private OrderStopMode stopMode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
