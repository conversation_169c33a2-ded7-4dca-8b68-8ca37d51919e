package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.CalcType;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderCreateInfo {

    private int status;

    //订单号. 长度不得超过16位.
    private String orderNo;

    // 费用抵扣模式
    private CalcType calcType;

    // 金额抵扣时必传; 账户总余额, 单位'元'
    private BigDecimal totalAmount;

    // 金额抵扣时必传; 可实时扣费金额, 单位'元'
    private BigDecimal frozenAmount;

    // 电量抵扣时必传; 电量账户总余额, 单位'kwh'
    private BigDecimal totalPower;

    // 电量抵扣时必传; 当前订单, 电量账户冻结余额(可实时扣费部分), 单位'kwh'
    private BigDecimal frozenPower;

    //停止方式
    private OrderStopMode stopMode;

    //车牌号 - VIN码充电时云端要返回车牌号信息
    private String carNo;

    // 充电SOC限制
    private Integer limitSoc;

    // 协议价优惠后的电价信息，下发到桩(鉴权用户使用协议价时存在)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargePriceVo priceVo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
