package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.base.CloudCommonResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderCreateResponse extends CloudCommonResponse<OrderCreateInfo> {


    /**
     * 场站功率分配. 网观侧填入值
     */
    private Integer power;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
