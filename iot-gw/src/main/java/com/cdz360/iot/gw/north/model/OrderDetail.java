package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 充电结束分时段详情
 *
 * @Author: Nathan
 * @Date: 2019/10/28 14:45
 */
@Data
@Accessors(chain = true)
public class OrderDetail {

    // HH:mm
    private String startTimeStr;

    // HH:mm
    private String stopTimeStr;

    // 分段计费开始时间, unix时间戳
    private long startTime;

    // 分段计费结束时间, unix时间戳
    private long stopTime;

    // 价格分段编号
    private int priceCode;

    // 时段开始时电表读数, 单位'kWh（千瓦时）'.
    private BigDecimal startMeter;

    // 当前累计电量, 单位'kwh（千瓦时）'.
    private BigDecimal kwh;

    // 电费单价, 单位 元/kwh
    private BigDecimal elecPrice;

    // 当前累计电费金额, 单位'元'
    private BigDecimal elecFee;

    // 服务费单价, 单位 元/kwh
    private BigDecimal servPrice;

    // 当前累计服务费金额, 单位'元'
    private BigDecimal servFee;



    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
