package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 充电中订单续费 -- 请求云端
 *
 * @Author: <PERSON>
 * @Date: 2019/7/4 13:28
 */
@Data
public class OrderFeeRefreshData {

    //请求api url
    // private String url = ApiUrl.URL_ORDER_FEE_REFRESH;

    // 订单号
    private String orderNo;

    // 桩编号
    private String evseNo;

    // 枪编号
    private int plugId;

    // 金额抵扣时必传; 剩余金额, 单位'元'
    private BigDecimal remainAmount;

    // 电量抵扣时必传; 剩余电量, 单位'kwh'
    private BigDecimal remainPower;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
