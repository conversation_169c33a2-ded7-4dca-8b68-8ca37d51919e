package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderFeeRefreshInfo {
    // 金额抵扣时必传; 账户总余额, 单位'元'
    private BigDecimal totalAmount;

    // 金额抵扣时必传; 增加的冻结金额, 单位'元'
    private BigDecimal frozenAmount;

    // 金额抵扣时必传; 增加后订单冻结金额, 单位'元'
    private BigDecimal afterOrderFrozenAmount;

    // 电量抵扣时必传; 电量账户总余额, 单位'kwh'
    private BigDecimal totalPower;

    // 电量抵扣时必传; 增加的冻结电量, 单位'kwh'
    private BigDecimal frozenPower;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
