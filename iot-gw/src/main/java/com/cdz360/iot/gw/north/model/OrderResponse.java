package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;

public class OrderResponse {
    private String seq;
    private int status;
    private String error;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
