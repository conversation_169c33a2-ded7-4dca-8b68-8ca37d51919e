package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.dto.BatteryDto;
import com.cdz360.base.model.charge.dto.BmsDto;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.type.CalcType;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderStartData {

    //请求api url
    // private String url = ApiUrl.URL_START_ORDER;

    //订单号
    private String orderNo;

    //桩编号
    private String evseNo;

    //枪编号
    private Integer plugId;

    // 订单启动结果 0表示成功，其他都表示失败, 失败码同充电结束码
    private Integer startResult;

    //开启方式
    private OrderStartType startType;

    // 费用抵扣模式
    private CalcType calcType;

    //账号
    private String accountNo;

    //车架号
    private String vin;

    //充电开始时间
    private Long startTime;

    // 本次充电的最大输出功率, 单位 kw
    private Integer power;

    //剩余电量
    private Integer soc;

    //停止方式
    private OrderStopMode stopMode;

    private Insulation insulation;

    // BMS静态信息
    private BmsDto bms;

    // 电池静态信息
    private BatteryDto battery;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    // 绝缘检测信息
    public static class Insulation {
        /**
         * 绝缘检测结果
         * <p>
         * 0正常
         * 1故障
         * 2告警
         */
        private Integer result;

        // DC+绝缘检测值. 单位1Ω/V, example = "123"
        private Integer positive;

        // DC-绝缘检测值. 单位1Ω/V, example = "123"
        private Integer negative;

        /**
         * 绝缘检测电压
         * 单位V
         */
        private BigDecimal voltage;
    }
}