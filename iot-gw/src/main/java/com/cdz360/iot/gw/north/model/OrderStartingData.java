package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

@Data
public class OrderStartingData {
    private String seq;
    private IotGwCmdType2 cmd;
    private int status;
    private String msg;
    private ChargeStartDetail detail;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
