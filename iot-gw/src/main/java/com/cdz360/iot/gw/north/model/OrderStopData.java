package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class OrderStopData {

    //请求api url
    // private String url = ApiUrl.URL_STOP_ORDER;

    //订单号
    private String orderNo;

    //桩编号
    private String evseNo;

    //枪编号
    private int plugId;

    // 充电完成原因
    private int completeCode;

    // 故障停充码. 0 为正常完成, 其他都为非正常完成
    private int stopCode;

    // 消费电量, 单位'kwh'
    private BigDecimal kwh;

    // 充电开始前电表读数, 单位'kwh'
    private BigDecimal startMeter;

    // 充电完成后电表读数, 单位'kwh'
    private BigDecimal stopMeter;

    // 合充的辅枪枪号
    private Integer secondPlugIdx;

    // 辅枪充电前电表读数
    private BigDecimal secondStartMeter;

    // 辅枪充电后电表读数
    private BigDecimal secondStopMeter;

    // 辅枪订单总电量
    private BigDecimal secondKwh;

    // 当前累计电费金额, 单位'元'
    private BigDecimal elecFee;

    // 当前累计服务费金额, 单位'元'
    private BigDecimal servFee;

    // 充电开始 开始充电时间, unix时间戳
    private long startTime;

    // 充电停止 停止充电时间, unix时间戳
    private long stopTime;

    // 开启方式
    private OrderStartType startType;

    //账号
    private String accountNo;

    // 车架号
    private String vin;

    // 电池单体最低电压
    private BigDecimal minBatteryVoltage;

    // 充电开始时的soc
    private Integer startSoc;

    // 充电结束时的soc
    private Integer soc;

    //电价模板编号
    private long priceCode;

    // 账单详情
    private List<OrderDetail> detail;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}