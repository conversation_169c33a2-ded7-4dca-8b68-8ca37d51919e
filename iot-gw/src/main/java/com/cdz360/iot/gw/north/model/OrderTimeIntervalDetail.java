package com.cdz360.iot.gw.north.model;

import com.cdz360.base.model.charge.dto.BatteryDynamicDto;
import com.cdz360.base.model.charge.dto.BmsDynamicDto;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 分时段详情
 * @Date 2019/5/14
 **/
@Data
@Accessors(chain = true)
public class OrderTimeIntervalDetail {
    private long timestamp;//unix时间戳
    private Integer duration;//已充时长, 单位'分钟
    private Integer remainingTime;//计剩余充电时间, 单位'分钟'
    private BigDecimal kwh;//消费电量, 单位'KWH'
    private BigDecimal elecFee;//当前累计电费金额, 单位'元'
    private BigDecimal servFee;//当前累计服务费金额, 单位'元'

    private Integer batteryTemp;//电池温度, 单位'摄氏度'
    private Integer evseTemp;//桩温度, 单位'摄氏度'
    private Integer plugTemp;//枪温度, 单位'摄氏度'
    private BigDecimal maxVoltage;//单体最高电压, 单位'伏'
    private BigDecimal minVoltage;//单体最低电压, 单位'伏'

    private Integer soc;//当前实时电量. 百分比.
    private BigDecimal power; //功率
    private BigDecimal dcVoltageO;//直流输出电压, 单位'伏'
    private BigDecimal dcCurrentO;//直流输出电流, 单位'安'
    private BigDecimal dcVoltageA;//直流A相输入电压, 单位'伏'
    private BigDecimal dcVoltageB;//直流B相输入电压, 单位'伏'
    private BigDecimal dcVoltageC;//直流C相输入电压, 单位'伏'
    private BigDecimal acVoltageA;//交流A相电压, 单位'伏'
    private BigDecimal acCurrentA;//交流A相电流, 单位'安'
    private BigDecimal acVoltageB;//交流B相电压, 单位'伏'
    private BigDecimal acCurrentB;//交流B相电流, 单位'安'
    private BigDecimal acVoltageC;//交流C相电压, 单位'伏'
    private BigDecimal acCurrentC;//交流C相电流, 单位'安'

    /**
     * BMS动态数据
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BmsDynamicDto bms;

    /**
     * 电池动态数据
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryDynamicDto battery;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
