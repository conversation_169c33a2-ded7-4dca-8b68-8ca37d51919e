package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

@Data
public class OrderUpdateData {

    //请求api url
    // private String url = ApiUrl.URL_UPDATE_ORDER;

    //订单号
    private String orderNo;

    //桩编号
    private String evseNo;

    //枪编号
    private int plugId;

    //分时段详情
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderTimeIntervalDetail> details;

    /**
     * 电价模板编号,仅上报分时数据时需要
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long priceCode;

    /**
     * 充电中分时数据
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderDetail> priceItems;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}