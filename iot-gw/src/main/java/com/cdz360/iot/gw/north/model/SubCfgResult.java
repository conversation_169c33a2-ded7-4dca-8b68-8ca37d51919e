package com.cdz360.iot.gw.north.model;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

@Data
public class SubCfgResult {

    // 0x00 -- 成功; null -- 不做变更; 其他 -- 失败
    private Integer adminCodeResult;//管理员账号配置结果
    private Integer triggerResult;//各种开关项配置结果
    private Integer whiteCardsResult;//紧急充电卡配置结果
    private Integer chargeResult;//电价配置结果
    private Integer qrResult;//二维码配置结果

    // OCPP 云端配置下发结果
    private Integer getConfigResult;
    private OcppConfigResult ocppConfigResult;
    private Integer changeConfigResult;
    private Integer getDiagnosticsResult;
    private String getDiagnosticsFileName;
    private Integer changeAvailabilityResult;
    private Integer clearCacheResult;
    private Integer triggerMessageResult;
    private Integer unlockConnectorResult;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
