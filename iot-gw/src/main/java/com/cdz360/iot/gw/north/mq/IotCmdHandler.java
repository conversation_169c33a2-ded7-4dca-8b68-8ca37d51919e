package com.cdz360.iot.gw.north.mq;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.mq.consumer.MqttConsumer;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 云端发送的下行指令的处理/云端反馈的响应
 */
@Component
public class IotCmdHandler {
    private final Logger logger = LoggerFactory.getLogger(IotCmdHandler.class);

    @Autowired
    private MqttConsumerFactory consumerFactory;

    @Autowired
    private MqttAbcQueue mqttAbcQueue;

    /**
     * 处理云端MOTT下发指令操作
     *
     * @param message message
     */
    public void process(String message) {
        String traceId = SeqGeneratorUtil.newStringId();
        logger.info("MQTT message received. traceId: {}, message: {}", traceId, message);

        if (StringUtils.isEmpty(message)) {
            logger.info("MQTT message is empty.");
            return;
        }

        try {
            JsonNode json = JsonUtils.fromJson(message);
            if (json == null) {
                logger.warn("<< 空指令: {}", message);
                return;
            }

            String seq = json.get("seq").asText();
            if (!StringUtils.isEmpty(seq)) {    // mqtt指令防重处理
                if (mqttAbcQueue.addAndCheckExist(seq)) {
                    logger.warn("<< seq( {} )已存在. 忽略重送的指令. msg: {}", seq, message);
                    return;
                }
               // mqttAbcQueue.pushKey(seq);
            }


            Integer cmd = json.get("c").asInt();

            MqttConsumer mqttConsumer = consumerFactory.getConsumer(cmd);

            if (mqttConsumer == null) {
                logger.error("不支持的指令: {}", json);
                return;
            }

            mqttConsumer.consume(traceId, json);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
