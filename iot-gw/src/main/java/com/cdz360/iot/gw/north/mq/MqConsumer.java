package com.cdz360.iot.gw.north.mq;

import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MqConsumer extends DefaultConsumer {

    private static Long idx = 0L;

    private IotCmdHandler iotCmdHandler;

    /**
     * Constructs a new instance and records its association to the passed-in channel.
     *
     * @param channel the channel to which this consumer is attached
     */
    public MqConsumer(Channel channel, IotCmdHandler iotCmdHandler) {
        super(channel);
        this.iotCmdHandler = iotCmdHandler;
    }

    @Override
    public void handleDelivery(String consumerTag,
                               Envelope envelope,
                               AMQP.BasicProperties properties,
                               byte[] body) {
        String msg = new String(body, StandardCharsets.UTF_8);
        log.info("consumerTag: {},  msg: {}", consumerTag, msg);
        try {

            Long idxTmp = idx++;
            log.debug("idx: {}, 处理前", idxTmp);
            iotCmdHandler.process(msg);
            log.debug("idx: {}, 处理后", idxTmp);
//            IotGwCmdTimeoutEvent event = JsonUtils.fromJson(msg, IotGwCmdTimeoutEvent.class);
//            this.gwCmdTimeoutHandler.handleTimeout(event.getData());
        } catch (Exception e) {
            log.warn("message:{}", e.getMessage());
            log.warn("收到未识别的消息. msg: {}", msg);
        }

        log.info("<<");
    }
}
