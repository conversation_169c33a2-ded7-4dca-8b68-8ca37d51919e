package com.cdz360.iot.gw.north.mq;

import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.config.MqProperties;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.BuiltinExchangeType;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqService {


    @Autowired
    private MqProperties mqProperties;

    @Autowired
    private IotCmdHandler iotCmdHandler;

    private ConnectionFactory factory;
    private Connection connection;

    @PostConstruct
    public void init() {
        log.info("mq 配置信息: {}", mqProperties);
        factory = new ConnectionFactory();
        factory.setHost(mqProperties.getHost());
        //端口
        factory.setPort(mqProperties.getPort());

        factory.setVirtualHost(mqProperties.getVirtualHost());
//        if (StringUtils.startsWithIgnoreCase(mqProperties.getVirtualHost(), "aliyun")) {
//            //设置账号信息，用户名、密码、vhost
//            factory.setCredentialsProvider(new AliyunCredentialsProvider(mqProperties.getUsername(), mqProperties.getPassword(), mqProperties.getAliResourceId()));
//        } else {
        factory.setUsername(mqProperties.getUsername());
        factory.setPassword(mqProperties.getPassword());
//        }
    }


    public boolean updateProps(String gwno) {
        try {

            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    log.warn("close mq connection failed!!! error: {}", e.getMessage(), e);
                }
            }
            // 获取到连接以及mq通道
            connection = factory.newConnection();
            Channel channel = connection.createChannel();
            // 声明队列

            channel.exchangeDeclare(IotGwConstants.MQ_EXCHANGE_NAME, BuiltinExchangeType.DIRECT,
                true, false, false, null);

            String queueName = IotGwConstants.MQ_QUEUE_NAME + "-" + gwno;
            channel.queueDeclare(queueName, true, false, false, null);

            // 绑定队列到交换机
            AMQP.Queue.BindOk bindRet = channel.queueBind(queueName,
                IotGwConstants.MQ_EXCHANGE_NAME, gwno);

            // 定义队列的消费者
            MqConsumer consumer = new MqConsumer(channel, iotCmdHandler);
            // 监听队列，手动返回完成
            String consumerTag = channel.basicConsume(queueName, true, consumer);
            log.info("mq consumerTag: {}", consumerTag);

            return true;
        } catch (Exception e) {

            log.error("修改MQ配置失败。", e);
            return false;
        }
    }
}
