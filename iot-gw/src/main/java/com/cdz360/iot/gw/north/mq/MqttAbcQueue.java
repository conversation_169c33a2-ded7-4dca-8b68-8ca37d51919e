package com.cdz360.iot.gw.north.mq;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentSkipListSet;
import org.springframework.stereotype.Component;

/**
 * 轮转队列
 * A队列用于接收最新的消息
 * B队列用于查找未过期的缓存
 * C队列用于将过期的消息清除
 */
@Component
public class MqttAbcQueue {

    private final static int SIZE = 3;
    private List<ConcurrentSkipListSet<String>> queues = new ArrayList<>(SIZE);
    private int aIdx = 0;   // A队列下标
    private int bIdx = 1;   // B队列下标
    private int cIdx = 2;   // C队列下标

    @PostConstruct
    public void init() {
        for(int i = 0;i < SIZE; i ++) {
            queues.add(new ConcurrentSkipListSet<>());
        }
    }

    public synchronized void rotate() {
        aIdx = (aIdx + 2) % SIZE;
        bIdx = (aIdx + 1) % SIZE;
        cIdx = (bIdx + 1) % SIZE;
        queues.get(cIdx).clear();
    }


    /**
     * 把 val 加入到队列A, 同时返回之前是否存在的信息
     * @param val
     * @return true: val 之前存在; false: val 之前不存在
     */
    public boolean addAndCheckExist(String val) {
        ConcurrentSkipListSet<String> queueA = this.getQueueA();
        boolean ret = queueA.add(val);  // true: 之前不存在; false: 之前已存在
        if(!ret){
            return true;
        }
        ConcurrentSkipListSet<String> queueB = this.getQueueB();
        return queueB.contains(val);

    }

    private ConcurrentSkipListSet<String> getQueueA() {
        return queues.get(aIdx);
    }

    private ConcurrentSkipListSet<String> getQueueB() {
        return queues.get(bIdx);
    }
}
