package com.cdz360.iot.gw.north.mq;

import com.cdz360.base.utils.NumberUtils;
import com.cdz360.iot.gw.north.mq.consumer.MqttConsumer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(value = "singleton")
public class MqttConsumerFactory   {

    private static final Map<Integer, MqttConsumer> cacheMap = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(MqttConsumerFactory.class);
    @Autowired
    private ApplicationContext applicationContext;

    MqttConsumer getConsumer(Integer method) {

        logger.debug("getConsumer. method: {}", method);

        if (method == null) {
            return null;
        }

        if (cacheMap.containsKey(method)) {
            return cacheMap.get(method);
        }

        //获取Spring容器中所有继承自CommandHandler的类的实例
        Map<String, MqttConsumer> map = applicationContext.getBeansOfType(MqttConsumer.class);

        for (Map.Entry<String, MqttConsumer> entry : map.entrySet()) {
            MqttConsumer handler = entry.getValue();
            if (NumberUtils.equals(method, handler.method())) {
                if (!cacheMap.containsKey(method)) {
                    cacheMap.put(method, handler);
                }
                return handler;
            }
        }

        return null;
    }
}
