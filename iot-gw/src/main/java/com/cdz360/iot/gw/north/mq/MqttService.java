package com.cdz360.iot.gw.north.mq;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MQTT_VERSION_3_1_1;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.MqttInfoService;
import com.cdz360.iot.gw.model.gw.MqttProperties;
import com.cdz360.iot.gw.north.server.IotUpService;
import java.nio.charset.StandardCharsets;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class MqttService {
    private static boolean mqttConnectFlag = false;
    private static Long idx = 0L;
    private final Logger logger = LoggerFactory.getLogger(MqttService.class);
    @Autowired
    private IotCmdHandler iotCmdHandler;
    @Autowired
    private MqttInfoService mqttInfoService;
    @Autowired
    private IotUpService iotUpService;
    private ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newCachedThreadPool();

    public boolean updateProps(MqttProperties props) {
        if(!props.isEnable()) {
            return true;
        }
        try {

            Assert.notNull(props, "参数不能为空");
            MqttProperties mqttInfoDB = mqttInfoService.getMqttInfoNotCancel();
            if (mqttInfoDB != null && mqttInfoDB.equals(props) && mqttConnectFlag) {
                return true;
            }

            mqttInfoService.addMqttInfo(props);
            return this.subscribe();
        } catch (Exception e) {

            logger.error("修改MQTT配置失败。", e);
            return false;
        }
    }


    public boolean subscribe() {
        MemoryPersistence persistence = new MemoryPersistence();
        MqttProperties mqttInfoDB = mqttInfoService.getMqttInfoNotCancel();
        if (mqttInfoDB == null) {
            logger.info("Connecting to mqtt subscribe mqttInfo is null");
            return false;
        }

        try {
            MqttClient sampleClient = new MqttClient(mqttInfoDB.getUrl(), mqttInfoDB.getClientId(), persistence);
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setUserName(mqttInfoDB.getUsername());
            connOpts.setPassword(mqttInfoDB.getPassword().toCharArray());
            connOpts.setCleanSession(true);
            // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送个消息判断客户端是否在线，但这个方法并没有重连的机制
            connOpts.setKeepAliveInterval(60);
            connOpts.setMqttVersion(MQTT_VERSION_3_1_1);
            connOpts.setAutomaticReconnect(true);
            connOpts.setConnectionTimeout(30);


            logger.info("Connecting to mqtt broker: {}, topic: {}", mqttInfoDB.getUrl(), mqttInfoDB.getTopic());
            sampleClient.connect(connOpts);
            mqttConnectFlag = true;
            logger.info("MQTT connected");
            // logger.info("Publishing message: "+content);
            // MqttMessage message = new MqttMessage(content.getBytes());
            // message.setQos(0);
            sampleClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    logger.error("MQTT连接意外断开。", cause);
                    mqttConnectFlag = false;
                    // 立即调用
                    new Timer("Timer-MQTT").schedule(new TimerTask() {
                        @Override
                        public void run() {
                            iotUpService.login();
                        }
                    }, 100);
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    logger.info("topic: {}, message: {}", topic, JsonUtils.toJsonString(message));
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    logger.info("token: {}", token.toString());
                }
            });

            //sampleClient.publish(topic, message);
            //sampleClient.subscribe("test01/bbb/ccc/");
            sampleClient.subscribe(mqttInfoDB.getTopic(), (tp, msg) -> {
                processMessage(tp, msg);
            });
            //logger.info("Message published");
            //sampleClient.disconnect();
            //logger.info("Disconnected");
            // System.exit(0);

        } catch (MqttException me) {
            mqttConnectFlag = false;
            logger.error("reason " + me.getReasonCode());
            logger.error("msg " + me.getMessage());
            logger.error("loc " + me.getLocalizedMessage());
            logger.error("cause " + me.getCause());
            logger.error("excep " + me);
            logger.error(me.getMessage(), me);
        }
        return mqttConnectFlag;
    }

    private void processMessage(String topic, MqttMessage message) {
        //sampleClient.subscribe(topic, (tp, msg) -> {
        logger.info("mqtt topic: {}, msg: {}", topic, JsonUtils.toJsonString(message));

        Long idxTmp = idx++;

        executor.submit(() -> {
            logger.debug("idx: {}, 处理前", idxTmp);
            iotCmdHandler.process(new String(message.getPayload(), StandardCharsets.UTF_8));
            logger.debug("idx: {}, 处理后", idxTmp);
        });
    }
}
