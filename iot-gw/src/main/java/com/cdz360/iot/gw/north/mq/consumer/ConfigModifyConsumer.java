package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CfgProcess;
import com.cdz360.iot.gw.north.mq.model.ConfigModifyRequest;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ConfigModifyConsumer implements MqttConsumer {
    private final Logger logger = LoggerFactory.getLogger(ConfigModifyConsumer.class);

    @Autowired
    private CfgProcess cfgProcess;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_MODIFY_CFG.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        // logger.info("云端修改桩配置。traceId: {}, message: {}.", traceId, message);
        try {
            cfgProcess.processEvseCfg(traceId, JsonUtils.fromJson(message, ConfigModifyRequest.class));
        } catch (Exception ex) {
            logger.error("[{}] 修改桩配置时发生错误 error: {}", traceId, ex.getMessage(), ex);
        }
    }
}
