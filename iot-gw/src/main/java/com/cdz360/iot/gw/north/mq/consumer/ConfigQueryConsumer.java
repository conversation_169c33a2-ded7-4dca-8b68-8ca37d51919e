package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CardProcess;
import com.cdz360.iot.gw.north.mq.model.ConfigQueryRequest;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ConfigQueryConsumer implements MqttConsumer {

    private final Logger logger = LoggerFactory.getLogger(ConfigQueryConsumer.class);

//    @Autowired
//    private CfgProcess cfgProcess;

    @Autowired
    private CardProcess cardProcess;

//    @Autowired
//    private VinProcess vinProcess;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_GET_CFG.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
//         logger.info("云端获取桩配置。 message: {}.", message);
/*

        try {
            this.cfgProcess.getConfig(traceId, JsonUtils.fromJson(message, ConfigQueryRequest.class));
        } catch (Exception ex) {
            logger.error("云端获取桩配置时发生错误。traceId: {}", traceId, ex);
        }
*/
        try {
            this.cardProcess.getCard(traceId,
                JsonUtils.fromJson(message, ConfigQueryRequest.class));
        } catch (Exception ex) {
            logger.error("云端获取紧急卡时发生错误。traceId: {}", traceId, ex);
        }
/*

        try {
            this.vinProcess.getVin(traceId, JsonUtils.fromJson(message, ConfigQueryRequest.class));
        } catch (Exception ex) {
            logger.error("云端获取本地VIN时发生错误。traceId: {}", traceId, ex);
        }
*/
    }
}
