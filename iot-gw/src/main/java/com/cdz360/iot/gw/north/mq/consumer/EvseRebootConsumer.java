package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.EvseCmdBizService;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.evse.protocol.EvseRebootDown;
import com.cdz360.iot.gw.north.mq.model.MqEvseRebootMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EvseRebootConsumer implements MqttConsumer {


    @Autowired
    private JsonServerImpl jsonServer;

    @Autowired
    private EvseCmdBizService evseCmdBizService;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_REBOOT.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        log.info("[{}] 云端重启桩 msg: {}", traceId, message);
        MqMsgBase<MqEvseRebootMsg> msg = JsonUtils.fromJson(message, new TypeReference<>() {
        });
        String evseNo = msg.getData().getEvseNo();
        long seqNo = SeqGeneratorUtil.newIntegerId(evseNo);

        this.evseCmdBizService.addCmd(evseNo, seqNo, msg);

        EvseMsgBase base = new EvseMsgBase();
        base.setEvseNo(evseNo).setTid(traceId).setSeq(seqNo);

        EvseRebootDown dMsg = new EvseRebootDown(base);
        try {
            boolean result = jsonServer.sendEvseResetReq(dMsg);
            this.evseCmdBizService.reportCmdResult(traceId, evseNo, seqNo,
                result ? IotGwConstants.SUCCESS : IotGwConstants.UNKNOWN_ERROR, null);
        } catch (DcException e) {
            log.error("[{} {}] 桩重启失败. error: {}", traceId, evseNo, e.getMessage(), e);
            this.evseCmdBizService.reportCmdResult(traceId, evseNo, seqNo, e.getStatus(),
                e.getMessage());
        } catch (Exception e) {
            log.error("[{} {}] 桩重启失败. error: {}", traceId, evseNo, e.getMessage(), e);
            this.evseCmdBizService.reportCmdResult(traceId, evseNo, seqNo,
                IotGwConstants.UNKNOWN_ERROR, e.getMessage());
        }
    }
}
