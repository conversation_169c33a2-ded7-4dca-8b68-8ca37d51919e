package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CloudOperationsProcess;
import com.cdz360.iot.gw.north.mq.model.ConfigModifyRequest;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OcppMessageConsumer implements MqttConsumer {

    private final Logger logger = LoggerFactory.getLogger(OcppMessageConsumer.class);

    @Autowired
    private CloudOperationsProcess cloudOperationsProcess;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_OCPP_CLOUD_OPERATIONS.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        try {
            cloudOperationsProcess.processCloudOperations(traceId,
                JsonUtils.fromJson(message, ConfigModifyRequest.class));
        } catch (Exception ex) {
            logger.error("[{}] OCPP云端操作时发生错误 error: {}", traceId, ex.getMessage(), ex);
        }
    }
}
