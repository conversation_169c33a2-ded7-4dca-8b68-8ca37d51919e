package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStopDown;
import com.cdz360.iot.gw.north.mq.model.MqChargeStopMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderStopConsumer implements MqttConsumer {

    @Autowired
    private OrderBizService orderBizService;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_CHARGE_STOP.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        log.info("[{}] 云端结束订单 msg: {}", traceId, message);
        try {
            MqMsgBase<MqChargeStopMsg> msg = JsonUtils.fromJson(message,
                new TypeReference<MqMsgBase<MqChargeStopMsg>>() {
                });

            MqChargeStopMsg mqMsg = msg.getData();

            EvseMsgBase base = new EvseMsgBase();
            base.setEvseNo(mqMsg.getEvseNo())
                .setTid(traceId)
                .setPlugIdx(mqMsg.getPlugId())
                .setCmdCode(EvseMessageType.CHARGE_STOP_FROM_CLOUD)
//                    .setSeq(mqMsg.getSeqNoToEvse())
            ;

            ChargeStopDown dMsg = new ChargeStopDown(base);
            dMsg.setOrderNo(mqMsg.getOrderNo())
            ;
            orderBizService.stopOrderFromCloud(dMsg);
        } catch (Exception e) {
            log.error("处理云端结束订单失败。traceId:{}, msg: {}", traceId, message, e);
        }
    }

}
