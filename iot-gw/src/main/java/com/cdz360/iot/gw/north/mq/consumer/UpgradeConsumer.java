package com.cdz360.iot.gw.north.mq.consumer;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.UpgradeProcess;
import com.cdz360.iot.gw.north.mq.model.MqEvseUpgradeMsg;
import com.cdz360.iot.gw.north.mq.model.MqMsgBase;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UpgradeConsumer implements MqttConsumer {

    @Autowired
    private UpgradeProcess upgradeProcess;

    @Override
    public Integer method() {
        return IotGwCmdType2.CE_UPGRADE.getCode();
    }

    @Override
    public void consume(String traceId, JsonNode message) {
        MqMsgBase<MqEvseUpgradeMsg> msg = JsonUtils.fromJson(message, new TypeReference<MqMsgBase<MqEvseUpgradeMsg>>() {
        });
        MqEvseUpgradeMsg data = msg.getData();
        if (CollectionUtils.isNotEmpty(data.getFileVoList())) {
            upgradeProcess.upgradeEvse2(traceId, msg);
        } else {
            upgradeProcess.upgradeEvse(traceId, msg);
        }
    }
}
