package com.cdz360.iot.gw.north.mq.model;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.north.model.GetConfigResponse;
import com.cdz360.iot.gw.north.model.IotPlatformRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EvseCfgGetResponse extends IotPlatformRequest<GetConfigResponse> {
    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
