package com.cdz360.iot.gw.north.mq.model;

import com.cdz360.base.utils.JsonUtils;

public class EvseVersion {
    private String name;
    private Short vendorCode;
    private Short swVer;
    private String url;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Short getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(Short vendorCode) {
        this.vendorCode = vendorCode;
    }

    public Short getSwVer() {
        return swVer;
    }

    public void setSwVer(Short swVer) {
        this.swVer = swVer;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
