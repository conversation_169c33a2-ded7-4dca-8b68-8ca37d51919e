package com.cdz360.iot.gw.north.mq.model;

//public enum MqCmdType {
//    CHARGE_START,   // 开启充电
//    CHARGE_STOP,    // 停止充电
//    EVSE_GET_CFG,   // 获取桩配置
//    EVSE_CFG,       // 更新桩配置
//    TUNNEL_START,   // 开启网关远程debug通道
//    TUNNEL_STOP,    // 关闭网关远程debug通道
//    EVSE_UPGRADE,   // 桩远程升级
//    EVSE_REBOOT,    // 桩远程重启
//    EVSE_DEBUG,   // 桩远程debug指令
//    CHARGE_SOC_CTRL, // SOC控制
//    CHARGE_POWER_CTRL, // 功率分配
//    EVSE_GET_MODULE, // 获取桩模块信息
//    TRANSFORMER_UPDATE, // 变压器分配功率更新
//    LOCAL_DEBUG, // 本地调试
//
//}
