package com.cdz360.iot.gw.north.mq.model;

import com.cdz360.base.model.base.type.IotGwCmdType2;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 网关MQ消息的基础数据格式
 */
@Data
public class MqMsgBase<T> {

    /**
     * 网关协议版本
     */
    @JsonProperty("v")
    private Integer version;

    /**
     * 请求消息中的 seq
     */
    @JsonProperty("s")
    private String seq;

    /**
     * 指令编码
     */
    @JsonProperty("c")
    private IotGwCmdType2 cmd;

    /**
     * 网关编号
     */
    @JsonProperty("n")
    private String gwno;

    private T data;
}
