package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.asyncPost;

import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.north.model.AuthData;
import com.cdz360.iot.gw.north.model.AuthRequest;
import com.cdz360.iot.gw.north.model.RequestAuthResultResponse;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 桩相关的业务逻辑处理服务. 作为 iotServer 和 iotClient 间的代理类
 */
@Service
public class AuthService {
    private final Logger logger = LoggerFactory.getLogger(AuthService.class);

    public Mono<RequestAuthResultResponse> processCardAuth(String traceId, AuthData authData) {
        AuthRequest request = new AuthRequest();
        request.setSeq(SeqGeneratorUtil.newStringId()); // 序列号
        request.setData(authData);
        logger.info("[{}] 请求鉴权。 request：{}", traceId, request);

        return asyncPost(ApiUrl.URL_REQUEST_AUTH, request, RequestAuthResultResponse.class)
                .doOnNext(result -> {
                    if (null != result && result.getStatus() == 0) {
                        //不要抛出自定义的运行时异常，会破坏后面的调用链
                        logger.info("[{}] 鉴权成功。 response: {}", traceId, result);
                    } else {
                        logger.error("[{}] 鉴权失败。 response: {}", traceId, result);
                    }
                })
                .doOnError(throwable -> {
                    logger.error("[{}] 请求鉴权发生错误。 fail: {}", traceId, throwable.getMessage(), throwable);
                    // filter.callback(JsonUtils.toJsonString(request));
                });
    }
}
