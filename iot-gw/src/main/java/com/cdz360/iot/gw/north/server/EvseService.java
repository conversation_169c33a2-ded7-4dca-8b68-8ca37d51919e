package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.asyncPost;
import static com.cdz360.iot.gw.util.WebClientUtil.syncPost;

import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.model.base.BasePlatformResponse;
import com.cdz360.iot.gw.model.evse.CloudEvseRegisterRes;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.north.model.CloudEvsePasscodeReq;
import com.cdz360.iot.gw.north.model.CloudEvsePasscodeRes;
import com.cdz360.iot.gw.north.model.CloudEvseRegisterReq;
import com.cdz360.iot.gw.north.model.EvseGetOpInfoResponse;
import com.cdz360.iot.gw.north.model.EvseOpInfoGetRequest;
import com.cdz360.iot.gw.north.model.EvseStatusRequest;
import com.cdz360.iot.gw.north.model.GetEvseOpInfo;
import com.cdz360.iot.gw.north.model.HeartbeatRequest;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EvseService {

    public CloudEvsePasscodeRes getEvsePasscode(final String traceId, final CloudUpReq<CloudEvsePasscodeReq> request) {
        log.info("[{}] 获取云端桩长效密钥。 request: {}", traceId, request);
        CloudEvsePasscodeRes res = syncPost(request, ApiUrl.URL_EVSE_PASSCODE,
            CloudEvsePasscodeRes.class);
        log.info("[{}] 获取桩密钥响应. res: {}", traceId, res);
        return res;
    }

    public void registerEvse(final String traceId, final CloudUpReq<CloudEvseRegisterReq> request) {
        log.info("[{}] 桩注册： request: {}", traceId, request);
        asyncPost(ApiUrl.URL_EVSE_REGISTER, request, CloudEvseRegisterRes.class)
            .doOnNext(r -> {

                if (r == null || r.getStatus() != 0) {
                    // throw new RuntimeException("创建订单失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{}] 桩注册失败。 response: {}", traceId, r);
                } else {
                    log.info("[{}] 桩注册成功： response: {}", traceId, r);
                }

                if (r == null) {
                    // filter.callback(JsonUtils.toJsonString(request));
                    log.error("[{}] 桩注册失败。 response is null.", traceId);
                }

            })
            .doOnError(throwable -> {
                log.error("[{}] 桩注册失败。", traceId, throwable);
                // filter.callback(JsonUtils.toJsonString(request));
            }).subscribe();
    }

    /**
     * 异步上报桩状态给云端, 不等待响应消息
     */
    public void reportEvseStatus(String traceId, EvseStatusRequest data) {
        log.info("[{}] 上报桩状态信息. data = {}", traceId, data);

        HeartbeatRequest heartbeatRequest = new HeartbeatRequest();
        heartbeatRequest.setSeq(SeqGeneratorUtil.newStringId());
        heartbeatRequest.setData(data);

        asyncPost(ApiUrl.URL_EVSE_STATUS, heartbeatRequest, BasePlatformResponse.class)
            .doOnNext(res -> {
                if (res == null || res.getStatus() != 0) {
                    // throw new RuntimeException("上报桩状态信息失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.error("[{}] 上报桩状态信息失败。 response: {}", traceId, res);
                } else {
                    log.info("[{}] 上报桩状态信息完成。 response: {}", traceId, res);
                }

                if (res == null) {
                    log.error("[{}] 上报桩状态信息失败。 response is null. reqData = {}",
                        traceId, data);
                    // filter.callback(JsonUtils.toJsonString(heartbeatRequest)); // 网络异常  ???? TODO: 临时移过来的, 要调整
                }
            })
            .doOnError(ex -> {
                log.error("[{}] 上报桩状态信息失败。 reqData = {}", traceId, data, ex);
            })
            .subscribe();
    }

    /**
     * 从云端获取桩运转信息
     */
    public Mono<EvseGetOpInfoResponse> getEvseOpInfo(String tid, GetEvseOpInfo opInfo) {
        log.info("[{}] 向云端请求桩运转信息. opInfo: {}", tid, opInfo);
        EvseOpInfoGetRequest request = new EvseOpInfoGetRequest();
        request.setSeq(SeqGeneratorUtil.newStringId());
        request.setData(opInfo);

        return asyncPost(ApiUrl.URL_GET_EVSE_OP_INFO, request, EvseGetOpInfoResponse.class).doOnNext(
            cloudRes -> {
                if (cloudRes == null || cloudRes.getStatus() != 0) {
                    log.error("[{}] 从云端获取桩运转信息失败。 response: {}", tid, cloudRes);
                } else {
                    log.info("[{}] 从云端获取桩运转信息。 response: {}", tid, cloudRes);
                }
                if (cloudRes == null) {
                    log.error("[{}] 从云端获取桩运转信息失败。 response: {}", tid, cloudRes);
                }
            }).doOnError(throwable -> {
            log.error("[{}] 从云端获取桩运转信息失败。 request: {}", tid, request, throwable);
        });
    }

}
