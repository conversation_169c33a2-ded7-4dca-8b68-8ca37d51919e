package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.syncPost;

import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.model.gw.CloudUpRes;
import com.cdz360.iot.gw.model.response.GwRegisterRes;
import com.cdz360.iot.gw.north.model.GwLoginReq;
import com.cdz360.iot.gw.north.model.GwRegisterReq;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GatewayService {
    /**
     * 网关注册
     *
     * @param req
     * @return
     */
    public GwRegisterRes register(GwRegisterReq req) {
        log.info("网关注册。request: {}", req);

        GwRegisterRes result = null;
        CloudUpReq<GwRegisterReq> upReq = new CloudUpReq<>();
        upReq.setData(req);
        try {
            result = syncPost(upReq, ApiUrl.URL_GW_REGISTER, GwRegisterRes.class);
        } catch (Exception ex) {
            // logger.error("网关注册失败。request: {}。", req, ex);
            log.error("网关注册失败。request: {}。", req);
        }

        log.error("网关注册。request: {}, response: {}。", req, result);

        return result;
    }

    public CloudUpRes login(String traceId, String lanIp, String mac) {
        log.info("网关登录。 traceId: {}", traceId);
        GwLoginReq req = new GwLoginReq();
        req.setLanIp(lanIp)
                .setMac(mac)
                .setSeq(SeqGeneratorUtil.newStringId());
        CloudUpRes res = syncPost(req, ApiUrl.URL_LOGIN, CloudUpRes.class);

        log.info("登录结果。traceId: {}, result: {}", traceId, res);

        return res;
    }

}
