package com.cdz360.iot.gw.north.server;

import static com.cdz360.iot.gw.util.WebClientUtil.asyncPost;
import static com.cdz360.iot.gw.util.WebClientUtil.syncPost;
import static com.cdz360.iot.gw.util.WebClientUtil.syncPost2;
import static com.cdz360.iot.gw.util.WebClientUtil.syncPostRaw;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.NetFilter;
import com.cdz360.iot.gw.biz.ds.IotGwCacheService;
import com.cdz360.iot.gw.config.ApiUrl;
import com.cdz360.iot.gw.config.GwConfigProperties;
import com.cdz360.iot.gw.model.base.BasePlatformResponse;
import com.cdz360.iot.gw.model.gw.CloudUpReq;
import com.cdz360.iot.gw.model.gw.CloudUpRes;
import com.cdz360.iot.gw.north.model.AuthRequest;
import com.cdz360.iot.gw.north.model.EvseCfgResultRequest;
import com.cdz360.iot.gw.north.model.EvseStatusRequest;
import com.cdz360.iot.gw.north.model.GetEvseListRes;
import com.cdz360.iot.gw.north.model.GetPlugListRes;
import com.cdz360.iot.gw.north.model.GwLoginReq;
import com.cdz360.iot.gw.north.model.ListEvseParam;
import com.cdz360.iot.gw.north.model.ListPlugParam;
import com.cdz360.iot.gw.north.model.OrderCreateData;
import com.cdz360.iot.gw.north.model.OrderCreateResponse;
import com.cdz360.iot.gw.north.model.OrderFeeRefreshData;
import com.cdz360.iot.gw.north.model.OrderFeeRefreshResponse;
import com.cdz360.iot.gw.north.model.OrderResponse;
import com.cdz360.iot.gw.north.model.OrderStartData;
import com.cdz360.iot.gw.north.model.OrderStopData;
import com.cdz360.iot.gw.north.model.OrderUpdateData;
import com.cdz360.iot.gw.north.model.RequestAuthResultResponse;
import com.cdz360.iot.gw.util.SHA256Util;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import com.fasterxml.jackson.databind.JsonNode;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * 接入到云平台的client端
 */
@Slf4j
@Service
public class IotClient {

    @Autowired
    private WebClient.Builder webClientBuilder;

    @Autowired
    private IotGwCacheService cacheService;

    @Autowired
    private GwConfigProperties gwConfig;

    @Autowired
    private NetFilter filter;

    @Autowired
    private GatewayService gatewayService;

    public boolean syncLogin(String lanIp, String mac) {
        String tid = SeqGeneratorUtil.newStringId();

        // 是否成功注册
        String token = cacheService.getToken(gwConfig.getGwno());

        log.info("[{}] 网关登录中. gwno: {}, token: {}", tid, gwConfig.getGwno(), token);

        try {
            // 注册
            if (null == token) {
                if (!this.register(tid)) { // 同步
                    return false;
                } else {
                    log.info("[{}] 网关注册成功。 gwno: {}.", tid, gwConfig.getGwno());
                }
            }

            CloudUpRes res = gatewayService.login(tid, lanIp, mac);

            if (res == null || res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                log.error("[{}] 登录失败. token: {}, gwno: {}, result: {}", tid, token,
                    gwConfig.getGwno(), res);
                return false;
            }

            // 状态码处理
            if (res.getStatus() != 0) {
                log.info("[{}] 登录失败，平台返回的status错误。 status: {}.", tid, res.getStatus());

                // 清空缓存
                cacheService.remove(gwConfig.getGwno());
            }

            log.info("[{}] 网关登录成功. res: {}.", tid, res);

            //result = res.getData();

        } catch (Exception e) {
            log.error("[{}] 网关登录失败.", tid, e);
            return false;
        }
        return true;
        // return result;
    }


    /**
     * 发送post请求(url)
     *
     * @param msg
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T sendMsgPostByUrl(Object msg, String url, Class<T> clazz) {
        log.info("send to cloud post(发送): {}", JsonUtils.toJsonString(msg));

        try {
            T res = syncPost2(msg, url, clazz);
            return res;
        } catch (Exception e) {
            log.error("fail: {}", e.getMessage());
            // 网络异常
            // filter.callback(JsonUtils.toJsonString( msg));
            return null;
        }
    }

    //非透传离线后恢复上报
    public String offlineSendMsg(String json) {
        log.info("离线文件上报：{}", json);
        String res = null;
        try {
            JsonNode jsonNode;
            try {
                jsonNode = JsonUtils.fromJson(json);
            } catch (Exception e) {
                log.error("json 解析失败. error: {}", e.getMessage());
                return "json解析失败";
            }

            if (jsonNode.get("url") == null || StringUtils.isEmpty(jsonNode.get("url").asText())) {
                log.error("离线文件无法恢复，无法判断恢复类型。request: {}", json);
                return null;
            }

            switch (jsonNode.get("url").asText()) {
                case ApiUrl.URL_GW_REGISTER:
                    break;
                case ApiUrl.URL_LOGIN:
                    break;
                case ApiUrl.URL_CFG_EVSE:
                    break;
                case ApiUrl.URL_CFG_EVSE_RESULT:
                    EvseCfgResultRequest evseCfgResultRequest = JsonUtils.fromJson(json,
                        EvseCfgResultRequest.class);
                    BasePlatformResponse cfgEvseResult = this.sendMsgPostByUrl(evseCfgResultRequest,
                        ApiUrl.URL_CFG_EVSE_RESULT, BasePlatformResponse.class);
                    res = JsonUtils.toJsonString(cfgEvseResult);
                    break;
                case ApiUrl.URL_EVSE_STATUS:
                    EvseStatusRequest evseStatusRequest = JsonUtils.fromJson(json,
                        EvseStatusRequest.class);
                    BasePlatformResponse evseStatusRes = this.sendMsgPostByUrl(evseStatusRequest,
                        ApiUrl.URL_EVSE_STATUS, BasePlatformResponse.class);
                    res = JsonUtils.toJsonString(evseStatusRes);
                    break;
                case ApiUrl.URL_CREATE_ORDER:
                    OrderCreateData orderCreateRequest = JsonUtils.fromJson(json,
                        OrderCreateData.class);
                    OrderCreateResponse orderCreateRes = syncPostRaw(orderCreateRequest,
                        ApiUrl.URL_CREATE_ORDER, OrderCreateResponse.class);
                    res = JsonUtils.toJsonString(orderCreateRes);
                    break;
                case ApiUrl.URL_START_ORDER:
                    OrderStartData orderStartRequest = JsonUtils.fromJson(json,
                        OrderStartData.class);
                    OrderResponse orderStartRes = syncPost(orderStartRequest,
                        ApiUrl.URL_START_ORDER, OrderResponse.class);
                    res = JsonUtils.toJsonString(orderStartRes);
                    break;
                case ApiUrl.URL_STOP_ORDER:
                    OrderStopData orderStopRequest = JsonUtils.fromJson(json, OrderStopData.class);
                    OrderResponse orderStopRes = syncPost(orderStopRequest, ApiUrl.URL_STOP_ORDER,
                        OrderResponse.class);
                    res = JsonUtils.toJsonString(orderStopRes);
                    break;
                case ApiUrl.URL_UPDATE_ORDER:
                    OrderUpdateData orderUpdateRequest = JsonUtils.fromJson(json,
                        OrderUpdateData.class);
                    OrderResponse orderUpdateRes = syncPost(orderUpdateRequest,
                        ApiUrl.URL_UPDATE_ORDER, OrderResponse.class);
                    res = JsonUtils.toJsonString(orderUpdateRes);
                    break;
                case ApiUrl.URL_REQUEST_AUTH:
                    AuthRequest authRequest = JsonUtils.fromJson(json, AuthRequest.class);
                    RequestAuthResultResponse authRes = syncPostRaw(authRequest,
                        ApiUrl.URL_REQUEST_AUTH, RequestAuthResultResponse.class);
                    res = JsonUtils.toJsonString(authRes);
                    break;
                // case ApiUrl.URL_STARTING_ORDER:
                //     OrderStartingData startingRequest = JsonUtils.fromJson(json, OrderStartingData.class);
                //     OrderResponse startingResoponse = syncPostRaw(startingRequest, ApiUrl.URL_STARTING_ORDER, OrderResponse.class);
                //     res = JsonUtils.toJsonString(startingResoponse);
                //     break;
                case ApiUrl.URL_ORDER_FEE_REFRESH:
                    OrderFeeRefreshData refreshRequest = JsonUtils.fromJson(json,
                        OrderFeeRefreshData.class);
                    OrderFeeRefreshResponse feeRefreshResponse = syncPostRaw(refreshRequest,
                        ApiUrl.URL_ORDER_FEE_REFRESH, OrderFeeRefreshResponse.class);
                    res = JsonUtils.toJsonString(feeRefreshResponse);
                    break;
                default:
                    break;
            }

            return res;
        } catch (Exception e) {
            log.error("离线文件上报失败。", e);
            // 中止上传
            filter.breakOff();
            return null;
        }
    }


    /**
     * 网关登录 -- 先注册(生成token)
     */
    public Boolean register(String tid) {
        log.debug("[{}] 网关注册。 gateway number: {}.", tid, gwConfig.getGwno());

        // 登录前注册
        WebClient webClient = webClientBuilder.build();

        GwLoginReq req = new GwLoginReq();
        req.setSeq(SeqGeneratorUtil.newStringId());

        Mono<ClientResponse> response = webClient.post()

            .uri(ApiUrl.URL_LOGIN, gwConfig.getVersion(), gwConfig.getGwno()).syncBody(req)
            .accept(MediaType.APPLICATION_JSON).exchange();

        // 捕获网络处理异常
        Optional<ClientResponse> clientResponse = Optional.empty();
        try {
            clientResponse = response.blockOptional();
        } catch (Exception e) {
            log.error("[{}] 网关注册失败。 {}.", tid, e.getMessage());
            return false;
        }

        if (!clientResponse.isPresent()) {
            log.debug("[{}] Client Response is empty.", tid);
            return false;
        }

        if (HttpStatus.UNAUTHORIZED == clientResponse.get().statusCode()) {
            List<String> authList = response.block().headers().header("WWW-Authenticate");
            String realm = authList.get(0).substring(12).replaceAll("\"", ""); // 截取 realm
            log.debug("[{}] gw receive realm: {}.", tid, realm);

            log.info("[{}] gw register success.", tid);
            try {
                // 生成token[SHA256(网关编号:密码:realm)]并缓存本地
                String newToken = SHA256Util.generate(
                    gwConfig.getGwno() + ":" + gwConfig.getPasscode() + ":" + realm);
                cacheService.updateToken(gwConfig.getGwno(), newToken);
                log.info("[{}] token已更新。 gwno={}, passcode={}, realm={}, new token: {}.", tid,
                    gwConfig.getGwno(), gwConfig.getPasscode(), realm, newToken);
            } catch (NoSuchAlgorithmException e) {
                log.error("[{}] SHA256Util.generate error: {}.", tid, e.getMessage());
                log.info("<<");
                return false;
            }
            //            }

            log.info("<<");
            return true;
        } else {
            log.info("[{}] first time login: status={}", tid, clientResponse.get().statusCode());
        }

        log.info("<<");
        return false;
    }


    /**
     * 发送下行指令的执行结果
     *
     * @param request
     * @return
     */
    public void sendCmdResult(String tid, CloudUpReq<?> request) {

        log.info("[{}] 上报下行指令的执行结果： request: {}", tid, request);
        asyncPost(ApiUrl.URL_CMD_RESULT, request, CloudUpRes.class).doOnNext(r -> {
            log.info("[{}] 上报下行指令的执行结果返回： response: {}", tid, r);

            if (r == null || r.getStatus() != 0) {
                // throw new RuntimeException("创建订单失败");//不要抛出自定义的运行时异常，会破坏后面的调用链
                log.error("[{}] 上报下行指令的执行结果失败。 response: {}", tid, r);
            }

            if (r == null) {
                filter.callback(JsonUtils.toJsonString(request));
            }

        }).doOnError(throwable -> {
            log.error("[{}] 上报下行指令的执行结果失败。 request:{}", tid, request, throwable);
            filter.callback(JsonUtils.toJsonString(request));
        }).subscribe();
    }


    public Mono<GetEvseListRes> getEvseList(String tid, String siteId, Long tfmId) {
        ListEvseParam param = new ListEvseParam();
        param.setSiteIdList(List.of(siteId)).setTfmId(tfmId).setStart(0L).setSize(9999);
        CloudUpReq<ListEvseParam> request = new CloudUpReq();
        request.setSeq(SeqGeneratorUtil.newStringId()); // 序列号
        request.setData(param);
        log.info("{} 获取桩列表 req = {}", tid, request);

        return asyncPost(ApiUrl.URL_SITE_GET_EVSE_LIST, request, GetEvseListRes.class).doOnNext(
            result -> {
                if (null != result && result.getStatus() == 0) {
                    //不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.info("[{}] 获取桩列表成功。response: {}", tid, result);
                } else {
                    log.error("[{}] 获取桩列表失败。 response: {}", tid, result);
                }
            }).doOnError(throwable -> {
            log.error("[{}] 获取桩列表发生错误。fail: {}", tid, throwable.getMessage(), throwable);
            // filter.callback(JsonUtils.toJsonString(request));
        });
    }


    public Mono<GetPlugListRes> getPlugList(String tid, ListPlugParam param) {
        CloudUpReq<ListPlugParam> request = new CloudUpReq();
        request.setSeq(SeqGeneratorUtil.newStringId()); // 序列号
        request.setData(param);
        log.info("[{}] 获取枪头列表 req = {}", tid, request);

        return asyncPost(ApiUrl.URL_SITE_GET_PLUG_LIST, request, GetPlugListRes.class).doOnNext(
            result -> {
                if (null != result && result.getStatus() == 0) {
                    //不要抛出自定义的运行时异常，会破坏后面的调用链
                    log.info("[{}] 获取枪头列表成功。response: {}", tid, result);
                } else {
                    log.error("[{}] 获取枪头列表失败。 response: {}", tid, result);
                }
            }).doOnError(throwable -> {
            log.error("[{}] 获取枪头列表发生错误。fail: {}", tid, throwable.getMessage(), throwable);
            // filter.callback(JsonUtils.toJsonString(request));
        });
    }
}
