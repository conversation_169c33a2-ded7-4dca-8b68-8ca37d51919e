package com.cdz360.iot.gw.north.server;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.NetFilter;
import com.cdz360.iot.gw.biz.YamlOptService;
import com.cdz360.iot.gw.config.GwConfigProperties;
import com.cdz360.iot.gw.model.gw.MqttProperties;
import com.cdz360.iot.gw.model.response.GwRegisterRes;
import com.cdz360.iot.gw.north.model.GwRegisterReq;
import com.cdz360.iot.gw.north.mq.MqService;
import com.cdz360.iot.gw.north.mq.MqttService;
import com.cdz360.iot.gw.util.ByteUtil;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 发送上行请求到云平台的服务类
 */
@Service
public class IotUpService {
    private final Logger logger = LoggerFactory.getLogger(IotUpService.class);

    @Value("${cdz360.retry}")
    private Integer retry;

    @Autowired
    private GwConfigProperties gwConfig;

    @Autowired
    private YamlOptService yamlOptService;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private MqService mqService;

    @Autowired
    private IotClient iotClient;

    @Autowired
    private GatewayService gatewayService;

    @Autowired
    private NetFilter filter;

//    @Autowired
//    private GwBizService gwBizService;

    /**
     * 网关上线
     */
//    public void online() {
//        this.iotClient.register();
//    }

    /**
     * 网络检测: 登录注册
     */
    public void checkNet() {
        logger.info("Check Net");
        // 登录注册
        this.login();
    }

    /**
     * 网关初始化
     */
    public boolean register() {
        logger.info("网关注册......");

        GwRegisterReq req = new GwRegisterReq();

        req.setGwno(this.gwConfig.getGwno());   //.setMac(this.getMac()).setLocalIp(this.getLocalIp());
        String[] macAndIp = this.getMacAndIp();
        if (macAndIp != null) {
            logger.info("ip: {}, mac: {}", macAndIp[0], macAndIp[1]);
            req.setLanIp(macAndIp[0]).setMac(macAndIp[1]);
        } else {
            logger.error("无法获取到 IP 和 MAC 地址");
        }

        req.setSeq(SeqGeneratorUtil.newStringId());

        GwRegisterRes result = gatewayService.register(req);

        if (isInvalid(result)) {
            try {
                //等待几秒后重新初始化
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                logger.warn("register InterruptedException", e);
            }

            logger.info("注册时获取的云端结果不正确，尝试重新注册。req: {}, result: {}", req, result);

            return this.register();
        }

        // 更新网关编号和密码
        yamlOptService.updateGwInit(new HashMap<String, String>() {{
            put("gwno", result.getGwno());
            put("passcode", result.getPasscode());
        }});

        if (!this.mqService.updateProps(result.getGwno())) {
            try {
                logger.warn("register mq connect fail=================");
                //等待几秒后重新初始化
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                logger.warn("register InterruptedException", e);
            }

            logger.info("注册时MQ配置不正确，尝试重新注册。req: {}, result: {}", req, result);

            return this.register();
        }

        // 更新mqtt的配置
        MqttProperties mqttProps = new MqttProperties();
        mqttProps.setClientId(result.getMqClientId())
                .setUrl(result.getMqUrl())
                .setUsername(result.getMqUsername())
                .setPassword(result.getMqPasscode())
                .setTopic(result.getMqTopic());

        //如果 MQTT 连接失败, 网关等待N秒后使用下发的网关编号重新发起初始化请求
        if (!this.mqttService.updateProps(mqttProps)) {
            try {
                logger.warn("register mq connect fail=================");
                //等待几秒后重新初始化
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                logger.warn("register InterruptedException", e);
            }

            logger.info("注册时MQTT配置不正确，尝试重新注册。req: {}, result: {}", req, result);

            return this.register();

        }

        return true;
    }

    //要求所有的字段必须都要有值
    private boolean isInvalid(GwRegisterRes result) {
        return result == null || StringUtils.isEmpty(result.getGwno())

                || StringUtils.isEmpty(result.getPasscode());
    }

    private String[] getMacAndIp() {

        try {
            // NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            String[] result = this.getIpAndMacFromEth0();   // 先看有没有叫 eth0 的网卡
            if (result != null) {
                return result;
            }

            Enumeration<NetworkInterface> ifs = NetworkInterface.getNetworkInterfaces();
            while (ifs.hasMoreElements()) {
                NetworkInterface inf = ifs.nextElement();
                if (StringUtils.startsWithIgnoreCase(inf.getName(), "docker")) {
                    // 忽略掉docker创建的网卡
                    continue;
                }
                result = this.getFromNetworkInterface(inf);
                if (result != null) {
                    return result;
                }
            }

        } catch (SocketException e) {
            logger.error(e.getMessage(), e);
        }

        return null;
    }

    private String[] getIpAndMacFromEth0() {
        NetworkInterface inf = null;
        // String result[] = new String[2];
        try {
            inf = NetworkInterface.getByName("eth0");
            if (inf == null) {
                logger.info("eth0 not found");
                return null;
            }
            return this.getFromNetworkInterface(inf);
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 返回 ip 和 mac
     *
     * @param inf
     * @return
     */
    private String[] getFromNetworkInterface(NetworkInterface inf) {

        try {
            byte[] mac = inf.getHardwareAddress();
            if (mac == null) {
                return null;
            }
            String ip = this.getIp(inf);
            if (!StringUtils.isEmpty(ip)) {
                String[] result = new String[2];
                result[0] = ip;
                result[1] = ByteUtil.byte2Mac(mac);
                logger.info("<< result: {}", JsonUtils.toJsonString(result));
                return result;
            }
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
        }

        return null;
    }

    private String getIp(NetworkInterface inf) {
        Enumeration<InetAddress> addrs = inf.getInetAddresses();
        while (addrs.hasMoreElements()) {
            InetAddress addr = addrs.nextElement();

            logger.info("interface: {}, ip: {}, isSiteLocal: {}, isLinkLocal: {}",
                    inf.getName(),
                    addr.getHostAddress(), addr.isSiteLocalAddress(), addr.isLinkLocalAddress());
            if (addr.isSiteLocalAddress()) {
                return addr.getHostAddress();
            }
        }
        return null;
    }

    private String getLocalIp() {
        String ip = "";
        try {
            InetAddress ia = InetAddress.getLocalHost();
            ip = ia.getHostAddress();
        } catch (UnknownHostException e) {
            //e.printStackTrace();
            logger.error(e.getMessage(), e);
        }

        return ip;
    }

    public void login() {
        // 构造登录所需的信息, 然后调用iotClient发送
        // 失败后要做延迟重新登录
        int i = 0;
        boolean isLoginSuccess = false;
        //GwLoginRes res;
        while (i <= retry && !isLoginSuccess) {

            // delay
            try {
                Thread.sleep(this.delay(i++));
            } catch (InterruptedException e) {
                logger.error("Thread.sleep error: {}.", e.getMessage());
            }

            String lanIp = "";
            String mac = "";
            String [] ipMac = this.getMacAndIp();
            if(ipMac != null) {
                lanIp = ipMac[0];
                mac = ipMac[1];
            }
           // GwLoginRes res =
            isLoginSuccess = this.iotClient.syncLogin(lanIp, mac);

//            if (res != null) {
//
            if(isLoginSuccess) {
                isLoginSuccess = this.mqService.updateProps(gwConfig.getGwno());
                if (!isLoginSuccess) {
                    logger.error("MQ 连接失败!!!!!! ");
                }
            }
//
//                // 更新mqtt的配置
//                MqttProperties mqttProps = new MqttProperties();
//                mqttProps.setClientId(res.getMqClientId())
//                        .setUrl(res.getMqUrl())
//                        .setUsername(res.getMqUsername())
//                        .setPassword(res.getMqPasscode())
//                        .setTopic(res.getMqTopic());
//                isLoginSuccess = this.mqttService.updateProps(mqttProps);
//                if (!isLoginSuccess) {
//                    logger.error("MQTT连接失败。 mqtt config: {}", res);
//                }
//            }
        }

        // 网络正常后
        if (isLoginSuccess) {
            this.filter.netRecover();
//            this.gwBizService.postLogin();
        } else {
            // 默认离线状态
            this.filter.changeNetStatus(false);

            // 登录不成功，启动网络检测定时任务
            this.filter.startCheckNetTask();
        }
    }

    /**
     * 再次登录延迟时间
     * 0 1 2 4 8 16 32 64 128 256 512 512 512 ...
     *
     * @param retryTime 重试次数
     * @return
     */
    private Integer delay(Integer retryTime) {
        logger.debug("网关重试延迟时间: {}", retryTime);

        // ==0
        if (retryTime == 0) {
            return 0;
        }

        // ==1
        if (retryTime == 1) {
            return 1;
        }

        // >= 10
        if (retryTime >= 10) {
            return 512;
        }

        // >0 && <10
        return 1 << (retryTime - 1);
    }
}
