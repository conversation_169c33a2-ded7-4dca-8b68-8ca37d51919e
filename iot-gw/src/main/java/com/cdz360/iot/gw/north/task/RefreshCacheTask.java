package com.cdz360.iot.gw.north.task;

import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.util.SeqGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RefreshCacheTask {

    @Autowired
    private JsonServerImpl jsonServer;

    //    @Scheduled(cron = "0 30 15 * * ? ")
    public void manualTask() {
        String tid = SeqGeneratorUtil.newStringId();
        String evseNo = "CP001";
        jsonServer.sendClearCacheRequest(tid, evseNo);
    }

}
