package com.cdz360.iot.gw.south.handler;

import com.cdz360.iot.gw.biz.NetFilter;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.model.converter.AuthCodeConverter;
import com.cdz360.iot.gw.model.evse.protocol.ChargeAuthUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.north.model.AuthData;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.RequestAuthResultResponse;
import com.cdz360.iot.gw.north.server.AuthService;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.AuthorizationStatus;
import eu.chargetime.ocpp.model.core.AuthorizeConfirmation;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ChargeAuthHandler extends UpstreamHandler2 {


    @Autowired
    NetFilter filter;

    //    @Autowired
//    private AuthResultCommander authResultHandler;
    @Autowired
    private AuthService authProcess;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.AUTH_REQUEST;
    }


    @Override
    public Mono<EvseTranx<BaseEvseMsgUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        log.info("[{}] 鉴权请求。", msgIn.getBase().getTid());
        ChargeAuthUp authReq = (ChargeAuthUp) msgIn;

        OrderData order = evseOrderRepository.getOrder(authReq.getAccount());
        if (order != null) {
            log.info("[{}] 订单已存在无需鉴权。 orderStatus: {}", msgIn.getBase().getTid(),
                order.getOrderStatus());
            return Mono.just(new IdTagInfo(AuthorizationStatus.Accepted)).map(
                e -> new EvseTranx<>(authReq.getBase().getEvseNo(), authReq,
                    new AuthorizeConfirmation(e)));
        }

        AuthData data = new AuthData();
        data.setVin(authReq.getVin());
        data.setAccountNo(authReq.getAccount()); // 账号
        data.setAuthType((byte) authReq.getAuthType().getCode()); // 鉴权类型
        data.setEvseNo(authReq.getBase().getEvseNo());  // 桩编号
        data.setPlugId(authReq.getBase().getPlugIdx());  // 枪口编号

        return authProcess.processCardAuth(msgIn.getBase().getTid(), data)
            .map(authResponse -> mapAuthResponse(authReq, authResponse))
            .map(authResult -> {
                return new EvseTranx<>(authReq.getBase().getEvseNo(), authReq, authResult);
            });
    }

    private AuthorizeConfirmation mapAuthResponse(ChargeAuthUp authReq,
        RequestAuthResultResponse response) {
        int status = ConstantCollections.CLOUD_AUTH_UNKNOWN_FAILURE;
        if (response != null) {
            status = response.getStatus();
        }
        IdTagInfo tagInfo = new IdTagInfo(AuthCodeConverter.convert(status));
        return new AuthorizeConfirmation(tagInfo);
    }

}
