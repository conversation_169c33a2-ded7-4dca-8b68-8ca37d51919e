package com.cdz360.iot.gw.south.handler;

import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.south.server.ChargeHbQueue;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.MeterValuesConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * 上行 枪头数据上报 6807
 */
@Slf4j
@Component
public class ChargeHbHandler extends UpstreamHandler2 {


    @Autowired
    private ChargeHbQueue heartbeatQueue;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.CHARGE_HB;
    }

    @Override
    public Mono<EvseTranx<ChargeHbUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        ChargeHbUp upMsg = (ChargeHbUp) msgIn;

        heartbeatQueue.push(upMsg);

        MeterValuesConfirmation confirmation = new MeterValuesConfirmation(upMsg.getBase());

        return Mono.just(new EvseTranx<>(upMsg.getBase().getEvseNo(), upMsg, confirmation));

    }

}
