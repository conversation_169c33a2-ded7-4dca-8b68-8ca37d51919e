package com.cdz360.iot.gw.south.handler;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.core.biz.OrderNoGenerator;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.converter.AuthCodeConverter;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStartedUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.model.type.CalcType;
import com.cdz360.iot.gw.model.type.ConstantCollections;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderResponse;
import com.cdz360.iot.gw.north.model.OrderStartData;
import com.cdz360.iot.gw.south.server.OrderAdaptationService;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.MeterValuesConfirmation;
import eu.chargetime.ocpp.model.core.StartTransactionConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 上行 桩上报充电启动结果 6805
 */
@Slf4j
@Component
public class ChargeStartedHandler extends UpstreamHandler2 {


    @Autowired
    private OrderBizService orderService;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private OrderBizService orderBizService;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private OrderAdaptationService orderAdaptationService;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.CHARGE_START;
    }

    @Override
    public Mono<EvseTranx<ChargeStartedUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        String tid = msgIn.getBase().getTid();
        String evseNo = msgIn.getBase().getEvseNo();
        log.info("[{} {}] 桩端开始充电. ", tid, evseNo);

        ChargeStartedUp req = (ChargeStartedUp) msgIn;
        OrderStartData startRequest = makeRequest(req);

        return orderService.orderStarted(tid, startRequest)
            .map(response -> {
                if (response == null || response.getStatus() != ConstantCollections.OK) {
                    log.error("[{} {}] 上传充电开始失败, 云端返回结果非预期。 response: {}",
                        tid, evseNo, response);
                } else {

                    String orderNo = req.getOrderNo();
                    OrderData data = evseOrderRepository.getOrder(orderNo);
                    if (data == null) {
                        data = orderAdaptationService.assembleEvseAutoOrderData(tid, req);
                    }
                    data.setStartTime(req.getStartTime());
                    data.setStartMeter(req.getStartMeter());
                    data.setStopMode(req.getStopMode()); // 停止方式
                    data.setOrderStatus(ChargeOrderStatus.START);
                    data.setUploadHb(true); // 设置订单已经启动的标志位, 设置后心跳数据会立即上报
                    OrderData orderData = evseOrderRepository.updateOrder(data); //add or update

                    this.fetchAndSaveOrderPriceInfo(tid, orderData.getOrderNo(), evseNo);

                    log.info("[{} {}] 上传充电开始到平台。 response: {}", tid, evseNo, response);
                }

                Integer status = Optional.ofNullable(response).map(OrderResponse::getStatus)
                    .orElse(null);
                IdTagInfo idTagInfo = new IdTagInfo(AuthCodeConverter.convert(status));
                Confirmation confirmation = this.assembleConfirmation(req.getTransId(), idTagInfo,
                    req.isStartTransReq());
                confirmation.setBase(req.getBase());

                return new EvseTranx<>(req.getBase().getEvseNo(), req, confirmation);
            });

    }

    /**
     * 将计费价格保存到订单上
     *
     * @param tid
     * @param orderNo
     * @param evseNo
     */
    public void fetchAndSaveOrderPriceInfo(String tid, String orderNo, String evseNo) {
        log.info("[{} {}] 将计费价格保存到订单上. orderNo: {}", tid, evseNo, orderNo);
        OrderData data = new OrderData(orderNo);

        GwEvseVo evseCache = evseRepository.getEvse(evseNo);
        log.info("[{} {}] 获取到桩缓存信息. evseCache: {}", tid, evseNo,
            JsonUtils.toJsonString(evseCache));
        if (evseCache.getPriceCode() != null && evseCache.getPrice() != null) {
            data.setPriceCode(evseCache.getPriceCode());
            data.setPrice(evseCache.getPrice());

            evseOrderRepository.updateOrder(data);
            return;
        }

        log.error("[{} {}] 未获取到桩本地计费信息, 从云端获取. orderNo: {}", tid, evseNo, orderNo);
        // TODO: 2025/4/21 WZFIX 应该直接获取订单上的计费信息，而不是桩缓存的计费
        orderBizService.fetchEvseCloudPrice(tid, evseNo).doOnNext(priceVo -> {
            data.setPriceCode(priceVo.getId());

            data.setPrice(priceVo);
            evseOrderRepository.updateOrder(data);

            evseRepository.updateEvsePrice(evseNo, priceVo);
        }).subscribe();
    }

    public Confirmation assembleConfirmation(Integer transId, IdTagInfo idTagInfo,
        boolean startTransReqAto) {
        if (startTransReqAto) {
            if (transId == null) {
                // 不存在就生成一个网关订单号返回给桩
                transId = orderNoGenerator.generateOrderNumber().getRight();
            }
            return new StartTransactionConfirmation(idTagInfo, transId);
        }
        return new MeterValuesConfirmation();
    }

    private OrderStartData makeRequest(ChargeStartedUp req) {
        OrderStartData startRequest = new OrderStartData();
        startRequest.setEvseNo(req.getBase().getEvseNo());
        startRequest.setPlugId(req.getBase().getPlugIdx());
        startRequest.setStartType(req.getOrderStartType());
        startRequest.setStartResult(req.getOrderStartResult());

        // 费用抵扣方式
        startRequest.setCalcType(CalcType.valueOf((byte) req.getFeeDM()));

        if (startRequest.getStartType() == OrderStartType.ONLINE_VIN) {
            startRequest.setVin(req.getAccountNo());
            startRequest.setAccountNo(req.getAccountNo());
        } else {
            startRequest.setAccountNo(req.getAccountNo());
        }

        startRequest.setStartTime(req.getStartTime());
        startRequest.setSoc(req.getSoc());
        startRequest.setStopMode(req.getStopMode());
        startRequest.setOrderNo(req.getOrderNo());//需要传到云端的订单号
        if (req.getInsulation() != null) {
            OrderStartData.Insulation insulation = new OrderStartData.Insulation();
            insulation.setResult(req.getInsulation().getResult());
            insulation.setNegative(req.getInsulation().getNegative());
            insulation.setPositive(req.getInsulation().getPositive());
            insulation.setVoltage(req.getInsulation().getVoltage());
            startRequest.setInsulation(insulation);
        }
        startRequest.setBms(req.getBms());
        startRequest.setBattery(req.getBattery());
        return startRequest;
    }

}
