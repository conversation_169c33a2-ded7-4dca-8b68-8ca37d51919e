package com.cdz360.iot.gw.south.handler;

import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Scope(value = "singleton")
public class CommandFactory {

    private final Map<Byte, UpstreamHandler2> cacheMap = new ConcurrentHashMap<>();

    @Autowired
    ApplicationContext applicationContext;

    public UpstreamHandler2 getHandler(BaseEvseMsgUp request) {
        EvseMessageType type = request.getBase().getCmdCode();

        return getHandler(type.getCode());
    }

    public UpstreamHandler2 getHandler(byte code) {

        if (cacheMap.containsKey(code)) {
            return cacheMap.get(code);
        }

        //获取Spring容器中所有继承自CommandHandler的类的实例
        Map<String, UpstreamHandler2> map = applicationContext.getBeansOfType(
            UpstreamHandler2.class);

        for (Map.Entry<String, UpstreamHandler2> stringCommandHandlerEntry : map.entrySet()) {
            UpstreamHandler2 handler = stringCommandHandlerEntry.getValue();
            EvseMessageType cmd = handler.cmd();
            if (code == cmd.getCode()) {
                if (!cacheMap.containsKey(code)) {
                    cacheMap.put(code, handler);
                }
                return handler;
            }
        }

        return null;
    }

}
