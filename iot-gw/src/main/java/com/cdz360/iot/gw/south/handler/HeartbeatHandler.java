package com.cdz360.iot.gw.south.handler;

import com.cdz360.iot.gw.model.evse.protocol.EvseHbMsgUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.south.server.HeartbeatService;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.StatusNotificationConfirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * 心跳报文处理类
 */
@Slf4j
@Component
public class HeartbeatHandler extends UpstreamHandler2 {


    @Autowired
    private HeartbeatService heartbeatService;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.EVSE_HB;
    }

    @Override
    public Mono<EvseTranx<EvseHbMsgUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        EvseHbMsgUp upMsg = (EvseHbMsgUp) msgIn;
        return Mono.just(upMsg).doOnNext(reqMsg -> {
            heartbeatService.processHeartBeat(reqMsg);
        }).map(hbReq -> {
            StatusNotificationConfirmation confirmation = new StatusNotificationConfirmation();
            return new EvseTranx<>(hbReq.getBase().getEvseNo(), hbReq, confirmation);
        });
    }

}
