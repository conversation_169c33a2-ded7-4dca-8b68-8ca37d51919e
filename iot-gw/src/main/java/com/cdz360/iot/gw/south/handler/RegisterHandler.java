package com.cdz360.iot.gw.south.handler;

import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.GwConfigProperties;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.base.CloudCommonResponse;
import com.cdz360.iot.gw.model.evse.protocol.EvseRegisterUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.north.model.GetEvseOpInfo;
import com.cdz360.iot.gw.north.server.EvseService;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.BootNotificationConfirmation;
import eu.chargetime.ocpp.model.core.RegistrationStatus;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import eu.chargetime.ocpp.model.remotetrigger.TriggerMessageRequestType;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class RegisterHandler extends UpstreamHandler2 {

    private static final Integer INTERVAL = 30; // 桩心跳间隔，单位秒

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Autowired
    private EvseService evseService;

    @Autowired
    private GwConfigProperties gwConfig;

    @Autowired
    private JsonServerImpl jsonServerImpl;

    @Override
    public EvseMessageType cmd() {
        return EvseMessageType.EVSE_REGISTER;
    }

    @Override
    public Mono<EvseTranx<BaseEvseMsgUp, Confirmation>> processRequest(BaseEvseMsgUp msgIn) {
        String tid = msgIn.getBase().getTid();
        String evseNo = msgIn.getBase().getEvseNo();
        log.info("[{}] 桩注册. evseNo: {}", tid, evseNo);
        EvseRegisterUp evseReq = (EvseRegisterUp) msgIn;

        ZonedDateTime now = ZonedDateTime.now();
        BootNotificationConfirmation bootNotification = new BootNotificationConfirmation();
        bootNotification.setBase(evseReq.getBase());
        bootNotification.setCurrentTime(now);

        GetEvseOpInfo opInfoReq = new GetEvseOpInfo();
        opInfoReq.setEvseNo(evseNo);
        return evseService.getEvseOpInfo(tid, opInfoReq)
            .filter(e -> e.getData() != null)
            .map(CloudCommonResponse::getData)
            .map(opInfo -> {
                evseReq.setPlugNum(opInfo.getPlugNum());
                evseReq.setSupplyType(opInfo.getSupply());

                GwEvseVo vo = new GwEvseVo().setNet(evseReq.getNetType());
                vo.setEvseNo(evseNo).setGwno(gwConfig.getGwno()).setPlugNum(evseReq.getPlugNum())
                    .setProtocolVer(evseReq.getProtocolVersion())
                    .setSupplyType(evseReq.getSupplyType());
                vo.setBrand(
                    opInfo.getBrand() == null ? null : EvseBrand.getByDesc(opInfo.getBrand()));
                vo.setTimeZone(
                    Optional.ofNullable(opInfo.getTimeZone()).orElse(now.getOffset().getId()));

//                String firmware = MessageFormat.format("{0}-{1}-{2}", evseReq.getPc01Sw(),
//                    evseReq.getPc02Sw(), evseReq.getPc03Sw());
                if (EvseBrand.WINLINE.equals(vo.getBrand())) {
                    Optional.ofNullable(evseReq.getFirmwareVersion()).map(e -> e.split("-"))
                        .ifPresent(x -> {
                            if (x.length >= 5) {
                                vo.setFirmwareVer(x[0]) // TCU版本信息
                                    .setPc01Ver(
                                        x[0] + "-" + x[1] + "-" + x[2]) // TCU版本信息-CCM版本信息-OCPP版本信息
                                    .setPc02Ver(x[3]) // SECC_A版本信息
                                    .setPc03Ver(x[4]); // SECC_B版本信息
                            } else if (x.length > 0) {
                                vo.setFirmwareVer(x[0]);
                                vo.setPc01Ver(x[0]);
                            }
                        });
                } else {
                    Optional.ofNullable(evseReq.getFirmwareVersion()).ifPresent(x -> {
                        vo.setFirmwareVer(x);
                        vo.setPc01Ver(x);
                    });
                }

                this.evseRepository.onlineEvse(tid, vo, evseReq);// 缓存桩的信息（到缓存和sqlite）

                evseStatusReportService.registerEvse(tid, vo, evseReq);

                bootNotification.setCurrentTime(
                    OcppZonedDateTime.issueTime(vo.getBrand(), vo.getTimeZone()));
                bootNotification.setInterval(INTERVAL);
                bootNotification.setStatus(RegistrationStatus.Accepted);
                return bootNotification;
            }).doOnNext(x -> {

                EvseMsgBase base = evseReq.getBase();
                int plugNum = evseReq.getPlugNum();
                // 桩上所有枪都触发状态上报
                IntStream.range(1, plugNum + 1).forEach(idx -> {
                    base.setPlugIdx(idx);
                    jsonServerImpl.sendTriggerMessageReq(base, null,
                        TriggerMessageRequestType.StatusNotification);
                });
            }).switchIfEmpty(Mono.just("[{}] 获取不到有效桩信息. evseNo: {}").map(msg -> {
                log.warn(msg, tid, evseNo);

                bootNotification.setStatus(RegistrationStatus.Rejected);
                return bootNotification;
            }))
            .map(x -> (new EvseTranx<>(evseNo, evseReq, x)));
    }

}
