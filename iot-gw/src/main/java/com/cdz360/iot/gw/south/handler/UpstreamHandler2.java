package com.cdz360.iot.gw.south.handler;

import com.cdz360.iot.gw.model.evse.protocol.EvseTranx;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import reactor.core.publisher.Mono;

public abstract class UpstreamHandler2 {
    public abstract EvseMessageType cmd();

    public abstract <REQ extends BaseEvseMsgUp, RES extends Confirmation>
    Mono<EvseTranx<REQ, RES>> processRequest(BaseEvseMsgUp request);
}
