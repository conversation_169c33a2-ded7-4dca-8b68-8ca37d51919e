package com.cdz360.iot.gw.south.server;

import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ChargeHbProcessor implements Runnable {

    private LinkedBlockingQueue<ChargeHbUp> queue = new LinkedBlockingQueue<>();
    private AtomicLong queueSize = new AtomicLong(0L);

    private ChargeHbProcessCallback<ChargeHbUp> callback;


    public ChargeHbProcessCallback<ChargeHbUp> getCallback() {
        return callback;
    }

    public void setCallback(ChargeHbProcessCallback<ChargeHbUp> callback) {
        this.callback = callback;
    }

    void push(ChargeHbUp msg) {
        try {
            this.queue.put(msg);
            queueSize.addAndGet(1L);
        } catch (InterruptedException e) {
            log.error("queue.size: {}, error: {}", queue.size(), e.getMessage(), e);
        }
    }

    @Override
    public void run() {
        do {
            ChargeHbUp request = null;
            try {
                request = queue.poll(1L, TimeUnit.SECONDS);
                if (request != null) {
                    long size = queueSize.addAndGet(-1L);
                    if (size > 1000 && size % 100 == 0) {
                        log.warn("堆积过大. size: {}", size);
                    }

                    if (callback != null) {
                        callback.onArrived(request);
                    }
                }
            } catch (Exception e) {
                log.error("queue.size: {}, error: {}", queue.size(), e.getMessage(), e);
                if (request != null) {

                    if (callback != null) {
                        callback.onFailed(request, e);
                    }

                }
            }
        } while (true);
    }
}
