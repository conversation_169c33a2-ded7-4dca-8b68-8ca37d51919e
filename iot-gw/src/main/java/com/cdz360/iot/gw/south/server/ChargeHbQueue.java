package com.cdz360.iot.gw.south.server;

import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ChargeHbQueue {

    private static final List<ChargeHbProcessor> processorList = new ArrayList<>();
    @Autowired
    private ChargeHbService heartbeatService;
    @Value("${cdz360.heartbeat.processor-threads:8}")
    private int THREAD_SIZE;

    @PostConstruct
    public void init() {

        for (int i = 0; i < THREAD_SIZE; i++) {
            ChargeHbProcessor processor = new ChargeHbProcessor();
            processor.setCallback(new ChargeHbProcessCallback<ChargeHbUp>() {
                @Override
                public void onArrived(ChargeHbUp iotRequest) {
                    heartbeatService.processHeartBeat(iotRequest);
                }

                @Override
                public void onFailed(ChargeHbUp iotRequest, Exception ex) {
                    log.error("[{}] 上报心跳数据失败。", iotRequest.getBase().getTid(), ex);
                }
            });

            startProcessor(processor);

            processorList.add(processor);
        }
    }

    private void startProcessor(ChargeHbProcessor processor) {
        // new Thread(processor).start();

        // Executors.newSingleThreadExecutor().execute(processor);

        ExecutorService executorService = new ThreadPoolExecutor(1,
            1,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(),
            new ChargeHbThreadFactory());

        new FinalizableDelegatedExecutorService(executorService).execute(processor);
    }

    public void push(ChargeHbUp iotRequest) {
        log.info("push chKey: {}", iotRequest.getBase().getChKey());
        int key = hashKey(iotRequest.getBase().getChKey(), THREAD_SIZE);

        // logger.info("key: {}", key);

        ChargeHbProcessor processor = processorList.get(key);
        processor.push(iotRequest);
    }

    private int hashKey(String key, int total) {
        return Math.abs((int) channelToLong(key) % total);
    }

    private long channelToLong(String channelKey) {
        // String channelKey = "/117.255.255.225:58303";
        String text = channelKey.replace("/", "").replace(".", "");
        String[] numbers = text.split(":");

        long value = 0L;
        if (numbers.length >= 2) {
            value = Long.parseLong(numbers[0]) + Long.parseLong(numbers[1]);
        }

        // logger.info("value: {}", value);

        return value;
    }
}
