package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.CacheManagerService;
import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.core.biz.OrderBizService;
import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStopDown;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderDetail;
import com.cdz360.iot.gw.north.model.OrderTimeIntervalDetail;
import com.cdz360.iot.gw.north.model.OrderUpdateData;
import com.cdz360.iot.gw.north.server.NorthOrderService;
import com.cdz360.iot.gw.util.PriceUtil;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class ChargeHbService {

    private final static Integer DETAIL_MAX_SIZE = 20;

    private final static String GW_EVSE_HB = "HB:EVSE";
    private static final List<PlugStatus> PLUG_BUSY_STATUS_LIST = List.of(PlugStatus.BUSY,
        PlugStatus.JOIN, PlugStatus.RECHARGE_END);

    @Autowired
    private IdempotenceService idempotenceService;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Autowired
    private NorthOrderService orderService;

    @Autowired
    private CacheManagerService cacheManagerService;

    @Autowired
    private OrderBizService orderBizService;

    @Value("${cdz360.timer.heartbeat-timeout:120000}")
    private int heartbeatTimeout;

    @Value("${cdz360.merge-num:4}")
    private int mergeNum;//合并发送数

    /**
     * 根据传入的时间获取对应的价格信息
     *
     * @param mvHbTime 需要判断的时间
     * @param priceVo  价格信息对象
     * @return Pair<Boolean, ChargePriceItem> 如果没有匹配到则返回null; Boolean: true: 普通计费, false: 分时计费;
     * ChargePriceItem: 对应时间段的价格信息;
     */
    private Pair<Boolean, ChargePriceItem> getPriceByTime(ZonedDateTime mvHbTime,
        ChargePriceVo priceVo) {

        if (mvHbTime == null || priceVo == null || CollectionUtils.isEmpty(priceVo.getItemList())) {
            return null;
        }
        if (priceVo.getItemList().size() == 1) {
            return Pair.of(true, priceVo.getItemList().get(0));
        }

        // 转换到系统默认时区
//        ZonedDateTime localDateTime = mvHbTime.withZoneSameInstant(ZoneId.systemDefault());

        // 获取当前小时和分钟
        LocalTime currentTime = mvHbTime.toLocalTime();

        // 遍历价格列表，查找匹配的时间段
        for (ChargePriceItem price : priceVo.getItemList()) {

            // 判断当前时间是否在时间段内
            if (PriceUtil.isTimeInRange(currentTime, price.getStartTime(), price.getEndTime())) {
                return Pair.of(false, price);
            }
        }
        return null;
    }

    public void processHeartBeat(ChargeHbUp msg) {
        log.debug("[{} {}] 处理订单心跳开始. plugId = {}",
            msg.getBase().getTid(), msg.getBase().getEvseNo(), msg.getBase().getPlugIdx());
        String evseNo = msg.getBase().getEvseNo();

        GwEvseVo evseCache = evseRepository.getEvse(evseNo); //先获取注册时本地缓存的桩

        int plugIdx = msg.getBase().getPlugIdx();

        Optional<GwPlugVo> plugOpt = cacheManagerService.getPlugCache(evseNo, plugIdx);
        if (plugOpt.isEmpty()) {
            log.error("[{} {}] 获取枪头缓存失败. plugIdx = {}",
                msg.getBase().getTid(), evseNo, plugIdx);
            return;
        }
        GwPlugVo plug = plugOpt.get().copy();
        log.debug("[{} {}] plug.status = {}", msg.getBase().getTid(), evseNo, plug.getStatus());
        if (plug.getStatus() == null || PlugStatus.UNKNOWN.equals(plug.getStatus())
            || PlugStatus.OFFLINE.equals(plug.getStatus())) {
            plug.setStatus(PlugStatus.BUSY); //解决3.7协议充电中DTU闪断，导致桩状态一直处于离线
        }

        EvseContext ctx = new EvseContext(msg.getBase().getTid(), evseNo, plugIdx, msg, evseCache);

        // 添加到桩编号 Channel Key 映射管理

        if (evseCache == null //|| plugHeartbeat == null
        ) {
            log.info("[{} {}] 桩/枪没有注册。", msg.getBase().getTid(), evseNo);
            return;
        }

//        ChargeHbReq evseReq;
//        evseReq = ChargeHbDecoder.parseX(msg.getTraceId(), evseCache, msg.getFullMsg());
//
//        evseReq.setBase(msg.getBaseMsg());

        log.trace("[{} {}] chargeHbReq = {}", msg.getBase().getTid(), evseNo, msg);
        this.processOrder(ctx, msg, plug);

        if (StringUtils.isEmpty(idempotenceService.get(GW_EVSE_HB + evseNo))) {
            log.debug("[{} {}] 设置桩全局心跳缓存。", msg.getBase().getTid(),
                msg.getBase().getEvseNo());
            idempotenceService.put(GW_EVSE_HB + evseNo, evseNo);
        }

        boolean isChanged = evseRepository.updateGun(ctx, plug);// 更新状态,并且返回更新标识

        // 更新 evseCache 其他属性值
        if (isChanged) {
            this.reportEvseStatus(ctx.getTraceId(), ctx.getEvse(), ctx.getPlugIdx());
            idempotenceService.remove(GW_EVSE_HB + evseNo);
            return;
        } else {
            log.debug("[{} {}] 未更新桩枪状态", msg.getBase().getTid(), msg.getBase().getEvseNo());
        }


    }


    private void processOrder(EvseContext ctx, ChargeHbUp evseReq, GwPlugVo plug) {
        // TODO: 2025/4/21 WZFIX 待删除日志
        log.info("[{} {}] 处理订单心跳. evseReq = {}", ctx.getTraceId(), ctx.getEvseNo(), evseReq);
        try {
            String tid = ctx.getTraceId();
            // 获取缓存中的订单
            OrderData order = evseOrderRepository.getOrder(evseReq.getOrderNo());
            log.trace("[{} {}] 获取缓存中的订单信息: orderNo = {}, OrderData: {}", tid,
                ctx.getEvseNo(), evseReq.getOrderNo(), JsonUtils.toJsonString(order));
            if (null == order) {
                log.error("[{} {}] 缓存中都没有该订单信息: orderNo = {}", tid, ctx.getEvseNo(),
                    evseReq.getOrderNo());
                return;
            }

            if (order.getBillingDetails() == null) {
                order.setBillingDetails(new LinkedHashMap<>());
            }
            if (order.getDetails() == null) {
                order.setDetails(new ArrayList<>());
            }

            Pair<BigDecimal, BigDecimal> pair = this.calcOrderFee(tid,
                ctx.getEvseNo(), evseReq.getMvHbTime(), order);
            log.trace("[{}] 计算订单费用: orderNo = {}, elecFee = {}, servFee = {}",
                tid, order.getOrderNo(), pair.getLeft(), pair.getRight());
            if (evseReq.getElecFee() == null) { // RCD桩充电中会上传电费
                evseReq.setElecFee(pair.getLeft());
            }
            if (evseReq.getServFee() == null) { // RCD桩充电中会上传服务费
                evseReq.setServFee(pair.getRight());
            }
            OrderTimeIntervalDetail detail = getOrderDetail(ctx, evseReq);
            //if(detail != null) {
            order.getDetails().add(detail);
            //}

            log.info(
                "[{} {}] plugIdx = {}, orderNo = {}, orderStatus = {}, isUploadHb = {}, details.size = {}",
                ctx.getTraceId(), order.getEvseNo(), order.getIdx(), order.getOrderNo(),
                order.getOrderStatus(), order.isUploadHb(), order.getDetails().size());
            //开启订单后第一次心跳直接上报，其他的满10次上报
            if (order.isUploadHb()) {
//                this.powerCtrl(ctx, evseReq);   // 功率动态分配
                order.setUploadHb(false);
                this.send2Cloud(ctx.getTraceId(), ctx.getEvseNo(), ctx.getPlugIdx(), order);
            } else if (order.getDetails().size() >= mergeNum) {
//                this.powerCtrl(ctx, evseReq);   // 功率动态分配
                this.send2Cloud(ctx.getTraceId(), ctx.getEvseNo(), ctx.getPlugIdx(), order);
            }

            //更新到缓存
            evseOrderRepository.hbUpdateOrder(ctx.getTraceId(), order, evseReq.getBase().getSeq());

            // 充电完成状态不需要下发
            if (plug.getStatus() == PlugStatus.BUSY) {
                this.checkOverCharge(ctx.getTraceId(), order, detail, ctx.getEvseNo(),
                    ctx.getPlugIdx());
            }
        } catch (Exception ex) {
            log.error("[{} {}] 心跳中处理订单数据失败。", ctx.getTraceId(), ctx.getEvseNo(), ex);
        }
    }


    private void checkOverCharge(String traceId, OrderData order, OrderTimeIntervalDetail detail,
        String evseNo, int plugIdx) {
        if (IotGwConstants.PAY_ACCOUNT_TYPE_PREPAY.equalsIgnoreCase(order.getAccountType())) {
            // 即充即退的订单要防止桩端超充
            BigDecimal fee = detail.getElecFee().add(detail.getServFee());
            if (DecimalUtils.gt(fee, order.getAmount())) {
                // 充超要下发停止指令
                log.warn(
                    "[{} {}] 桩端充电金额超过订单冻结金额!!! orderNo = {}, frozenAmount = {}, order.fee = {}",
                    traceId, evseNo, order.getOrderNo(), order.getAmount(), fee);

                EvseMsgBase base = new EvseMsgBase();
                base.setEvseNo(evseNo)
                    .setPlugIdx(plugIdx)
                    .setCmdCode(EvseMessageType.CHARGE_FROM_CLOUD)
//                    .setSeq(mqMsg.getSeqNoToEvse())
                ;

                ChargeStopDown dMsg = new ChargeStopDown(base);
                dMsg.setOrderNo(order.getOrderNo())
                ;
                this.orderBizService.stopOrderFromCloud(dMsg);
            }
        }
    }

    /*
    // 功率动态分配
    private void powerCtrl(EvseContext ctx, ChargeHbUp evseReq) {
        Integer power = this.sitePowerBizService.hb(ctx.getTraceId(), ctx.getEvseNo(),
            ctx.getPlugIdx(), evseReq.getRequestPower().intValue());
        if (power != null && power.intValue() > 0) {
            //
            ChargePowerCtrlDown dMsg = new ChargePowerCtrlDown(evseReq.getBase());
            dMsg.setOrderNo(evseReq.getOrderNo());
            dMsg.setPowerCtrl(power);
            chargePowerCtrlCommander.sendReply(ctx.getTraceId(), dMsg);
        }
    }
    */

    /**
     * 计算订单费用
     * @param tid
     * @param evseNo
     * @param mvHbTime
     * @param order
     * @return
     */
    private Pair<BigDecimal, BigDecimal> calcOrderFee(String tid, String evseNo,
        ZonedDateTime mvHbTime, OrderData order) {
        log.trace("[{} {}] mvHbTime: {}, order: {}", tid, evseNo, mvHbTime,
            JsonUtils.toJsonString(order));

        if (order.getStartMeter() == null || order.getCurrMeter() == null || DecimalUtils.eq(
            order.getStartMeter(), order.getCurrMeter())) {
            log.info("[{} {}] 电量未变化，不处理", tid, order.getEvseNo());
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        Pair<Boolean, ChargePriceItem> pricePair = this.getPriceByTime(mvHbTime, order.getPrice());
        log.debug("[{} {}] 获取价格信息 pricePair.left = {}, pricePair.right = {}", tid, evseNo,
            pricePair != null ? pricePair.getLeft() : null,
            pricePair != null ? pricePair.getRight() : null);

        OrderDetail latestOrderDetail = order.getLatestOrderDetail();

        if (pricePair != null && pricePair.getRight() != null) {
            Boolean standardBilling = pricePair.getLeft(); // true: 普通计费, false: 分时计费;
            ChargePriceItem priceItem = pricePair.getRight();

            if (latestOrderDetail == null) {
                log.info("[{} {}] 时段账单数据不存在，初始化第一段OrderDetail. orderNo = {}", tid,
                    evseNo, order.getOrderNo());

                this.initOrCompleteOrderDetail(tid, mvHbTime, order);

                /*
                BigDecimal startMeter =
                    order.getBillingDetails() == null || order.getBillingDetails().isEmpty()
                        ? order.getStartMeter() : order.getCurrMeter();
                long startTime;
                long realStartTime = order.getStartTime();
                if (NumberUtils.gtZero(realStartTime)) {
                    if (standardBilling) {
                        startTime = realStartTime; // 普通计费则不考虑时段数据
                    } else {
                        ZonedDateTime startDateTime = ZonedDateTime.ofInstant(
                            Instant.ofEpochSecond(realStartTime), mvHbTime.getZone());
                        // 判断实际充电开始时间 和 mvHbTime上报时间 是否在同一个时段内；
                        // 是：正常上报，实际充电开始时间作为第一段的开始时间；
                        // 否：异常上报，已缺失了之前的MeterValues数据，mvHbTime上报时间作为第一段的开始时间，让订单变成异常订单；
                        boolean timeInRange = PriceUtil.isTimeInRange(startDateTime.toLocalTime(),
                            priceItem.getStartTime(), priceItem.getEndTime());
                        if (timeInRange) {
                            startTime = realStartTime;
                        } else {
                            startTime = mvHbTime.toEpochSecond();
                            startMeter = order.getCurrMeter();
                        }
                    }
                } else {
                    startTime = mvHbTime.toEpochSecond();
                }

                OrderDetail newOrderDetail = new OrderDetail();
                newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                    .setStopTimeStr(priceItem.getEndTime())
                    .setStartTime(startTime)
                    .setElecPrice(priceItem.getElecPrice())
                    .setServPrice(priceItem.getServPrice())
                    .setStartMeter(startMeter)
                    .setKwh(BigDecimal.ZERO);
                Optional.ofNullable(priceItem.getCategory())
                    .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                order.setLastOrderDetail(newOrderDetail);
                */

                return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
            }

            if (mvHbTime.toEpochSecond() <= latestOrderDetail.getStartTime()) {
                log.debug(
                    "[{} {}] mvHbTime时间小于当前时段的开始时间，跳过处理. orderNo: {}, mvHbTime: {}, latestOrderDetail.startTime: {}",
                    tid, evseNo, order.getOrderNo(), mvHbTime.toEpochSecond(),
                    latestOrderDetail.getStartTime());
                // nothing to do

            } else if (
                !latestOrderDetail.getStartTimeStr().equalsIgnoreCase(priceItem.getStartTime())
                    && !latestOrderDetail.getStopTimeStr()
                    .equalsIgnoreCase(priceItem.getEndTime())) {

                if (latestOrderDetail.getStopTimeStr().equalsIgnoreCase(priceItem.getStartTime())) {
                    log.info("[{} {}] 切换到下一时段. orderNo: {}", tid, evseNo,
                        order.getOrderNo());
                    BigDecimal currKwh = order.getCurrMeter()
                        .subtract(latestOrderDetail.getStartMeter());
                    BigDecimal elecFee = latestOrderDetail.getElecPrice().multiply(currKwh)
                        .setScale(4, RoundingMode.HALF_UP);
                    BigDecimal servFee = latestOrderDetail.getServPrice().multiply(currKwh)
                        .setScale(4, RoundingMode.HALF_UP);
                    latestOrderDetail.setKwh(currKwh)
                        .setElecFee(elecFee)
                        .setServFee(servFee);

                    Pair<Integer, Integer> pair = PriceUtil.splitTimeStr(priceItem.getStartTime());
                    ZonedDateTime currStopTime = mvHbTime.with(LocalTime.MIN)
                        .withHour(pair.getLeft()).withMinute(pair.getRight());
                    // 记录上一时段的结束时间
                    latestOrderDetail.setStopTime(currStopTime.toEpochSecond());
                    order.pushBillingDetail(latestOrderDetail);

                    OrderDetail newOrderDetail = new OrderDetail();
                    newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                        .setStopTimeStr(priceItem.getEndTime())
                        .setStartTime(latestOrderDetail.getStopTime())
                        .setElecPrice(priceItem.getElecPrice())
                        .setServPrice(priceItem.getServPrice())
                        .setStartMeter(order.getCurrMeter())
                        .setKwh(BigDecimal.ZERO);
                    Optional.ofNullable(priceItem.getCategory())
                        .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                    order.setLatestOrderDetail(newOrderDetail);
                    evseOrderRepository.updateOrder(order);
                } else {
                    log.info(
                        "[{} {}] 缓存中订单最新时段和mvHbTime不是相邻时段. orderNo: {}, latestOrderDetail.startTimeStr: {}, latestOrderDetail.stopTimeStr: {}, priceItem.startTime: {}, priceItem.endTime: {}",
                        tid, evseNo, order.getOrderNo(), latestOrderDetail.getStartTimeStr(),
                        latestOrderDetail.getStopTimeStr(), priceItem.getStartTime(),
                        priceItem.getEndTime());

                    this.completeTimesharingOrderDetail(tid, mvHbTime, priceItem, order);
                }

            } else {
                // 仍在当前时段
                if (order.getCurrMeter() != null && latestOrderDetail.getStartMeter() != null) {
                    BigDecimal currKwh = order.getCurrMeter()
                        .subtract(latestOrderDetail.getStartMeter());

                    latestOrderDetail.setKwh(currKwh)
                        .setElecFee(priceItem.getElecPrice().multiply(currKwh)
                            .setScale(4, RoundingMode.HALF_UP))
                        .setServFee(priceItem.getServPrice().multiply(currKwh)
                            .setScale(4, RoundingMode.HALF_UP));
                    order.setLatestOrderDetail(latestOrderDetail);
                }
                evseOrderRepository.updateOrder(order);
            }

        } else {
            log.error("[{} {}] 获取价格信息失败 orderNo: {}", tid, evseNo, order.getOrderNo());
        }

        if (latestOrderDetail == null) {
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        AtomicReference<BigDecimal> elecFeeAto = new AtomicReference<>(
            latestOrderDetail.getElecFee());
        AtomicReference<BigDecimal> servFeeAto = new AtomicReference<>(
            latestOrderDetail.getServFee());

        order.getBillingDetails().forEach((k, v) -> {
            elecFeeAto.set(DecimalUtils.add(elecFeeAto.get(), v.getElecFee()));
            servFeeAto.set(DecimalUtils.add(servFeeAto.get(), v.getServFee()));
        });
        return Pair.of(elecFeeAto.get(), servFeeAto.get());
    }

    /**
     * 当时段为空时，新增一个或n个时段
     * <p>适用场景：正常充电</p>
     * <p>适用场景：离线开启，一段时间后桩上线</p>
     *
     * @param tid
     * @param mvHbTime
     * @param order
     */
    private void initOrCompleteOrderDetail(String tid, ZonedDateTime mvHbTime, OrderData order) {
        ChargePriceVo priceVo = order.getPrice();
        if (priceVo == null || CollectionUtils.isEmpty(priceVo.getItemList())) {
            log.error("[{} {}] 缺少计费模板信息 price = {}", tid, order.getEvseNo(), priceVo);
            return;
        }
        if (order.getCurrMeter() == null || order.getStartMeter() == null
            || DecimalUtils.eq(order.getCurrMeter(), order.getStartMeter())) {
            log.info("[{} {}] 电量未变化，不处理", tid, order.getEvseNo());
            return;
        }
        ZoneId zone = mvHbTime.getZone();
        BigDecimal actualStartMeter = order.getStartMeter();

        // 分别通过order.startTime和mvHbTime获取对应的ChargePriceItem
        long actualStartTime = order.getStartTime();
        ZonedDateTime actualStartDateTime = ZonedDateTime.ofInstant(
            Instant.ofEpochSecond(actualStartTime), zone);
        Pair<Boolean, ChargePriceItem> startPair = this.getPriceByTime(actualStartDateTime,
            priceVo);
        Pair<Boolean, ChargePriceItem> mvPair = this.getPriceByTime(mvHbTime, priceVo);
        if (startPair == null || startPair.getRight() == null || mvPair == null
            || mvPair.getRight() == null) {
            log.error(
                "[{} {}] 通过时间获取对应的价格信息失败 actualStartDateTime = {}, mvHbTime = {}, priceVo = {}",
                tid, order.getEvseNo(), actualStartDateTime, mvHbTime,
                JsonUtils.toJsonString(priceVo));
            return;
        }
        log.debug(
            "[{} {}] startPair.left = {}, startPair.right = {}, mvPair.left = {}, mvPair.right = {}",
            tid, order.getEvseNo(), startPair.getLeft(),
            JsonUtils.toJsonString(startPair.getRight()), mvPair.getLeft(),
            JsonUtils.toJsonString(mvPair.getRight()));

        Boolean standardBilling = mvPair.getLeft();
        ChargePriceItem mvPriceItem = mvPair.getRight();
        if (standardBilling) {
            // 普通计费
            OrderDetail newOrderDetail = new OrderDetail();
            newOrderDetail.setStartTimeStr(mvPriceItem.getStartTime())
                .setStopTimeStr(mvPriceItem.getEndTime())
                .setStartTime(actualStartTime)
                .setElecPrice(mvPriceItem.getElecPrice())
                .setServPrice(mvPriceItem.getServPrice())
                .setStartMeter(actualStartMeter);

            BigDecimal subtractKwh = order.getCurrMeter().subtract(actualStartMeter);
            newOrderDetail.setKwh(subtractKwh)
                .setElecFee(mvPriceItem.getElecPrice().multiply(subtractKwh)
                    .setScale(4, RoundingMode.HALF_UP))
                .setServFee(mvPriceItem.getServPrice().multiply(subtractKwh)
                    .setScale(4, RoundingMode.HALF_UP));

            Optional.ofNullable(mvPriceItem.getCategory())
                .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
            order.setLatestOrderDetail(newOrderDetail);

        } else {
            // 分时计费
            ChargePriceItem startPriceItem = startPair.getRight();
            boolean isSameTime = startPriceItem.getStartTime().equals(mvPriceItem.getStartTime())
                && startPriceItem.getEndTime().equals(mvPriceItem.getEndTime());
            log.debug("[{} {}] isSameTime = {}", tid, order.getEvseNo(), isSameTime);
            // 判断startDateTime和mvHbTime是否在同一个时段
            if (isSameTime) {
                // 在同一个时段内，正常初始化一个detail
                OrderDetail newOrderDetail = new OrderDetail();
                newOrderDetail.setStartTimeStr(startPriceItem.getStartTime())
                    .setStopTimeStr(startPriceItem.getEndTime())
                    .setStartTime(actualStartTime)
                    .setElecPrice(startPriceItem.getElecPrice())
                    .setServPrice(startPriceItem.getServPrice())
                    .setStartMeter(actualStartMeter);

                BigDecimal subtractKwh = order.getCurrMeter().subtract(actualStartMeter);
                newOrderDetail.setKwh(subtractKwh)
                    .setElecFee(startPriceItem.getElecPrice().multiply(subtractKwh)
                        .setScale(4, RoundingMode.HALF_UP))
                    .setServFee(startPriceItem.getServPrice().multiply(subtractKwh)
                        .setScale(4, RoundingMode.HALF_UP));

                Optional.ofNullable(startPriceItem.getCategory())
                    .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                order.setLatestOrderDetail(newOrderDetail);
            } else {
                // 不在同一个时段内，按秒平均分电量，计费每个时段所充的电量和费用
                BigDecimal subtractKwh = order.getCurrMeter().subtract(actualStartMeter);
                long subtractTime = mvHbTime.toEpochSecond() - order.getStartTime();
                BigDecimal kwhPerSecond = subtractKwh.divide(BigDecimal.valueOf(subtractTime), 6,
                    RoundingMode.HALF_UP);
                log.debug("[{} {}] subtractKwh = {}, subtractTime = {}, kwhPerSecond = {}", tid,
                    order.getEvseNo(), subtractKwh, subtractTime, kwhPerSecond);

                List<ChargePriceItem> sortedPriceItems = priceVo.getItemList().stream()
                    .sorted((p1, p2) -> {
                        Optional<LocalTime> t1 = PriceUtil.parseTimeString(p1.getStartTime());
                        Optional<LocalTime> t2 = PriceUtil.parseTimeString(p2.getStartTime());
                        if (t1.isEmpty() || t2.isEmpty()) {
                            return 0;
                        }
                        return t1.get().compareTo(t2.get());
                    }).toList();
                
                List<OrderDetail> needPushOrderDetails = new ArrayList<>();
                ZonedDateTime loopStartTime = actualStartDateTime;
                BigDecimal loopStartMeter = actualStartMeter;
                boolean isLastPriceItem = false;
                // 循环priceItem，从order.startTime所在时段开始创建OrderDetail，到mvHbTime所在时段结束
                for (ChargePriceItem priceItem : sortedPriceItems) {
                    log.debug("[{} {}] loopStartTime = {}, loopStartMeter = {}, priceItem = {}",
                        tid, order.getEvseNo(), loopStartTime, loopStartMeter,
                        JsonUtils.toJsonString(priceItem));
                    if (!PriceUtil.isTimeInRange(loopStartTime.toLocalTime(),
                        priceItem.getStartTime(), priceItem.getEndTime())) {
                        continue;
                    }
                    if (isLastPriceItem) {
                        break;
                    }

                    isLastPriceItem = mvPriceItem.getStartTime().equals(priceItem.getStartTime())
                        && mvPriceItem.getEndTime().equals(priceItem.getEndTime());
                    log.debug("[{} {}] isLastPriceItem = {}", tid, order.getEvseNo(),
                        isLastPriceItem);

                    OrderDetail newOrderDetail = new OrderDetail();
                    newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                        .setStopTimeStr(priceItem.getEndTime())
                        .setElecPrice(priceItem.getElecPrice())
                        .setServPrice(priceItem.getServPrice())
                        .setStartMeter(loopStartMeter)
                        .setStartTime(loopStartTime.toEpochSecond());

                    long stopTime = 0;
                    if (isLastPriceItem) {
                        stopTime = mvHbTime.toEpochSecond();
                    } else {
                        Optional<LocalTime> stopTimeOpt = PriceUtil.parseTimeString(
                            priceItem.getEndTime());
                        if (stopTimeOpt.isPresent()) {
                            LocalTime temp = stopTimeOpt.get();
                            stopTime = loopStartTime.with(LocalTime.MIN).withHour(temp.getHour())
                                .withMinute(temp.getMinute()).toEpochSecond();
                        }
                        newOrderDetail.setStopTime(stopTime);
                    }
                    log.debug("[{} {}] stopTime = {}, newOrderDetail.getStartTime() = {}", tid,
                        order.getEvseNo(), stopTime, newOrderDetail.getStartTime());
                    if (stopTime > newOrderDetail.getStartTime()) {
                        long durationSecond = stopTime - newOrderDetail.getStartTime();
                        BigDecimal kwh = kwhPerSecond.multiply(BigDecimal.valueOf(durationSecond));

                        newOrderDetail.setKwh(kwh)
                            .setElecFee(priceItem.getElecPrice().multiply(kwh)
                                .setScale(4, RoundingMode.HALF_UP))
                            .setServFee(priceItem.getServPrice().multiply(kwh)
                                .setScale(4, RoundingMode.HALF_UP));
                    }
                    log.debug("[{} {}] newOrderDetail = {}", tid, order.getEvseNo(),
                        JsonUtils.toJsonString(newOrderDetail));

                    Optional.ofNullable(priceItem.getCategory())
                        .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                    if (isLastPriceItem) {
                        order.setLatestOrderDetail(newOrderDetail);
                    } else {
                        needPushOrderDetails.add(newOrderDetail);
                    }

                    loopStartMeter = loopStartMeter.add(newOrderDetail.getKwh());
                    loopStartTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(stopTime), zone);
                }

                needPushOrderDetails.forEach(order::pushBillingDetail);
            }
        }
        evseOrderRepository.updateOrder(order);
    }

    /**
     * 补全分时订单时段数据
     * <p>适用场景：在线开启后，某个时刻离线，一段时间后桩上线（跨了n个时段）</p>
     *
     * @param tid
     * @param mvHbTime
     * @param mvPriceItem
     * @param order
     */
    private void completeTimesharingOrderDetail(String tid, ZonedDateTime mvHbTime,
        ChargePriceItem mvPriceItem, OrderData order) {
        OrderDetail latestOrderDetail = order.getLatestOrderDetail();
        log.debug("[{} {}] latestOrderDetail = {}", tid, order.getEvseNo(),
            JsonUtils.toJsonString(latestOrderDetail));
        if (latestOrderDetail == null) {
            log.error("[{} {}] 缺少账单详情 latestOrderDetail = {}", tid, order.getEvseNo(),
                latestOrderDetail);
            return;
        }

        // latestOrderDetail 和 mvHbTime 不在相邻时段内，按秒平均分电量，计费每个时段所充的电量和费用
        BigDecimal subtractKwh = order.getCurrMeter().subtract(
            Optional.ofNullable(latestOrderDetail.getStartMeter()).orElse(BigDecimal.ZERO));
        long subtractTime = mvHbTime.toEpochSecond() - latestOrderDetail.getStartTime();
        BigDecimal kwhPerSecond = subtractKwh.divide(BigDecimal.valueOf(subtractTime), 6,
            RoundingMode.HALF_UP);
        log.debug("[{} {}] subtractKwh = {}, subtractTime = {}, kwhPerSecond = {}", tid,
            order.getEvseNo(), subtractKwh, subtractTime, kwhPerSecond);

        ZoneId zone = mvHbTime.getZone();
        ZonedDateTime lastStartDateTime = ZonedDateTime.ofInstant(
            Instant.ofEpochSecond(latestOrderDetail.getStartTime()), zone);
        BigDecimal lastStartMeter = Optional.ofNullable(latestOrderDetail.getStartMeter())
            .orElse(BigDecimal.ZERO);

        List<ChargePriceItem> sortedPriceItems = order.getPrice().getItemList().stream()
            .sorted((p1, p2) -> {
                Optional<LocalTime> t1 = PriceUtil.parseTimeString(p1.getStartTime());
                Optional<LocalTime> t2 = PriceUtil.parseTimeString(p2.getStartTime());
                if (t1.isEmpty() || t2.isEmpty()) {
                    return 0;
                }
                return t1.get().compareTo(t2.get());
            }).toList();

        List<OrderDetail> needPushOrderDetails = new ArrayList<>();
        ZonedDateTime loopStartTime = lastStartDateTime;
        BigDecimal loopStartMeter = lastStartMeter;
        boolean isLastPriceItem = false;
        // 循环priceItem，从lastOrderDetail.startTime所在时段开始补全OrderDetail，到mvHbTime所在时段结束
        for (ChargePriceItem priceItem : sortedPriceItems) {
            log.debug("[{} {}] loopStartTime = {}, loopStartMeter = {}, priceItem = {}", tid,
                order.getEvseNo(), loopStartTime, loopStartMeter,
                JsonUtils.toJsonString(priceItem));
            if (!PriceUtil.isTimeInRange(loopStartTime.toLocalTime(), priceItem.getStartTime(),
                priceItem.getEndTime())) {
                continue;
            }
            if (isLastPriceItem) {
                break;
            }

            isLastPriceItem = mvPriceItem.getStartTime().equals(priceItem.getStartTime())
                && mvPriceItem.getEndTime().equals(priceItem.getEndTime());
            log.debug("[{} {}] isLastPriceItem = {}", tid, order.getEvseNo(),
                isLastPriceItem);

            OrderDetail newOrderDetail = new OrderDetail();
            newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                .setStopTimeStr(priceItem.getEndTime())
                .setElecPrice(priceItem.getElecPrice())
                .setServPrice(priceItem.getServPrice())
                .setStartMeter(loopStartMeter)
                .setStartTime(loopStartTime.toEpochSecond());

            long stopTime = 0;
            if (isLastPriceItem) {
                stopTime = mvHbTime.toEpochSecond();
            } else {
                Optional<LocalTime> stopTimeOpt = PriceUtil.parseTimeString(
                    priceItem.getEndTime());
                if (stopTimeOpt.isPresent()) {
                    LocalTime temp = stopTimeOpt.get();
                    stopTime = loopStartTime.with(LocalTime.MIN).withHour(temp.getHour())
                        .withMinute(temp.getMinute()).toEpochSecond();
                }
                newOrderDetail.setStopTime(stopTime);
            }
            log.debug("[{} {}] stopTime = {}, newOrderDetail.getStartTime() = {}", tid,
                order.getEvseNo(), stopTime, newOrderDetail.getStartTime());
            if (stopTime > newOrderDetail.getStartTime()) {
                long durationSecond = stopTime - newOrderDetail.getStartTime();
                BigDecimal kwh = kwhPerSecond.multiply(BigDecimal.valueOf(durationSecond));

                newOrderDetail.setKwh(kwh)
                    .setElecFee(priceItem.getElecPrice().multiply(kwh)
                        .setScale(4, RoundingMode.HALF_UP))
                    .setServFee(priceItem.getServPrice().multiply(kwh)
                        .setScale(4, RoundingMode.HALF_UP));
            }
            log.debug("[{} {}] newOrderDetail = {}", tid, order.getEvseNo(),
                JsonUtils.toJsonString(newOrderDetail));

            Optional.ofNullable(priceItem.getCategory())
                .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
            if (isLastPriceItem) {
                order.setLatestOrderDetail(newOrderDetail);
            } else {
                needPushOrderDetails.add(newOrderDetail);
            }

            loopStartMeter = loopStartMeter.add(newOrderDetail.getKwh());
            loopStartTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(stopTime), zone);
        }

        needPushOrderDetails.forEach(order::pushBillingDetail);
        evseOrderRepository.updateOrder(order);
    }

    private OrderTimeIntervalDetail getOrderDetail(
        EvseContext ctx, ChargeHbUp evseReq) {

        GwEvseVo evse = ctx.getEvse();

        OrderTimeIntervalDetail detail = new OrderTimeIntervalDetail();
        //long timestamp = getTime(evseReq, order);
        detail.setTimestamp(evseReq.getMvHbTime().toEpochSecond()); //时间戳
        detail.setDuration(evseReq.getDuration());
        detail.setRemainingTime(evseReq.getRemainTime());
        detail.setKwh(evseReq.getKwh());
        detail.setElecFee(evseReq.getElecFee());
        detail.setServFee(evseReq.getServFee());
        //detail.setBalance();
        if (evseReq.getBattery() != null) {
            detail.setBatteryTemp(evseReq.getBattery().getTemp());
        }
        detail.setEvseTemp(evseReq.getEvseTemp());
        detail.setPlugTemp(evseReq.getPlugTemp());
        //detail.setMaxVoltage(DecimalUtils.divide100(evseReq.getMaxVoltage()));
        //detail.setMinVoltage(DecimalUtils.divide100(evseReq.getMinVoltage()));
        detail.setBms(evseReq.getBms());
        detail.setBattery(evseReq.getBattery());

        if (evseReq.getSoc() != null) {
            detail.setSoc(evseReq.getSoc() == 0xFF ? null : evseReq.getSoc());
        }
        detail.setPower(evseReq.getPower());

        //交流
        if (evse.getSupplyType() == SupplyType.AC) {
            //电压
            detail.setAcVoltageA(evseReq.getVoltageA());
            detail.setAcVoltageB(evseReq.getVoltageB());
            detail.setAcVoltageC(evseReq.getVoltageC());

            //电流
            detail.setAcCurrentA(evseReq.getCurrentA());
            detail.setAcCurrentB(evseReq.getCurrentB());
            detail.setAcCurrentC(evseReq.getCurrentC());
        }

        //直流
        if (evse.getSupplyType() == SupplyType.DC) {
            detail.setDcVoltageO(evseReq.getOutputVoltage());
            detail.setDcCurrentO(evseReq.getOutputCurrent());
            detail.setDcVoltageA(evseReq.getVoltageA());
            detail.setDcVoltageB(evseReq.getVoltageB());
            detail.setDcVoltageC(evseReq.getVoltageC());
        }

        log.debug("[{} {}] 订单分时信息。 detail: {}", ctx.getTraceId(), ctx.getEvseNo(), detail);

        return detail;
    }

    private void send2Cloud(String traceId, String evseNo, int plugNo, OrderData order) {

        //上报update 分时段详情
        OrderUpdateData updateReq = orderService.transOrderUpdateRequest(evseNo, plugNo, order);
        orderService.updateOrder(traceId, updateReq)
            .subscribe(a -> order.getDetails().clear());//上报后清掉分时段详情


    }

    // 指定枪头编号进上传该枪状态，其他不上传
    private void reportEvseStatus(String traceId, GwEvseVo evse, Integer plugIdx) {

        if (evse != null) {
//            // 调用方都转换了，这里可以注释
            evseStatusReportService.reportStatus(traceId, evse, plugIdx);
        } else {
            log.warn("[{}] evse is null!!! ", traceId);
        }
    }


    private boolean isValid(String orderNo) {
        boolean exist = false;
        if (orderNo != null && !StringUtils.isEmpty(orderNo)) {
            exist = !StringUtils.isEmpty(orderNo.replace("0", ""));
        }
        return exist;
    }
}
