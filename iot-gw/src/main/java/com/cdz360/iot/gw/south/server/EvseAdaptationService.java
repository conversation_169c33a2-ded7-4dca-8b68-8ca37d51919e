package com.cdz360.iot.gw.south.server;

import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.EvseSocketCache;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.connection.EvseConnection;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.remotetrigger.TriggerMessageRequestType;
import java.time.Duration;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EvseAdaptationService {

//    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseSocketCache evseSocketCache;

    @Autowired
    private JsonServerImpl jsonServerImpl;

    public void triggerStatusNotification(EvseMsgBase base, UUID sessionIndex) {
        String evseNo = base.getEvseNo();
        Optional<GwEvseVo> evseOfOtherCache = evseRepository.getEvseOfOtherCache(evseNo);
        if (evseOfOtherCache.isEmpty()) {
            return;
        }

        Date now = new Date();
        Mono.delay(Duration.ofSeconds(10)).subscribe(x -> {
            EvseConnection evseConnection = evseSocketCache.get(evseNo);
            log.info("[{} {}] 触发桩注册或枪头状态上报. evseConnection: {}", base.getTid(), evseNo,
                evseConnection);
            // 如果evseConnection为空或最近一次的registerTime在10s之前，则触发桩注册
            if (evseConnection == null || evseConnection.getRegisterTime() == null
                || evseConnection.getRegisterTime().before(now)) {
                // 触发桩注册
                jsonServerImpl.sendTriggerMessageReq(base, sessionIndex,
                    TriggerMessageRequestType.BootNotification);
            } else {
                // 触发枪头状态上报
                GwEvseVo redisEvseVo = evseOfOtherCache.get();
                Integer plugNum = redisEvseVo.getPlugNum();
                // 桩上所有枪都触发
                IntStream.range(1, plugNum + 1).forEach(idx -> {
                    base.setPlugIdx(idx);
                    jsonServerImpl.sendTriggerMessageReq(base, sessionIndex,
                        TriggerMessageRequestType.StatusNotification);
                });
            }
        });
    }

}
