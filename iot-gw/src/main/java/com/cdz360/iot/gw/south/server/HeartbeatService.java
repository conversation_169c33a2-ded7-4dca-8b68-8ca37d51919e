package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.EvseStatusReportService;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.IdempotenceService;
import com.cdz360.iot.gw.biz.ds.PlugCache;
import com.cdz360.iot.gw.model.EvseContext;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.GwPlugVo;
import com.cdz360.iot.gw.model.evse.protocol.EvseHbMsgUp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class HeartbeatService {

    private final static Integer DETAIL_MAX_SIZE = 20;

    private final static String GW_EVSE_HB = "HB:EVSE";
    private static final List<PlugStatus> PLUG_BUSY_STATUS_LIST = List.of(PlugStatus.BUSY,
        PlugStatus.JOIN, PlugStatus.RECHARGE_END);
    //private final Logger logger = LoggerFactory.getLogger(HeartbeatService.class);
    @Autowired
    private PlugCache plugCache;

    @Autowired
    private IdempotenceService idempotenceService;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private EvseStatusReportService evseStatusReportService;

    @Value("${cdz360.timer.heartbeat-timeout:120000}")
    private int heartbeatTimeout;
    @Value("${cdz360.merge-num:4}")
    private int mergeNum;//合并发送数

    public void processHeartBeat(EvseHbMsgUp evseReq) {
        String tid = evseReq.getBase().getTid();
        String evseNo = evseReq.getBase().getEvseNo();
        int plugNo = evseReq.getBase().getPlugIdx();
        log.debug("[{} {}] 处理心跳开始. plugNo = {}", tid, evseNo, plugNo);

        GwEvseVo evseCache = evseRepository.getEvse(evseNo); //先获取注册时本地缓存的桩

        EvseContext ctx = new EvseContext(tid, evseNo, plugNo, evseReq,
            evseCache);

        // 添加到桩编号 Channel Key 映射管理

        //PlugConnection plugHeartbeat = plugCache.get(evseNo, plugNo);
        if (evseCache == null //|| plugHeartbeat == null
        ) {
            log.info("[{} {}] 桩/枪没有注册。", tid, evseNo);
            return;
        }

        // 转换数据
        GwPlugVo plug = GwPlugVo.toGwPlugVo(evseCache.getSupplyType(), evseReq);//桩端上报的桩/枪的状态

        if (StringUtils.isEmpty(idempotenceService.get(GW_EVSE_HB + evseNo))) {
            log.debug("[{} {}] 设置桩全局心跳缓存。", tid, evseNo);
            idempotenceService.put(GW_EVSE_HB + evseNo, evseNo);
        }

        if (NumberUtils.isZero(plug.getIdx())) {
            // idx=0 代表 充电桩主控制器
            if (!NumberUtils.isZero(plug.getErrorCode()) || StringUtils.isNotBlank(
                plug.getErrorMsg()) || !NumberUtils.isZero(plug.getAlertCode())) {
                log.info("[{} {}] 主控制器有故障/告警，作为1枪的上报数据来处理。", tid, evseNo);
                plug.setIdx(1);
            } else {
                // 否则跳过不处理
                log.info("[{} {}] 主控制器没有故障/告警，跳过不处理。", tid, evseNo);
                return;
            }
        }

        boolean isChanged = evseRepository.updateGun(ctx, plug);// 更新状态,并且返回更新标识

        // 更新 evseCache 其他属性值
        this.updateRegistedEvse(ctx, evseReq); // 这里的数据没有同步到本地缓存
        if (isChanged) {
            this.reportEvseStatus(ctx.getTraceId(), ctx.getEvse(), ctx.getPlugIdx());
            idempotenceService.remove(GW_EVSE_HB + evseNo);
            return;
        }

        if (ctx.getEvse().getBooting() && isGunsOnline(ctx.getEvse())) {
            log.info("[{} {}] 所有枪上线了，开始上报桩状态。 plugNo: {}", tid,
                evseNo, plugNo);

            this.reportEvseStatus(ctx.getTraceId(), ctx.getEvse(), null);
            idempotenceService.remove(GW_EVSE_HB + evseNo);
            this.evseRepository.evsePostBoot(evseNo);
        } else if (ctx.getEvse().getBooting()) {

            log.info("[{} {}] 还有枪未上线, 等待后再上报状态。 plugNo: {}, heartbeatTimeout: {}",
                tid, evseNo, plugNo, heartbeatTimeout);

            //timer超时时间内，上报正常状态；否则上报错误状态
            Timer timer = new Timer("Timer-Heartbeat-" + evseNo);
            timer.schedule(new TimerTask() {
                @Override
                public void run() {

                    log.debug("[{} {}] 超时上报桩状态。 plugNo: {}", tid,
                        evseNo, plugNo);

                    if (!StringUtils.isEmpty(idempotenceService.get(GW_EVSE_HB + evseNo))) {
                        log.info("[{} {}] 立即上报桩状态。 plugNo: {}", tid,
                            evseNo, plugNo);
                        //超时时间内没有收到心跳，使用当前缓存数据向云端上报桩/枪信息
//                        GwEvseVo evse = evseRepository.getEvse(evseNo);
//                        evse.setStatus(evseRepository.genEvseStatus(evse.getPlugs(), null));
                        // 这里是通过缓存确定桩的状态，不需要重新调整
                        reportEvseStatus(tid, evseRepository.getEvse(evseNo),
                            null);
                        evseRepository.evsePostBoot(evseNo);
                    }

                    idempotenceService.remove(GW_EVSE_HB + evseNo);

                    timer.cancel();
                }
            }, heartbeatTimeout);
        }
    }

    private boolean isGunsOnline(GwEvseVo evse) {
        boolean isAllOnline = false;

        if (CollectionUtils.isNotEmpty(evse.getPlugs())) {
            // 所有被认为在线的状态
            List<PlugStatus> statusList = new ArrayList<>(Arrays.asList(
                PlugStatus.IDLE, PlugStatus.CONNECT, PlugStatus.BUSY, PlugStatus.RECHARGE_END));

            long count = evse.getPlugs().parallelStream()
                .filter(plugVo -> statusList.contains(plugVo.getStatus()))
                .count();

            isAllOnline = count == evse.getPlugNum();
        }

        return isAllOnline;
    }


    // 指定枪头编号进上传该枪状态，其他不上传
    private void reportEvseStatus(String traceId, GwEvseVo evse, Integer plugIdx) {

        if (evse != null) {
//            // 调用方都转换了，这里可以注释
            evseStatusReportService.reportStatus(traceId, evse, plugIdx);
        } else {
            log.warn("[{}] evse is null!!! ", traceId);
        }
    }

    private void updateRegistedEvse(EvseContext ctx, EvseHbMsgUp evseReq) {
        // 桩信息
        ctx.getEvse().setEvseNo(evseReq.getBase().getEvseNo())
//                .setTemperature(evseReq.getEvseTemp())
        ;

        ctx.getEvse().setAlertCode(evseReq.getAlertCode());
        ctx.getEvse().setErrorCode(evseReq.getErrorCode());

        log.info("[{} {}] 更新已注册的桩信息。", ctx.getTraceId(), ctx.getEvseNo());
    }


    private boolean isValid(String orderNo) {
        boolean exist = false;
        if (orderNo != null && !StringUtils.isEmpty(orderNo)) {
            exist = !StringUtils.isEmpty(orderNo.replace("0", ""));
        }
        return exist;
    }
}
