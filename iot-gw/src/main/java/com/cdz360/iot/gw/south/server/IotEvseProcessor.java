package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.GetTransDataService;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.biz.ds.EvseSocketCache;
import com.cdz360.iot.gw.biz.ds.PlugCache;
import com.cdz360.iot.gw.biz.ds.RedisRwService;
import com.cdz360.iot.gw.core.biz.OrderNoGenerator;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.connection.EvseConnection;
import com.cdz360.iot.gw.model.connection.PlugConnection;
import com.cdz360.iot.gw.model.evse.protocol.Charge4EvseReq;
import com.cdz360.iot.gw.model.evse.protocol.ChargeAuthUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeHbUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStartedUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStoppedUp;
import com.cdz360.iot.gw.model.evse.protocol.EvseHbMsgUp;
import com.cdz360.iot.gw.model.evse.rcd.RcdErrorCode;
import com.cdz360.iot.gw.model.evse.rcd.StopChargeReuploadEvent;
import com.cdz360.iot.gw.model.evse.winline.WinlineErrorCode;
import com.cdz360.iot.gw.model.type.CalcType;
import com.cdz360.iot.gw.model.type.ChargeMode;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.model.type.EvseGunStatus;
import com.cdz360.iot.gw.model.type.OrderCompleteCode;
import com.cdz360.iot.gw.model.type.OrderStopMode;
import com.cdz360.iot.gw.model.type.RcdStopReason;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.south.handler.CommandFactory;
import com.cdz360.iot.gw.south.handler.UpstreamHandler2;
import com.cdz360.iot.gw.south.server.rcd.RcdOrderProcessor;
import com.cdz360.iot.gw.south.server.rcd.strategy.BillReqTransferStrategy;
import com.cdz360.iot.gw.south.server.rcd.strategy.DataTransferStrategyFactory;
import com.cdz360.iot.gw.south.server.rcd.strategy.IDataTransferStrategy;
import com.cdz360.iot.gw.util.ByteUtil;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.core.AuthorizationStatus;
import eu.chargetime.ocpp.model.core.AuthorizeRequest;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.core.HeartbeatConfirmation;
import eu.chargetime.ocpp.model.core.HeartbeatRequest;
import eu.chargetime.ocpp.model.core.IdTagInfo;
import eu.chargetime.ocpp.model.core.MeterValue;
import eu.chargetime.ocpp.model.core.MeterValuesConfirmation;
import eu.chargetime.ocpp.model.core.MeterValuesRequest;
import eu.chargetime.ocpp.model.core.Reason;
import eu.chargetime.ocpp.model.core.SampledValue;
import eu.chargetime.ocpp.model.core.StartTransactionConfirmation;
import eu.chargetime.ocpp.model.core.StartTransactionRequest;
import eu.chargetime.ocpp.model.core.StatusNotificationRequest;
import eu.chargetime.ocpp.model.core.StopTransactionConfirmation;
import eu.chargetime.ocpp.model.core.StopTransactionRequest;
import eu.chargetime.ocpp.model.dc.evse.protocol.BaseEvseMsgUp;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import eu.chargetime.ocpp.model.dc.type.DeviceStatusCodeType;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 处理桩请求的实现类
 */
@Slf4j
@Component
public class IotEvseProcessor {

    private final Logger logger = LoggerFactory.getLogger(IotEvseProcessor.class);

    @Autowired
    PlugCache plugCache;

    @Autowired
    private CommandFactory commandFactory;

    @Autowired
    private EvseSocketCache evseSocketCache;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private RcdOrderProcessor rcdOrderProcessor;

    @Autowired
    private ConfigurationService configurationService;

    @Autowired
    private RedisRwService redisRwService;

    @Autowired
    private GetTransDataService getTransDataService;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private OrderAdaptationService orderAdaptationService;

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    @EventListener
    public void handleStopChargeEvent(StopChargeReuploadEvent event) {
        if (event.getStopMsg() == null) {
            log.error("[{}] 重新上传StopMsg. 事件数据为空", event.getTid());
            return;
        }
        ChargeStoppedUp stopMsg = event.getStopMsg();
        log.info("[{}] 重新上传StopMsg. orderNo: {}", event.getTid(), stopMsg.getOrderNo());
        this.evseProcess(stopMsg);
    }

    public Confirmation process(BaseEvseMsgUp msg) {
        logger.debug("[{} {}] 处理桩向网关上报报文信息。cmd = {}, plugIdx = {}",
            msg.getBase().getTid(), msg.getBase().getEvseNo(), msg.getBase().getCmdCode(),
            msg.getBase().getPlugIdx());

        this.forgeSeq(msg.getBase());

        //预处理部分报文
        this.preprocess(msg);

        // 上传数据
        return this.evseProcess(msg);    // 把响应消息加入到序列号缓存中
    }

    private void preprocess(BaseEvseMsgUp request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();

        // 收集TCP通讯通道
        switch (request.getBase().getCmdCode()) {
            case EVSE_REGISTER: // 桩注册

                if (StringUtils.isEmpty(request.getBase().getChKey()) || StringUtils.isEmpty(
                    evseNo)) {
                    logger.warn("[{} {}] 桩注册数据有误：channelKey: {}", tid, evseNo,
                        request.getBase().getChKey());
                }

                setRegisterEvseCache(request);

                scheduler.schedule(
                    () -> configurationService.checkEvseMeterValuesSampledData(request), 1,
                    TimeUnit.SECONDS);

                break;
            case EVSE_HB: // 桩心跳
            case CHARGE_HB: // 枪头数据上报

                if (StringUtils.isEmpty(request.getBase().getChKey()) || StringUtils.isEmpty(
                    evseNo)) {
                    logger.warn("[{} {}] 桩心跳数据有误：channelKey = {}", tid, evseNo,
                        request.getBase().getChKey());
                }

                setHBEvseCache(request);

                setPlugCache(request);

                break;

            default:
                break;
        }

    }

    private void setRegisterEvseCache(BaseEvseMsgUp msg) {

        logger.debug("[{}] 设置注册时的桩缓存。channelKey: {}", msg.getBase().getEvseNo(),
            msg.getBase().getChKey());

        EvseConnection connection = new EvseConnection();
        connection.setClientAddress(msg.getBase().getChKey());
        connection.setEvseNo(msg.getBase().getEvseNo());
        connection.setHeartbeatTime(new Date());
        connection.setRegisterTime(new Date());//注册时间

        this.evseSocketCache.put(msg.getBase().getEvseNo(), connection);
    }

    private void setHBEvseCache(BaseEvseMsgUp msg) {
        //logger.debug("设置心跳时的桩缓存。traceId: {}, channelKey: {}, evseNo: {}", msg.gettraceId(), msg.getChannelKey(), msg.getEvseNo());

        String evseNo = msg.getBase().getEvseNo();

        EvseConnection connection = evseSocketCache.get(evseNo);

        if (connection == null) {
            connection = new EvseConnection();
            // RegisterTime真正注册时才更新
            // connection.setRegisterTime(new Date());//注册时间
        }

        connection.setClientAddress(msg.getBase().getChKey());
        connection.setEvseNo(evseNo);
        connection.setHeartbeatTime(new Date());

        this.evseSocketCache.put(evseNo, connection);

    }

    private void setPlugCache(BaseEvseMsgUp msg) {
        String evseId = msg.getBase().getEvseNo();
        int plugId = msg.getBase().getPlugIdx();

        //logger.debug("设置心跳时的枪缓存。evseNo: {}, plugNo: {}", evseId, plugId);

        PlugConnection heartbeat;

        if (plugCache.containsKey(evseId, plugId)) {
            heartbeat = plugCache.get(evseId, plugId);
            heartbeat.setHeartbeatTime(new Date());
        } else {
            heartbeat = new PlugConnection();
            heartbeat.setRegisterTime(
                new Date());//由于注册报文里不含枪号(3.5的协议有枪号，但默认00不代表任何枪号)。这里将第一次发送的心跳作为注册时间。
            heartbeat.setEvseId(evseId);
            heartbeat.setPlugNo(plugId);
            heartbeat.setSeqNo(new AtomicLong(0L));
            plugCache.put(evseId, plugId, heartbeat);
        }
    }

    public Confirmation authorizeProcess(AuthorizeRequest request) {
        EvseMsgBase base = request.getBase();
        String idTag = request.getIdTag();

        ChargeAuthUp msg = new ChargeAuthUp(base);
        msg.setAccount(request.getIdTag());
        OrderStartType orderStartType = orderAdaptationService.estimateOrderType(base.getEvseNo(),
            idTag).getLeft();
        msg.setAuthType(orderStartType);
        return this.process(msg);
    }

    public Confirmation statusNotificationProcess(StatusNotificationRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();

        EvseMsgBase base = request.getBase();
        base.setPlugIdx(request.getConnectorId());
        EvseHbMsgUp msg = new EvseHbMsgUp(base);

        switch (request.getStatus()) {
            case Available:
                msg.setPlugStatus(EvseGunStatus.FREE);
                break;
            case Preparing:
                msg.setPlugStatus(EvseGunStatus.GUN_ATTACHED_VEHICLE);
                break;
            case Charging:
                msg.setPlugStatus(EvseGunStatus.CHARGING);
                break;
            case SuspendedEVSE:
                msg.setPlugStatus(EvseGunStatus.GUN_ATTACHED_VEHICLE);
                break;
            case SuspendedEV:
                msg.setPlugStatus(EvseGunStatus.GUN_ATTACHED_VEHICLE);
                break;
            case Finishing:
                msg.setPlugStatus(EvseGunStatus.CHARGING_COMPLETED);
                break;
            case Reserved:
                msg.setPlugStatus(EvseGunStatus.RESERVATION);
                break;
            case Unavailable:
            case Faulted:
                msg.setPlugStatus(EvseGunStatus.ERROR);
                break;
        }

        if (request.getErrorCode() != null) {
            switch (request.getErrorCode()) {
                case ConnectorLockFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C11.getCode());
                    break;
                case EVCommunicationError:
                    msg.setErrorCode(DeviceStatusCodeType.C5000.getCode());
                    break;
                case GroundFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C04.getCode());
                    break;
                case HighTemperature:
                    msg.setErrorCode(DeviceStatusCodeType.C10.getCode());
                    break;
                case InternalError:
                    msg.setErrorCode(DeviceStatusCodeType.C5001.getCode());
                    break;
                case LocalListConflict:
                    msg.setErrorCode(DeviceStatusCodeType.C5002.getCode());
                    break;
                case NoError:
                    break;
                case OtherError:
                    msg.setErrorCode(DeviceStatusCodeType.C13.getCode());
                    break;
                case OverCurrentFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C5003.getCode());
                    break;
                case OverVoltage:
                    msg.setErrorCode(DeviceStatusCodeType.C5004.getCode());
                    break;
                case PowerMeterFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C5005.getCode());
                    break;
                case PowerSwitchFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C5006.getCode());
                    break;
                case ReaderFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C5007.getCode());
                    break;
                case ResetFailure:
                    msg.setErrorCode(DeviceStatusCodeType.C5008.getCode());
                    break;
                case UnderVoltage:
                    msg.setErrorCode(DeviceStatusCodeType.C5009.getCode());
                    break;
                case WeakSignal:
                    msg.setErrorCode(DeviceStatusCodeType.C5010.getCode());
                    break;
            }
        }

        if (StringUtils.isNotBlank(request.getVendorErrorCode())) {
            GwEvseVo evse = evseRepository.getEvse(evseNo);
            if (EvseBrand.RCD.equals(evse.getBrand())) {
                Optional.ofNullable(RcdErrorCode.valueOfByStr(request.getVendorErrorCode()))
                    .map(RcdErrorCode::getCodeType)
                    .ifPresent(t -> {
                        msg.setErrorCode(t.getCode());
                    });
            } else if (EvseBrand.WINLINE.equals(evse.getBrand())) {
                Optional.ofNullable(WinlineErrorCode.valueOfByStr(request.getVendorErrorCode()))
                    .map(WinlineErrorCode::getCodeType)
                    .ifPresent(t -> {
                        msg.setErrorCode(t.getCode());
                    });
            }
        }

        return this.process(msg);
    }

    public Confirmation dataTransferProcess(DataTransferRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();

        DataTransferMessage msg = DataTransferMessage.getByName(request.getMessageId());
        log.info("[{}] dataTransferProcess. msg: {}", tid, msg.name());
        IDataTransferStrategy strategy = strategyFactory.getStrategy(msg);
        if (strategy == null) {
            log.error("[{}] dataTransferProcess. strategy不存在 messageId: {}", tid,
                request.getMessageId());
            DataTransferConfirmation confirmation = new DataTransferConfirmation(
                DataTransferStatus.UnknownMessageId);
            confirmation.setBase(request.getBase());
            return confirmation;
        }

        DataTransferConfirmation confirmation = strategy.dataProcessing(request);

        if (strategy instanceof BillReqTransferStrategy
            && DataTransferStatus.Accepted.equals(confirmation.getStatus())) {
            GwEvseVo evse = evseRepository.getEvse(evseNo);

            if (EvseBrand.RCD.equals(evse.getBrand())) {
                int plugIdx = confirmation.getBase().getPlugIdx();
                PlugVo plugCache = redisRwService.getPlugRedisCache(evseNo, plugIdx);
                if (plugCache == null) {
                    log.error("[{}] 订单结束后找不到枪头. evseNo: {}, plugIdx: {}", tid, evseNo,
                        plugIdx);
                } else if (StringUtils.isNotBlank(plugCache.getOrderNo())) {
                    OrderData order = evseOrderRepository.getOrder(plugCache.getOrderNo());
                    rcdOrderProcessor.processBillReqMsg(tid, plugCache.getOrderNo(),
                            order.getRcdBillReqDto()) // 要用order缓存中的billReqDto（时间经过特殊处理）
                        .ifPresent(stopMsg -> {
                            // 用bill_req补全stopMsg后，上报订单结束
                            stopMsg.getBase().setTid(tid);
                            this.evseProcess(stopMsg);
                        });
                }
            }
        }

        return confirmation;
    }

    public HeartbeatConfirmation heartbeatProcess(HeartbeatRequest request) {
        EvseMsgBase base = request.getBase();
        String evseNo = base.getEvseNo();

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        if (evse == null) {
            return new HeartbeatConfirmation(ZonedDateTime.now(), base);
//        } else {
//            this.setHBEvseCache(new BaseEvseMsgUp(base));
//            scheduler.schedule(() -> this.reUploadPlugStatus(base), 10, TimeUnit.SECONDS);
        }
        ZonedDateTime nowByTz = OcppZonedDateTime.issueTime(evse.getBrand(), evse.getTimeZone());
        return new HeartbeatConfirmation(nowByTz, base);
    }

    public void reUploadPlugStatus(EvseMsgBase base) {
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        List<Integer> needReportPlugIdxList = new ArrayList<>();

        GwEvseVo evseVo = evseRepository.getEvse(evseNo);
        if (evseVo == null) {
            return;
        }
        log.info("[{}] reUploadPlugStatus. evseStatus: {}, plugs: {}", tid, evseVo.getStatus(),
            JsonUtils.toJsonString(evseVo.getPlugs()));
        if (evseVo.getStatus() == null || EvseStatus.OFFLINE.equals(evseVo.getStatus())) {
            // 网关本地缓存中不存在：网关重启WS连接中断，桩重新建立连接后上报Heartbeat
            int plugNum = Optional.ofNullable(evseVo.getPlugNum()).orElse(2);
            IntStream.range(1, plugNum + 1).forEach(needReportPlugIdxList::add);
        } else if (CollectionUtils.isNotEmpty(evseVo.getPlugs())) {
            evseVo.getPlugs().forEach(plug -> {
                // 若之前上报桩离线到云端，现在重新有了Heartbeat要手动上报离线前的枪状态（因为桩只在状态变化时才主动上报）
                if (plug.getStatus() == null || PlugStatus.OFFLINE.equals(plug.getStatus())) {
                    needReportPlugIdxList.add(plug.getIdx());
                }
            });
        }
        if (!needReportPlugIdxList.isEmpty()) {
            // 1. 从trace_data中找到此桩目标枪最近一次的StatusNotification
            Map<Integer, StatusNotificationRequest> plugMap = getTransDataService.queryStatusNotificationMsg(
                base, needReportPlugIdxList);
            // 2. 循环调用 this.statusNotificationProcess() 上报云端
            plugMap.forEach((k, v) -> {
                v.getBase().setTid(tid);
                this.statusNotificationProcess(v);
            });
        }
    }

    private ZonedDateTime findOrderStartTime(MeterValuesRequest request, String timeZone) {
        ZonedDateTime startTime;
        if (request.getMeterValue() != null && request.getMeterValue().length > 0) {
            MeterValue meterValue = request.getMeterValue()[0];
            startTime = meterValue.getTimestamp();
        } else {
            startTime = OcppZonedDateTime.nowTz(timeZone);
        }
        return startTime;
    }

    public Confirmation meterProcess(MeterValuesRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        ZonedDateTime mvStartTime = findOrderStartTime(request, evse.getTimeZone());

        EvseMsgBase base = request.getBase();
        base.setPlugIdx(request.getConnectorId());
        BaseEvseMsgUp msgUp = new BaseEvseMsgUp(base);

        this.forgeSeq(base);

        this.preprocess(msgUp);

        if (request.getTransactionId() != null) {
            boolean continueFlag = true;

            OrderData order = evseOrderRepository.getOrder(request.getTransactionId());
            log.info("[{}] 订单号: {}, 订单状态: {}", tid,
                order != null ? order.getOrderNo() : null,
                order != null ? order.getOrderStatus() : null);

            if (order == null || order.getOrderStatus() == null || ChargeOrderStatus.INIT.equals(
                order.getOrderStatus()) || ChargeOrderStatus.STARTING.equals(
                order.getOrderStatus())) {
                // 若订单不存在 或 订单启动中，直接上报充电开始
                base.setCmdCode(EvseMessageType.CHARGE_START);

                ChargeStartedUp evseReq = new ChargeStartedUp(base);
                evseReq.setStartTransReq(false);
                evseReq.setOrderStartResult(0);
                evseReq.setTransId(request.getTransactionId());
                evseReq.setStartTime(mvStartTime.toEpochSecond());
                evseReq.setFeeDM(CalcType.MONEY.getCode());
                OrderStopMode stopMode = new OrderStopMode();
                stopMode.setType(ChargeMode.FULL);
                evseReq.setStopMode(stopMode);

                if (order != null) {
                    evseReq.setOrderNo(order.getOrderNo());
                    evseReq.setOrderStartType(OrderStartType.valueOf(order.getStartType()));
                    evseReq.setAccountNo(order.getAccount());
                    evseReq.setStopMode(order.getStopMode());
                    evseReq.setStartTime(order.getStartTime());
                    evseReq.setStartMeter(order.getStartMeter());
                } else {
                    // 补全订单上报参数
                    orderAdaptationService.complementChargeStartedUp(tid, evseNo, evseReq);
                }
                Confirmation chargeStartConfirmation = this.evseProcess(evseReq);
                continueFlag = chargeStartConfirmation != null;
            }

            // 重新获取订单状态
            order = evseOrderRepository.getOrder(request.getTransactionId());
            if (continueFlag && ChargeOrderStatus.START.equals(order.getOrderStatus())) {
                // 枪头数据上报
                base.setCmdCode(EvseMessageType.CHARGE_HB);

                ChargeHbUp evseReq = new ChargeHbUp(base);

                BigDecimal startMeter = BigDecimal.ZERO; // 充电开始电表读数（用于补漏），单位 kWh
                BigDecimal currMeter = BigDecimal.ZERO; // 当前电表读数，单位 kWh
                BigDecimal power = null;
                BigDecimal kwh = BigDecimal.ZERO;
                Integer soc = null;
                Integer evseTemp = null;
                BigDecimal voltage = null;
                BigDecimal voltageA = null;
                BigDecimal voltageB = null;
                BigDecimal voltageC = null;
                BigDecimal current = null;
                BigDecimal currentA = null;
                BigDecimal currentB = null;
                BigDecimal currentC = null;
                List<String> energyList = List.of("Energy.Active.Import.Register",
                    "Energy.Active.Import.Interval");

                if (request.getMeterValue() != null) {
                    MeterValue mv = request.getMeterValue()[0];
                    if (mv != null && mv.getSampledValue() != null) {

                        evseReq.setMvHbTime(mv.getTimestamp());

                        for (SampledValue sampledValue : mv.getSampledValue()) {
                            if ("Energy.Active.Import.Interval".equals(
                                sampledValue.getMeasurand())) {
                                BigDecimal v = new BigDecimal(sampledValue.getValue());
                                kwh = "Wh".equals(sampledValue.getUnit()) ? v.movePointLeft(3) : v;

                            } else if ("Energy.Active.Import.Register".equals(
                                sampledValue.getMeasurand())) {
                                BigDecimal v = new BigDecimal(sampledValue.getValue());
                                BigDecimal tempMeter =
                                    "Wh".equals(sampledValue.getUnit()) ? v.movePointLeft(3) : v;

                                if (DecimalUtils.isZero(startMeter)) {
                                    startMeter = tempMeter;
                                } else {
                                    startMeter = DecimalUtils.min(startMeter, tempMeter);
                                }
                                currMeter = DecimalUtils.max(currMeter, tempMeter);

                            } else if ("Current.Import".equals(sampledValue.getMeasurand())) {
                                BigDecimal temp = new BigDecimal(sampledValue.getValue());
                                if ("L1".equals(sampledValue.getPhase())) {
                                    currentA = temp;
                                } else if ("L2".equals(sampledValue.getPhase())) {
                                    currentB = temp;
                                } else if ("L3".equals(sampledValue.getPhase())) {
                                    currentC = temp;
                                } else {
                                    current = temp;
                                }

                            } else if ("Voltage".equals(sampledValue.getMeasurand())) {
                                BigDecimal temp = new BigDecimal(sampledValue.getValue());
                                if ("L1".equals(sampledValue.getPhase())) {
                                    voltageA = temp;
                                } else if ("L2".equals(sampledValue.getPhase())) {
                                    voltageB = temp;
                                } else if ("L3".equals(sampledValue.getPhase())) {
                                    voltageC = temp;
                                } else {
                                    voltage = temp;
                                }

                            } else if ("Power.Active.Import".equals(sampledValue.getMeasurand())) {
                                BigDecimal v = new BigDecimal(sampledValue.getValue());
                                power = "W".equals(sampledValue.getUnit()) ? v.movePointLeft(3) : v;

                            } else if ("SoC".equals(sampledValue.getMeasurand())) {
                                BigDecimal socValue = new BigDecimal(sampledValue.getValue());
                                soc = socValue.setScale(0, RoundingMode.HALF_UP).intValue();

                            } else if ("Temperature".equals(sampledValue.getMeasurand())) {
                                BigDecimal tempValue = new BigDecimal(sampledValue.getValue());
                                evseTemp = tempValue.setScale(0, RoundingMode.HALF_UP).intValue();

                            }
                        }
                    }
                }

                OrderData updateOrder = new OrderData(order.getOrderNo());
                if (DecimalUtils.gtZero(startMeter) && (order.getStartMeter() == null
                    || DecimalUtils.isZero(order.getStartMeter()))) {
                    updateOrder.setStartMeter(startMeter);
                }
                if (order.getStartSoc() == null && soc != null) {
                    updateOrder.setStartSoc(soc);
                }
                if (DecimalUtils.isZero(currMeter) && order.getStartMeter() != null) {
                    // 桩未上报meter时，手动计算
                    currMeter = DecimalUtils.add(order.getStartMeter(), kwh);
                    updateOrder.setCurrMeter(currMeter);
                } else if (DecimalUtils.gtZero(currMeter)) {
                    updateOrder.setCurrMeter(currMeter);
                }

                if (DecimalUtils.gtZero(kwh)) {
                    evseReq.setKwh(kwh);
                } else if (DecimalUtils.gtZero(currMeter) && order.getStartMeter() != null) {
                    kwh = currMeter.subtract(order.getStartMeter());
                    evseReq.setKwh(kwh);
                }

                if (NumberUtils.gtZero(order.getStartTime()) && evseReq.getMvHbTime() != null) {
                    long gap = evseReq.getMvHbTime().toEpochSecond() - order.getStartTime();
                    evseReq.setDuration((int) (gap / 60));
                }
                evseReq.setOrderNo(order.getOrderNo());
                evseReq.setSoc(soc);
                evseReq.setPower(power);
                evseReq.setEvseTemp(evseTemp);
                evseReq.setVoltageA(voltageA);
                evseReq.setCurrentA(currentA);
                evseReq.setVoltageB(voltageB);
                evseReq.setCurrentB(currentB);
                evseReq.setVoltageC(voltageC);
                evseReq.setCurrentC(currentC);
                //直流桩电压和电流
                evseReq.setOutputCurrent(current);
                evseReq.setOutputVoltage(voltage);
                updateOrder.setSoc(soc);
                evseOrderRepository.updateOrder(updateOrder);

                // 根据order中 LatestOrderFee 填充 evseReq
                Optional.ofNullable(order.getLatestOrderFee()).ifPresent(orderFee -> {
                    evseReq.setDuration(orderFee.getChargeTime());
                    evseReq.setElecFee(orderFee.getElecAmount());
                    evseReq.setServFee(orderFee.getServAmount());
                });

                return this.evseProcess(evseReq);
            } else {
                log.warn("[{}] 未适配的orderStatus. orderNo: {}, orderStatus: {}", tid,
                    order.getOrderNo(), order.getOrderStatus());
            }
        } else {
            log.warn("[{}] 无效的transactionId. transactionId: {}", tid,
                request.getTransactionId());
        }
        return new MeterValuesConfirmation(base); // 正常返回
    }

    public Confirmation startTransProcess(StartTransactionRequest request) {
        EvseMsgBase base = request.getBase();
        base.setPlugIdx(request.getConnectorId());
        Charge4EvseReq evseReq = new Charge4EvseReq(base);

        String idTag = request.getIdTag();
        if (StringUtils.isBlank(idTag)) {
            // 也要生成一个网关订单号返回给桩
            Integer transId = orderNoGenerator.generateOrderNumber().getRight();
            return new StartTransactionConfirmation(new IdTagInfo(AuthorizationStatus.Invalid),
                transId);
        }
        BigDecimal startMeter = Optional.ofNullable(request.getMeterStart())
            .map(BigDecimal::valueOf).map(e -> e.movePointLeft(3)).orElse(null);
        long startTime = request.getTimestamp().toEpochSecond();
        OrderData order = evseOrderRepository.getOrder(idTag);

        Pair<OrderStartType, String> pair = orderAdaptationService.estimateOrderType(
            base.getEvseNo(), idTag);
        OrderStartType orderStartType = pair.getLeft();
        String accountNo = pair.getRight();

        // idTag就是订单号时，是平台远程开启的订单
        // idTag全为0和全为F时，是桩端即插即用自启订单
        // idTag全为1是信用卡开启充电，云端处理逻辑同桩端自启订单
        // idTag全为2是密码开启充电，云端处理逻辑同桩端自启订单
        if (order != null || OrderStartType.EVSE_AUTO.equals(orderStartType)
            || OrderStartType.CREDIT_CARD.equals(orderStartType)) {
            base.setCmdCode(EvseMessageType.CHARGE_START);
            return this.evseAutoProcess(base, idTag, startMeter, startTime, order, orderStartType,
                accountNo);
        } else {
            evseReq.setOrderStartType(orderStartType);
            evseReq.setAccount(idTag);
            evseReq.setStartTime(startTime);
            evseReq.setStartMeter(startMeter);
        }

        evseReq.setChargeMode(ChargeMode.FULL);
        return this.process(evseReq);
    }

    public Confirmation evseAutoProcess(EvseMsgBase base, String idTag, BigDecimal startMeter,
        long startTime, OrderData order, OrderStartType orderStartType, String accountNo) {
        String tid = base.getTid();
        ChargeStartedUp startedUp = new ChargeStartedUp();
        startedUp.setBase(base);
        startedUp.setStartTransReq(true);
        startedUp.setOrderStartResult(0);
        startedUp.setStartTime(startTime);
        startedUp.setStartMeter(startMeter);

        this.forgeSeq(base);

        if (order != null) {
            log.info("[{}] order: {}", tid, order);
            startedUp.setOrderStartType(OrderStartType.valueOf(order.getStartType()));
            startedUp.setOrderNo(order.getOrderNo());
            startedUp.setTransId(order.getTransId());
            startedUp.setStopMode(order.getStopMode());

            OrderData updateOrder = new OrderData(order.getOrderNo());
            updateOrder.setStartMeter(startMeter);
            updateOrder.setOrderStatus(ChargeOrderStatus.INIT);
            evseOrderRepository.updateOrder(updateOrder);

        } else {
            startedUp.setOrderStartType(orderStartType);
            startedUp.setAccountNo(accountNo);
            if (OrderStartType.CREDIT_CARD.equals(orderStartType)) {
                if (!idTag.matches("^1+$")) {
                    startedUp.setAccountNo(idTag); // 脱敏信用卡号
                }
            }
            Pair<String, Integer> orderPair = orderNoGenerator.generateOrderNumber();
            String orderNo = orderPair.getLeft();
            log.info("[{}] 桩自动启动充电. orderNo: {}", tid, orderNo);
            startedUp.setOrderNo(orderNo);
            startedUp.setTransId(orderPair.getRight());

            startedUp.setStopMode(new OrderStopMode().setType(ChargeMode.FULL)
                .setAmount(BigDecimal.ZERO)
                .setKwh(BigDecimal.ZERO)
                .setTime(0L));
            startedUp.setFeeDM(CalcType.MONEY.getCode());
        }

        return this.evseProcess(startedUp);
    }

    public Confirmation stopChargeProcess(StopTransactionRequest request) {
        EvseMsgBase base = request.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();

        GwEvseVo evse = evseRepository.getEvse(evseNo);

        StopTransactionConfirmation confirmation = new StopTransactionConfirmation();
        confirmation.setBase(base);

        ChargeStoppedUp stopMsg = new ChargeStoppedUp(base);
        stopMsg.setAccount(request.getIdTag());
        stopMsg.setFeeDM(CalcType.MONEY.getCode());
        stopMsg.setStopTime(request.getTimestamp().toEpochSecond());
        stopMsg.setStopMeter(Optional.ofNullable(request.getMeterStop()).map(BigDecimal::valueOf)
            .map(e -> e.movePointLeft(3)).orElse(null));
        this.parseTransactionStopReason(stopMsg, evse.getBrand(), request.getReason());

        OrderData order = evseOrderRepository.getOrder(request.getTransactionId());
        if (order != null) {
            log.info("[{}] stopChargeProcess. order: {}", tid, order);

            base.setPlugIdx(order.getIdx());
            this.forgeSeq(base);

            stopMsg.setOrderStartType(OrderStartType.valueOf(order.getStartType() & 0xFF));
            stopMsg.setTransId(request.getTransactionId());
            stopMsg.setOrderNo(order.getOrderNo());

            stopMsg.setStartSoc(order.getStartSoc());
            stopMsg.setStopSoc(order.getSoc());
            stopMsg.setStartTime(order.getStartTime());
            stopMsg.setStartMeter(order.getStartMeter());
            if (stopMsg.getStopMeter() == null) {
                stopMsg.setStopMeter(order.getCurrMeter());
            }

            BigDecimal kwh = BigDecimal.ZERO;

            if (request.getTransactionData() != null) {
                for (MeterValue mv : request.getTransactionData()) {
                    if (mv != null && mv.getSampledValue() != null) {
                        for (SampledValue sampledValue : mv.getSampledValue()) {
                            if ("Energy.Active.Import.Interval".equals(
                                sampledValue.getMeasurand())) {
                                BigDecimal v = new BigDecimal(sampledValue.getValue());
                                BigDecimal tempKwh = "Wh".equals(sampledValue.getUnit())
                                    ? v.movePointLeft(3) : v;
                                kwh = DecimalUtils.max(kwh, tempKwh);
                            }
                        }
                    }
                }
            }
            if (DecimalUtils.gtZero(kwh)) {
                stopMsg.setKwh(kwh);
            } else if (stopMsg.getStartMeter() != null && stopMsg.getStopMeter() != null) {
                kwh = stopMsg.getStopMeter().subtract(stopMsg.getStartMeter());
                stopMsg.setKwh(kwh);
            }

            stopMsg.setPriceCode(order.getPriceCode());

            if (EvseBrand.RCD.equals(evse.getBrand())) {
                if (order.getRcdBillReqDto() == null) {
                    log.info("[{} {}] 还未收到bill_req消息，延迟上报. orderNo: {}", tid, evseNo,
                        order.getOrderNo());
                    rcdOrderProcessor.stashingStopTransMsg(tid, stopMsg);

                    confirmation.setIdTagInfo(new IdTagInfo(AuthorizationStatus.Accepted));
                    return confirmation;
                } else {
                    log.info("[{} {}] 已收到bill_req消息，补全stopMsg. orderNo: {}", tid, evseNo,
                        order.getOrderNo());
                    rcdOrderProcessor.completeStopMsg(stopMsg, order.getRcdBillReqDto());
                }
            }

            return this.evseProcess(stopMsg);
        } else {
            // 订单不存在时，找到最近一笔StartTrans重新拼凑订单信息
            orderAdaptationService.makeUpStopMsgByStartTrans(stopMsg, evse);
            this.forgeSeq(stopMsg.getBase());
            return this.evseProcess(stopMsg);
        }
    }

    private void parseTransactionStopReason(ChargeStoppedUp stopMsg, EvseBrand brand,
        String reason) {
        if (StringUtils.isBlank(reason)) {
            return;
        }
        int completeCode = 0;
        int errorCode = 0;
        try {
            Reason tempEnum = Reason.valueOf(reason);
            switch (tempEnum) {
                case DeAuthorized:
                case EVDisconnected:
                case HardReset:
                case Other:
                case PowerLoss:
                case Reboot:
                case SoftReset:
                case UnlockCommand:
                    completeCode = OrderCompleteCode.ABNORMAL_STOP.getCode();
                    break;
                case EmergencyStop:
                    completeCode = OrderCompleteCode.MANUAL_STOP_BUTTON.getCode();
                    errorCode = RcdStopReason.ERROR_CODE_40.getCode();
                    break;
                case Local:
                    // 常规的交易终止
                    break;
                case Remote:
                    completeCode = OrderCompleteCode.PLATFORM_STOP.getCode();
                    break;
                default:
                    completeCode = OrderCompleteCode.ABNORMAL_STOP.getCode();
                    break;
            }
            stopMsg.setCompleteCode(completeCode);
            stopMsg.setErrorCode(errorCode);
        } catch (Exception e) {
            log.info("[{}] 解析停止原因失败. reason: {}", stopMsg.getBase().getTid(), reason);
        }

    }

    private void forgeSeq(EvseMsgBase base) {
        // 为做逻辑兼容,伪造一个报文序列号
        PlugConnection conn = plugCache.get(base.getEvseNo(), base.getPlugIdx());
        if (conn != null) {
            long seqNo = conn.getSeqNo().addAndGet(1L);
            base.setSeq(seqNo);
        } else {
            logger.warn("[{} {}] 获取枪头连接失败. evseNo: {}", base.getTid(), base.getChKey(),
                base.getEvseNo());
        }
    }

    /**
     * 接收桩端上传的封装数据
     *
     * @param msg
     */
    private Confirmation evseProcess(BaseEvseMsgUp msg) {
        UpstreamHandler2 handler = commandFactory.getHandler(msg);

        if (handler == null) {
            logger.warn("未知的指令：{}! ",
                ByteUtil.byteToHexStr(msg.getBase().getCmdCode().getCode()));
            return null;
        }

        return handler.processRequest(msg)
            .map(e -> {
                e.getDownMsg().setBase(msg.getBase());
                return e.getDownMsg();
            })
            .block();
    }

}
