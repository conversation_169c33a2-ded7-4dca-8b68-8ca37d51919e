package com.cdz360.iot.gw.south.server;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.biz.GetTransDataService;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.core.biz.OrderNoGenerator;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStartedUp;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStoppedUp;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderDetail;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import com.cdz360.iot.gw.util.PriceUtil;
import eu.chargetime.ocpp.model.core.StartTransactionRequest;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderAdaptationService {

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private GetTransDataService getTransDataService;

    /**
     * 根据桩品牌和idTag判断订单类型
     *
     * @param evseNo
     * @param idTag
     * @return <订单类型, accountNo>
     */
    public Pair<OrderStartType, String> estimateOrderType(@Nonnull String evseNo, String idTag) {
        if (StringUtils.isBlank(idTag)) {
            return Pair.of(OrderStartType.UNKNOWN, null);
        }

        if (idTag.matches("^0+$") || idTag.matches("^F+$")) {
            // 润诚达 即插即用充电：全为0和全为F
            // 永联 密码充电：****************
            return Pair.of(OrderStartType.EVSE_AUTO, null);
        } else if (idTag.matches("^1+$")) {
            // 润诚达 信用卡充电：全为1
            return Pair.of(OrderStartType.CREDIT_CARD, null);
        } else if (idTag.matches("^2+$")) {
            // 润诚达 密码启动充电：全为2
            return Pair.of(OrderStartType.EVSE_AUTO, null);
        } else if (idTag.matches(IotGwConstants.CREDIT_CARD_REGULAR)) {
            // 信用卡
            return Pair.of(OrderStartType.CREDIT_CARD, idTag);
        }

        // 不确定鉴权类别，云端会处理
        return Pair.of(OrderStartType.UNKNOWN, idTag);

        /*
        GwEvseVo evse = evseRepository.getEvse(evseNo);
        if (EvseBrand.WINLINE.equals(evse.getBrand())) {
            if (idTag != null && idTag.matches(IotGwConstants.WINLINE_ONLINE_CARD_REGULAR)) {
                return Pair.of(OrderStartType.ONLINE_CARD, idTag);
            } else if (idTag != null && idTag.matches(IotGwConstants.CREDIT_CARD_REGULAR)) {
                return Pair.of(OrderStartType.CREDIT_CARD, idTag);
            } else {
                return Pair.of(OrderStartType.ONLINE_VIN, idTag);
            }
        } else {
            if (idTag != null && idTag.matches(IotGwConstants.CREDIT_CARD_REGULAR)) {
                return Pair.of(OrderStartType.CREDIT_CARD, idTag);
            } else {
                return Pair.of(OrderStartType.ONLINE_CARD, idTag);
            }
        }
        */
    }

    /**
     * 订单不存在时，拼装出一个订单数据
     *
     * @param tid
     * @param startedUp
     * @return
     */
    public OrderData assembleEvseAutoOrderData(String tid, ChargeStartedUp startedUp) {
        OrderData data = new OrderData();

        data.setTransId(startedUp.getTransId());
        data.setOrderNo(startedUp.getOrderNo());
        data.setOrderStatus(ChargeOrderStatus.INIT);

        data.setStopMode(startedUp.getStopMode());

        data.setStartType(startedUp.getOrderStartType().getCode());

        data.setEvseNo(startedUp.getBase().getEvseNo());
        data.setIdx(startedUp.getBase().getPlugIdx());

        log.info("[{}] assembleEvseAutoOrderData data: {}", tid, data);
        return data;
    }

    /**
     * 从trace_data中补全ChargeStartedUp
     *
     * @param tid
     * @param evseNo
     * @param req
     */
    public void complementChargeStartedUp(String tid, String evseNo, ChargeStartedUp req) {

        if (StringUtils.isBlank(req.getOrderNo())) {
            Pair<String, Integer> orderPair = orderNoGenerator.generateOrderNumber();
            req.setOrderNo(orderPair.getLeft());
            // 注意：这里仍然用实际报文中的transId
            // req.setTransId(orderPair.getSecond());
        }

        // 从trace_data中找到StartTransactionRequest.req
        getTransDataService.queryStartTransactionMsg(tid, evseNo, null, req.getBase().getPlugIdx())
            .ifPresent(startReqMsg -> {
                Pair<OrderStartType, String> pair = this.estimateOrderType(evseNo,
                    startReqMsg.getIdTag());
                req.setOrderStartType(pair.getLeft());
                req.setAccountNo(pair.getRight());
                req.setStartTime(startReqMsg.getTimestamp().toEpochSecond());
                req.setStartMeter(
                    Optional.ofNullable(startReqMsg.getMeterStart()).map(BigDecimal::valueOf)
                        .map(e -> e.movePointLeft(3)).orElse(null));
            });
    }

    /**
     * 补全StopMsg
     *
     * @param evse
     * @param stopMsg
     */
    public void makeUpStopMsgByStartTrans(@Nonnull ChargeStoppedUp stopMsg,
        @Nullable GwEvseVo evse) {
        String tid = stopMsg.getBase().getTid();
        String evseNo = stopMsg.getBase().getEvseNo();
        String idTag = stopMsg.getAccount();

        stopMsg.setOrderNo(orderNoGenerator.generateOrderNumber().getLeft());

        // 从trace_data中根据idTag查询对应的StartTransaction消息
        Optional<StartTransactionRequest> reqMsgOptional = getTransDataService.queryStartTransactionMsg(
            tid, evseNo, idTag, null);
        if (reqMsgOptional.isEmpty()) {
            return;
        }
        StartTransactionRequest startReqMsg = reqMsgOptional.get();

        stopMsg.getBase().setPlugIdx(startReqMsg.getConnectorId());
        Pair<OrderStartType, String> pair = this.estimateOrderType(evseNo, idTag);
        stopMsg.setOrderStartType(pair.getLeft());
        stopMsg.setAccount(idTag);
        stopMsg.setStartTime(startReqMsg.getTimestamp().toEpochSecond());
        stopMsg.setStartMeter(
            Optional.ofNullable(startReqMsg.getMeterStart()).map(BigDecimal::valueOf)
                .map(e -> e.movePointLeft(3)).orElse(null));

        this.complementOrderDetailAndFee(stopMsg, evse);
    }

    /**
     * 补全Order.detail 和 费用
     *
     * @param stopMsg
     * @param evse
     */
    public void complementOrderDetailAndFee(@Nonnull ChargeStoppedUp stopMsg,
        @Nullable GwEvseVo evse) {
        String tid = stopMsg.getBase().getTid();
        String evseNo = stopMsg.getBase().getEvseNo();
        log.debug("[{} {}] complementOrderDetailAndFee.", tid, evseNo);
        BigDecimal totalKwh = Optional.ofNullable(stopMsg.getKwh()).orElse(BigDecimal.ZERO);
        if (stopMsg.getStartMeter() != null && stopMsg.getStopMeter() != null) {
            totalKwh = stopMsg.getStopMeter().subtract(stopMsg.getStartMeter());
        }
        stopMsg.setKwh(totalKwh);
        if (evse == null) {
            evse = evseRepository.getEvse(evseNo);
        }

        log.info(
            "[{}] 从StartTransaction消息补全StopMsg. startTime: {}, stopTime: {}, evsePriceId: {}",
            tid, stopMsg.getStartTime(), stopMsg.getStopTime(),
            Optional.ofNullable(evse).map(GwEvseVo::getPrice).map(ChargePriceVo::getId)
                .orElse(null));
        if (NumberUtils.gtZero(stopMsg.getStartTime()) && NumberUtils.gtZero(stopMsg.getStopTime())
            && DecimalUtils.gtZero(totalKwh) && evse != null && evse.getPrice() != null
            && CollectionUtils.isNotEmpty(evse.getPrice().getItemList())) {

            // 如果桩是固定计费或充电时间未跨时段，则模拟一个detail
            List<ChargePriceItem> priceItemList = evse.getPrice().getItemList();
            ChargePriceItem priceItem = null;
            if (priceItemList.size() == 1) {
                priceItem = priceItemList.get(0);
            } else {
                // 遍历priceItemList，找到能完全覆盖stopMsg.getStartTime()和stopMsg.getStopTime()的priceItem
                priceItem = this.getPriceItemCoveringTimeRange(stopMsg.getStartTime(),
                    stopMsg.getStopTime(), evse.getPrice(), evse.getTimeZone());
            }
            log.debug("[{} {}] 获取价格信息 priceItem: {}", tid, evseNo,
                JsonUtils.toJsonString(priceItem));

            if (priceItem != null) {
                // 已找到完全匹配充电时段的计费信息
                OrderDetail newOrderDetail = new OrderDetail();
                BigDecimal elecFee = priceItem.getElecPrice().multiply(totalKwh)
                    .setScale(4, RoundingMode.HALF_UP);
                BigDecimal servFee = priceItem.getServPrice().multiply(totalKwh)
                    .setScale(4, RoundingMode.HALF_UP);
                newOrderDetail.setStartTimeStr(priceItem.getStartTime())
                    .setStopTimeStr(priceItem.getEndTime()).setStartTime(stopMsg.getStartTime())
                    .setStopTime(stopMsg.getStopTime()).setStartMeter(stopMsg.getStartMeter())
                    .setKwh(totalKwh).setElecPrice(priceItem.getElecPrice()).setElecFee(elecFee)
                    .setServPrice(priceItem.getServPrice()).setServFee(servFee);
                Optional.ofNullable(priceItem.getCategory())
                    .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                stopMsg.setDetail(List.of(newOrderDetail));

                stopMsg.setElecFee(elecFee);
                stopMsg.setServFee(servFee);
            } else {
                // 充电时间跨时间段了，按秒平均分电量，计费每个时段所充的电量和费用
                long subtractTime = stopMsg.getStopTime() - stopMsg.getStartTime();
                BigDecimal kwhPerSecond = totalKwh.divide(BigDecimal.valueOf(subtractTime), 6,
                    RoundingMode.HALF_UP);
                log.debug("[{} {}] totalKwh = {}, subtractTime = {}, kwhPerSecond = {}", tid,
                    evseNo, totalKwh, subtractTime, kwhPerSecond);

                ZoneOffset zone = OcppZonedDateTime.parseTimeZoneOrDefault(evse.getTimeZone());
                ZonedDateTime actualStartDateTime = ZonedDateTime.ofInstant(
                    Instant.ofEpochSecond(stopMsg.getStartTime()), zone);
                BigDecimal actualStartMeter = Optional.ofNullable(stopMsg.getStartMeter())
                    .orElse(BigDecimal.ZERO);

                ZonedDateTime stopDateTime = ZonedDateTime.ofInstant(
                    Instant.ofEpochSecond(stopMsg.getStopTime()), zone);

                List<ChargePriceItem> sortedPriceItems = PriceUtil.sortPriceItems(priceItemList);

                List<OrderDetail> needPushOrderDetails = new ArrayList<>();
                ZonedDateTime loopStartTime = actualStartDateTime;
                BigDecimal loopStartMeter = actualStartMeter;
                boolean isLastPriceItem = false;
                // 循环priceItem，从stopMsg.startTime所在时段开始补全OrderDetail，到stopMsg.stopTime所在时段结束
                for (ChargePriceItem priceItemm : sortedPriceItems) {
                    log.debug("[{} {}] loopStartTime = {}, loopStartMeter = {}, priceItem = {}", tid, evseNo,
                        loopStartTime, loopStartMeter, JsonUtils.toJsonString(priceItemm));
                    if (!PriceUtil.isTimeInRange(loopStartTime.toLocalTime(), priceItemm.getStartTime(),
                        priceItemm.getEndTime())) {
                        continue;
                    }
                    if (isLastPriceItem) {
                        break;
                    }

                    isLastPriceItem = PriceUtil.isTimeInRange(stopDateTime.toLocalTime(),
                        priceItemm.getStartTime(), priceItemm.getEndTime());
                    log.debug("[{} {}] isLastPriceItem = {}", tid, evseNo, isLastPriceItem);

                    OrderDetail newOrderDetail = new OrderDetail();
                    newOrderDetail.setStartTimeStr(priceItemm.getStartTime())
                        .setStopTimeStr(priceItemm.getEndTime())
                        .setElecPrice(priceItemm.getElecPrice())
                        .setServPrice(priceItemm.getServPrice())
                        .setStartMeter(loopStartMeter)
                        .setStartTime(loopStartTime.toEpochSecond());

                    long stopTime = 0;
                    if (isLastPriceItem) {
                        stopTime = stopDateTime.toEpochSecond();
                        newOrderDetail.setStopTime(stopTime);
                    } else {
                        Optional<LocalTime> stopTimeOpt = PriceUtil.parseTimeString(
                            priceItemm.getEndTime());
                        if (stopTimeOpt.isPresent()) {
                            LocalTime temp = stopTimeOpt.get();
                            stopTime = loopStartTime.with(LocalTime.MIN).withHour(temp.getHour())
                                .withMinute(temp.getMinute()).toEpochSecond();
                        }
                        newOrderDetail.setStopTime(stopTime);
                    }
                    log.debug("[{} {}] stopTime = {}, newOrderDetail.getStartTime() = {}", tid,
                        evseNo, stopTime, newOrderDetail.getStartTime());
                    if (stopTime > newOrderDetail.getStartTime()) {
                        long durationSecond = stopTime - newOrderDetail.getStartTime();
                        BigDecimal newKwh = kwhPerSecond.multiply(
                            BigDecimal.valueOf(durationSecond));

                        newOrderDetail.setKwh(newKwh).setElecFee(
                            priceItemm.getElecPrice().multiply(newKwh)
                                .setScale(4, RoundingMode.HALF_UP)).setServFee(
                            priceItemm.getServPrice().multiply(newKwh)
                                .setScale(4, RoundingMode.HALF_UP));
                    }
                    log.debug("[{} {}] newOrderDetail = {}", tid, evseNo,
                        JsonUtils.toJsonString(newOrderDetail));

                    Optional.ofNullable(priceItemm.getCategory())
                        .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                    needPushOrderDetails.add(newOrderDetail);

                    loopStartMeter = loopStartMeter.add(newOrderDetail.getKwh());
                    loopStartTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(stopTime), zone);
                }

                stopMsg.setDetail(needPushOrderDetails);
                if (CollectionUtils.isNotEmpty(needPushOrderDetails)) {
                    BigDecimal totalElecFee = BigDecimal.ZERO;
                    BigDecimal totalServFee = BigDecimal.ZERO;
                    for (OrderDetail detail : needPushOrderDetails) {
                        if (detail.getElecFee() != null) {
                            totalElecFee = totalElecFee.add(detail.getElecFee());
                        }
                        if (detail.getServFee() != null) {
                            totalServFee = totalServFee.add(detail.getServFee());
                        }
                    }
                    stopMsg.setElecFee(totalElecFee);
                    stopMsg.setServFee(totalServFee);
                }
            }
            stopMsg.setPriceCode(evse.getPrice().getId());
        } else {
            log.error(
                "[{} {}] 补全OrderDetail和费用失败. startTime: {}, stopTime: {}, totalKwh: {}, priceItemList.size: {}",
                tid, evseNo, stopMsg.getStartTime(), stopMsg.getStopTime(), totalKwh,
                Optional.ofNullable(evse).map(GwEvseVo::getPrice).map(ChargePriceVo::getItemList)
                    .map(List::size).orElse(null));
        }
    }

    /**
     * 根据传入的时间段获取能完全覆盖该时间段的价格信息
     *
     * @param startTimeEpoch 开始时间（时间戳秒）
     * @param stopTimeEpoch  结束时间（时间戳秒）
     * @param priceVo        价格信息对象
     * @param timeZone       时区信息 +8 或 +02:00
     * @return 能完全覆盖时间段的价格信息，如果没有匹配到则返回null
     */
    private ChargePriceItem getPriceItemCoveringTimeRange(long startTimeEpoch, long stopTimeEpoch,
        ChargePriceVo priceVo, String timeZone) {
        if (startTimeEpoch <= 0 || stopTimeEpoch <= 0 || startTimeEpoch >= stopTimeEpoch
            || priceVo == null || CollectionUtils.isEmpty(priceVo.getItemList())) {
            return null;
        }

        // 使用桩的时区将时间戳转换为LocalTime
        ZoneOffset zoneOffset = OcppZonedDateTime.parseTimeZoneOrDefault(timeZone);
        ZonedDateTime startDateTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(startTimeEpoch),
            zoneOffset);
        ZonedDateTime stopDateTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(stopTimeEpoch),
            zoneOffset);

        LocalTime startTime = startDateTime.toLocalTime();
        LocalTime stopTime = stopDateTime.toLocalTime();

        // 遍历价格列表，查找能完全覆盖时间段的价格项
        for (ChargePriceItem price : priceVo.getItemList()) {
            Optional<LocalTime> priceStartTimeOpt = PriceUtil.parseTimeString(price.getStartTime());
            Optional<LocalTime> priceStopTimeOpt = PriceUtil.parseTimeString(price.getEndTime());
            if (priceStartTimeOpt.isEmpty() || priceStopTimeOpt.isEmpty()) {
                continue; // 跳过无法解析的时间
            }
            LocalTime priceStartTime = priceStartTimeOpt.get();
            LocalTime priceStopTime = priceStopTimeOpt.get();

            // 判断价格时间段是否能完全覆盖充电时间段
            if (PriceUtil.isPriceTimeRangeCoveringChargeTimeRange(startTime, stopTime, priceStartTime,
                priceStopTime)) {
                return price;
            }
        }
        return null;
    }

    /**
     * 订单结束时，判断actualStopTime是否在lastOrderDetail的范围内
     * <P>在范围内，则正常补全lastOrderDetail<P/>
     * <P>不在范围内，需要补全间隔的detail<P/>
     *
     * @param tid
     * @param evseNo
     * @param order
     * @param actualStopMeter
     * @param actualStopTime
     */
    public void complementLastOrderDetail(String tid, String evseNo, OrderData order,
        BigDecimal actualStopMeter, long actualStopTime) {
        log.debug("[{} {}] complementLastOrderDetail. actualStopMeter = {}, actualStopTime = {}",
            tid, evseNo, actualStopMeter, actualStopTime);
        OrderDetail latestOrderDetail = order.getLatestOrderDetail();
        GwEvseVo evse = evseRepository.getEvse(evseNo);
        if (latestOrderDetail == null || evse == null) {
            log.error("[{} {}] 补全OrderDetail和费用失败. latestOrderDetail: {}, evse: {}", tid,
                evseNo, latestOrderDetail, evse);
            return;
        }

        ZoneOffset zone = OcppZonedDateTime.parseTimeZoneOrDefault(evse.getTimeZone());
        ZonedDateTime actualStopDateTime = ZonedDateTime.ofInstant(
            Instant.ofEpochSecond(actualStopTime), zone);

        // 判断actualStopTime是否在lastOrderDetail的范围内
        boolean isInRange = PriceUtil.isTimeInRange(actualStopDateTime.toLocalTime(),
            latestOrderDetail.getStartTimeStr(), latestOrderDetail.getStopTimeStr());
        log.info("[{} {}] isInRange = {}", tid, evseNo, isInRange);
        if (isInRange) {
            // 在范围内，则正常补全lastOrderDetail
            if (NumberUtils.isZero(latestOrderDetail.getStopTime())) {
                latestOrderDetail.setStopTime(actualStopTime);
            }

            // 最后这个时段的电量可能会有误差，要重新计算
            BigDecimal currKwh = actualStopMeter.subtract(latestOrderDetail.getStartMeter());
            BigDecimal elecPrice = latestOrderDetail.getElecPrice().multiply(currKwh)
                .setScale(4, RoundingMode.HALF_UP);
            BigDecimal servPrice = latestOrderDetail.getServPrice().multiply(currKwh)
                .setScale(4, RoundingMode.HALF_UP);
            latestOrderDetail.setKwh(currKwh)
                .setElecFee(elecPrice)
                .setServFee(servPrice);

            log.debug("[{} {}] latestOrderDetail = {}", tid, evseNo,
                JsonUtils.toJsonString(latestOrderDetail));
            order.pushBillingDetail(latestOrderDetail);
        } else {
            // 如果不在范围内，则需补全间隔的detail，按秒平均分电量，计费每个时段所充的电量和费用
            BigDecimal subtractKwh = actualStopMeter.subtract(latestOrderDetail.getStartMeter());
            long subtractTime = actualStopTime - latestOrderDetail.getStartTime();
            BigDecimal kwhPerSecond = subtractKwh.divide(BigDecimal.valueOf(subtractTime), 6,
                RoundingMode.HALF_UP);
            log.debug("[{} {}] subtractKwh = {}, subtractTime = {}, kwhPerSecond = {}", tid, evseNo,
                subtractKwh, subtractTime, kwhPerSecond);

            ZonedDateTime lastStartDateTime = ZonedDateTime.ofInstant(
                Instant.ofEpochSecond(latestOrderDetail.getStartTime()), zone);
            BigDecimal lastStartMeter = Optional.ofNullable(latestOrderDetail.getStartMeter())
                .orElse(BigDecimal.ZERO);

            List<ChargePriceItem> sortedPriceItems = PriceUtil.sortPriceItems(
                evse.getPrice().getItemList());

            List<OrderDetail> needPushOrderDetails = new ArrayList<>();
            ZonedDateTime loopStartTime = lastStartDateTime;
            BigDecimal loopStartMeter = lastStartMeter;
            boolean isLastPriceItem = false;
            // 循环priceItem，从lastOrderDetail.startTime所在时段开始补全OrderDetail，到stopTime所在时段结束
            for (ChargePriceItem priceItemm : sortedPriceItems) {
                log.debug("[{} {}] loopStartTime = {}, loopStartMeter = {}, priceItem = {}", tid,
                    evseNo, loopStartTime, loopStartMeter, JsonUtils.toJsonString(priceItemm));
                if (!PriceUtil.isTimeInRange(loopStartTime.toLocalTime(), priceItemm.getStartTime(),
                    priceItemm.getEndTime())) {
                    continue;
                }
                if (isLastPriceItem) {
                    break;
                }

                isLastPriceItem = PriceUtil.isTimeInRange(actualStopDateTime.toLocalTime(),
                    priceItemm.getStartTime(), priceItemm.getEndTime());
                log.debug("[{} {}] isLastPriceItem = {}", tid, evseNo, isLastPriceItem);

                OrderDetail newOrderDetail = new OrderDetail();
                newOrderDetail.setStartTimeStr(priceItemm.getStartTime())
                    .setStopTimeStr(priceItemm.getEndTime())
                    .setElecPrice(priceItemm.getElecPrice())
                    .setServPrice(priceItemm.getServPrice())
                    .setStartMeter(loopStartMeter)
                    .setStartTime(loopStartTime.toEpochSecond());

                long tempStopTime = 0;
                if (isLastPriceItem) {
                    tempStopTime = actualStopTime;
                    newOrderDetail.setStopTime(tempStopTime);
                    order.setLatestOrderDetail(newOrderDetail);
                } else {
                    Optional<LocalTime> stopTimeOpt = PriceUtil.parseTimeString(
                        priceItemm.getEndTime());
                    if (stopTimeOpt.isPresent()) {
                        LocalTime temp = stopTimeOpt.get();
                        tempStopTime = loopStartTime.with(LocalTime.MIN).withHour(temp.getHour())
                            .withMinute(temp.getMinute()).toEpochSecond();
                    }
                    newOrderDetail.setStopTime(tempStopTime);
                }
                log.debug("[{} {}] tempStopTime = {}, newOrderDetail.getStartTime() = {}", tid,
                    evseNo, tempStopTime, newOrderDetail.getStartTime());
                if (tempStopTime > newOrderDetail.getStartTime()) {
                    long durationSecond = tempStopTime - newOrderDetail.getStartTime();
                    BigDecimal newKwh = kwhPerSecond.multiply(BigDecimal.valueOf(durationSecond));

                    newOrderDetail.setKwh(newKwh)
                        .setElecFee(priceItemm.getElecPrice().multiply(newKwh)
                            .setScale(4, RoundingMode.HALF_UP))
                        .setServFee(priceItemm.getServPrice().multiply(newKwh)
                            .setScale(4, RoundingMode.HALF_UP));
                }
                log.debug("[{} {}] newOrderDetail = {}", tid, evseNo,
                    JsonUtils.toJsonString(newOrderDetail));

                Optional.ofNullable(priceItemm.getCategory())
                    .ifPresent(e -> newOrderDetail.setPriceCode(e.getCode()));
                needPushOrderDetails.add(newOrderDetail);

                loopStartMeter = loopStartMeter.add(newOrderDetail.getKwh());
                loopStartTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(tempStopTime), zone);
            }

            log.debug("[{} {}] needPushOrderDetails.size = {}", tid, evseNo,
                needPushOrderDetails.size());
            needPushOrderDetails.forEach(order::pushBillingDetail);
        }

    }

}
