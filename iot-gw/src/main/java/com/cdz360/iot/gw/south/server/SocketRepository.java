package com.cdz360.iot.gw.south.server;

import com.cdz360.iot.gw.model.base.SocketInfo;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * WebSocket连接的管理类
 */
@Component
@Slf4j
public class SocketRepository {

    /**
     * Key: channelKey 例如："/11.22.33.44:12345"
     * Value: SocketInfo
     */
    private final Map<String, SocketInfo> socketCache = new ConcurrentHashMap<>();

    public SocketRepository put(String socketAddr, SocketInfo value) {
        socketCache.put(socketAddr, value);
        return this;
    }

    public SocketInfo get(String socketAddr) {
        return socketCache.get(socketAddr);
    }

    public void remove(String socketAddr) {
        this.socketCache.remove(socketAddr);
    }

    public int size() {
        return this.socketCache.size();
    }
}
