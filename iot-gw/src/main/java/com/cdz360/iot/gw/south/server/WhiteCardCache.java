package com.cdz360.iot.gw.south.server;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.config.IotConfigProperties;
import com.cdz360.iot.gw.north.model.WhiteCard;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WhiteCardCache {

    /**
     * TODO: 2025/4/15 WZFIX 白名单管理方案选型：
     * 单节点小规模部署 ：当前文件缓存方案足够，但如果预计会有以下情况，建议升级到数据库或Redis方案：
     * - 充电桩数量增长（>1000台）
     * - 需要历史记录查询功能
     * - 需要多服务实例共享缓存
     * - 对数据可靠性要求极高
     */
    private static final String CACHE_FILE_PATH = "whitelistCache.json";
    private static final int MAX_VERSIONS_PER_EVSE = 5; // 每个充电桩最多保留的版本数

    // 缓存结构：Map<evseNo, Map<listVersion, List<WhiteCard>>>
    private final ConcurrentHashMap<String, LinkedHashMap<Integer, List<WhiteCard>>> whiteCardCache = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final ObjectMapper objectMapper = new ObjectMapper(); // 新增ObjectMapper
    @Autowired
    private IotConfigProperties configProperties;
    private volatile boolean isChange = false;

    @PostConstruct
    public void init() {
        loadFromFile();
        // 每5分钟检查一次是否需要保存文件
        scheduler.scheduleWithFixedDelay(this::saveIfNeeded, 5, 5, TimeUnit.MINUTES);
    }

    @PreDestroy
    public void destroy() {
        // 程序关闭前确保数据被保存
        saveIfNeeded();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(1, TimeUnit.MINUTES)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    public void saveWhiteCard(String evseNo, Integer listVersion, List<WhiteCard> whiteCards) {
        whiteCardCache.compute(evseNo, (k, versionMap) -> {
            if (versionMap == null) {
                versionMap = new LinkedHashMap<>(MAX_VERSIONS_PER_EVSE, 0.75f, true) {
                    @Override
                    protected boolean removeEldestEntry(
                        Map.Entry<Integer, List<WhiteCard>> eldest) {
                        return size() > MAX_VERSIONS_PER_EVSE;
                    }
                };
            }
            versionMap.put(listVersion, whiteCards);
            return versionMap;
        });
        isChange = true;
    }

    private synchronized void saveIfNeeded() {
        if (isChange) {
            saveToFile();
            isChange = false;
        }
    }

    /**
     * 将缓存保存到文件
     */
    private synchronized void saveToFile() {
        log.info("开始将白名单缓存写入到文件");
        File cacheFile = new File(configProperties.getRtCfgPath(), CACHE_FILE_PATH);
        try (FileWriter fr = new FileWriter(cacheFile); BufferedWriter br = new BufferedWriter(
            fr)) {
            List<WhiteCardCacheJson> jsonList = new ArrayList<>();

            for (Map.Entry<String, LinkedHashMap<Integer, List<WhiteCard>>> entry : whiteCardCache.entrySet()) {
                String evseNo = entry.getKey();
                WhiteCardCacheJson json = new WhiteCardCacheJson();
                List<CacheEntry> entries = new ArrayList<>();

                for (Map.Entry<Integer, List<WhiteCard>> versionEntry : entry.getValue()
                    .entrySet()) {
                    CacheEntry cacheEntry = new CacheEntry(versionEntry.getKey(),
                        versionEntry.getValue());
                    entries.add(cacheEntry);
                }
                json.setEvseNo(evseNo);
                json.setCacheEntries(entries);
                jsonList.add(json);
            }

            fr.write(JsonUtils.toJsonString(jsonList));
            log.info("白名单缓存写入到文件完成");
        } catch (Exception e) {
            log.error("写入白名单缓存文件失败. filePath = {}", CACHE_FILE_PATH, e);
        }
    }

    /**
     * 从文件加载缓存
     */
    private void loadFromFile() {
        log.info("开始从缓存文件加载白名单信息");
        File cacheFile = new File(configProperties.getRtCfgPath(), CACHE_FILE_PATH);
        if (!cacheFile.exists()) {
            log.info("白名单缓存文件不存在，无需从文件加载");
            return;
        }

        try (FileReader fr = new FileReader(cacheFile); BufferedReader br = new BufferedReader(
            fr)) {
            StringBuilder buf = new StringBuilder();
            while (br.ready()) {
                buf.append(br.readLine());
            }
            if (buf.length() > 0) {
                List<WhiteCardCacheJson> jsonList = JsonUtils.fromJson(buf.toString(),
                    new TypeReference<List<WhiteCardCacheJson>>() {
                    });
                if (jsonList != null) {
                    for (WhiteCardCacheJson json : jsonList) {
                        if (json != null && json.getCacheEntries() != null) {
                            LinkedHashMap<Integer, List<WhiteCard>> versionMap = new LinkedHashMap<>(
                                MAX_VERSIONS_PER_EVSE, 0.75f, true) {
                                @Override
                                protected boolean removeEldestEntry(
                                    Map.Entry<Integer, List<WhiteCard>> eldest) {
                                    return size() > MAX_VERSIONS_PER_EVSE;
                                }
                            };

                            for (CacheEntry entry : json.getCacheEntries()) {
                                versionMap.put(entry.getListVersion(), entry.getWhiteCards());
                            }
                            whiteCardCache.put(json.getEvseNo(), versionMap);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("读取白名单缓存文件失败. filePath = {}", CACHE_FILE_PATH, e);
            // 加载失败时清空缓存，防止部分加载导致的数据不一致
            whiteCardCache.clear();
        }
        log.info("从缓存文件加载白名单信息完成，共 {} 个充电桩", whiteCardCache.size());
    }

    /**
     * 获取白名单卡列表
     */
    public List<WhiteCard> getWhiteCards(String evseNo, Integer listVersion) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null ? versionMap.get(listVersion) : null;
    }

    /**
     * 获取充电桩的最新白名单版本
     */
    public Integer getLatestVersion(String evseNo) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null ? versionMap.keySet().stream().max(Integer::compareTo)
            .orElse(null) : null;
    }

    /**
     * 检查充电桩是否存在指定版本的白名单
     */
    public boolean containsVersion(String evseNo, Integer listVersion) {
        Map<Integer, List<WhiteCard>> versionMap = whiteCardCache.get(evseNo);
        return versionMap != null && versionMap.containsKey(listVersion);
    }

    /**
     * 检查充电桩是否存在任何白名单
     */
    public boolean containsEvse(String evseNo) {
        return whiteCardCache.containsKey(evseNo);
    }

    @Data
    public static class WhiteCardCacheJson {

        private String evseNo;
        private List<CacheEntry> cacheEntries;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CacheEntry {

        private Integer listVersion;
        private List<WhiteCard> whiteCards;
    }

}