package com.cdz360.iot.gw.south.server.rcd;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.model.evse.protocol.ChargeStoppedUp;
import com.cdz360.iot.gw.model.evse.rcd.RcdBillReqDto;
import com.cdz360.iot.gw.model.evse.rcd.StopChargeReuploadEvent;
import com.cdz360.iot.gw.south.server.IotEvseProcessor;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RcdOrderProcessor {

    private static final String ORDER_RCD_STOP_MSG_PREFIX = "iot:gw:order:rcdStopMsg:";

    private static final long MESSAGE_EXPIRATION_TIME = 10; // seconds

    private static final long CACHE_CHECK_INTERVAL = 10; // seconds

    private final ConcurrentHashMap<String, ChargeStoppedUp> orderNoStopMsgCache = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @PostConstruct
    public void init() {
        String tid = RandomStringUtils.randomAlphabetic(8).toUpperCase();
        loadCacheFromRedis(tid);
        startCacheCleanupTask(tid);
    }

    @PreDestroy
    public void destroy() {
        scheduler.shutdownNow();
    }

    public void stashingStopTransMsg(String tid, ChargeStoppedUp stopMsg) {
        String orderNo = stopMsg.getOrderNo();

        // Store in local cache
        orderNoStopMsgCache.put(orderNo, stopMsg);

        // Store in Redis with expiration
        String key = ORDER_RCD_STOP_MSG_PREFIX + orderNo;
        redisTemplate.opsForValue()
            .set(key, JsonUtils.toJsonString(stopMsg), MESSAGE_EXPIRATION_TIME, TimeUnit.SECONDS);

        log.info("[{}] stashingStopTransMsg orderNo: {}", tid, orderNo);
    }

    /**
     * 根据 order中 BillReqDto 填充 stopMsg
     *
     * @param stopMsg
     * @param billDto
     */
    public void completeStopMsg(ChargeStoppedUp stopMsg, RcdBillReqDto billDto) {
        if (billDto == null) {
            return;
        }
        stopMsg.setAccount(billDto.getAccount());
        stopMsg.setStartTime(billDto.getStartTime().toInstant().getEpochSecond());
        stopMsg.setStopTime(billDto.getStopTime().toInstant().getEpochSecond());

        stopMsg.setOrderFee(billDto.getAmount());
        stopMsg.setElecFee(billDto.getEnergyAmount());
        stopMsg.setServFee(billDto.getServerAmount());

        stopMsg.setStartSoc(billDto.getStartSoc());
        stopMsg.setStopSoc(billDto.getStopSoc());

        Optional.ofNullable(billDto.getCompleteCode())
            .ifPresent(stopMsg::setCompleteCode);
        Optional.ofNullable(billDto.getErrorCode())
            .ifPresent(stopMsg::setErrorCode);

        if (billDto.getStartEnergy() != null && billDto.getStopEnergy() != null) {
            stopMsg.setStartMeter(billDto.getStartEnergy());
            stopMsg.setStopMeter(billDto.getStopEnergy());

            stopMsg.setKwh(billDto.getStopEnergy().subtract(billDto.getStartEnergy()));
        }
    }

    /**
     *
     * @param tid
     * @param orderNo
     * @param rcdBillReqDto
     * @return
     */
    public Optional<ChargeStoppedUp> processBillReqMsg(String tid, String orderNo,
        RcdBillReqDto rcdBillReqDto) {

        ChargeStoppedUp stopMsg = orderNoStopMsgCache.get(orderNo);
        if (stopMsg != null) {
            log.info("[{}] StopTransMsg存在 需补全stopMsg后上报云端. orderNo: {}", tid, orderNo);
            this.completeStopMsg(stopMsg, rcdBillReqDto);
            return Optional.of(stopMsg);
        } else {
            log.info("[{}] 不存在该订单的StopTransMsg 无需处理. orderNo: {}", tid, orderNo);
        }
        return Optional.empty();
    }

    private void loadCacheFromRedis(String tid) {
        // Load messages from Redis to local cache on startup
        redisTemplate.keys(ORDER_RCD_STOP_MSG_PREFIX + "*").forEach(redisKey -> {
            String stopMsgStr = redisTemplate.opsForValue().get(redisKey);
            ChargeStoppedUp stopMsg = JsonUtils.fromJson(stopMsgStr, ChargeStoppedUp.class);
            String orderNo = redisKey.substring(ORDER_RCD_STOP_MSG_PREFIX.length());
            orderNoStopMsgCache.put(orderNo, stopMsg);
            log.info("[{}] loadCacheFromRedis orderNo: {} ", tid, orderNo);
        });
    }

    private void startCacheCleanupTask(String tid) {
        // 安排任务定期检查和清理本地缓存中的过期消息
        scheduler.scheduleAtFixedRate(() -> {
            orderNoStopMsgCache.forEach((orderNo, stopMsg) -> {
                String redisKey = ORDER_RCD_STOP_MSG_PREFIX + orderNo;
                if (redisTemplate.opsForValue().get(redisKey) == null) {
                    log.info(
                        "[{}] Removed expired message from orderNoStopMsgCache orderNo: {}, stopMsg: {}",
                        tid, orderNo, stopMsg);
                    /**
                     * 对应事件监听器为 {@link IotEvseProcessor#handleStopChargeEvent(StopChargeReuploadEvent)}
                     */
                    eventPublisher.publishEvent(new StopChargeReuploadEvent(tid, stopMsg));
                    orderNoStopMsgCache.remove(orderNo);
                }
            });
        }, 0, CACHE_CHECK_INTERVAL, TimeUnit.SECONDS);
    }

}