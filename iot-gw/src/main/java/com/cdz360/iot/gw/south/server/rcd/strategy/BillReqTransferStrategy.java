package com.cdz360.iot.gw.south.server.rcd.strategy;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdBillConfVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdBillReqDto;
import com.cdz360.iot.gw.model.type.OrderCompleteCode;
import com.cdz360.iot.gw.model.type.RcdStopReason;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import jakarta.annotation.PostConstruct;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BillReqTransferStrategy implements IDataTransferStrategy {

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private EvseRepository evseRepository;

    @PostConstruct
    public void init() {
        strategyFactory.addStrategy(DataTransferMessage.BILL_REQ, this);
    }

    @Override
    public DataTransferConfirmation dataProcessing(DataTransferRequest request) {
        EvseMsgBase base = request.getBase();
        String tid = base.getTid();
        String evseNo = base.getEvseNo();
        log.info("[{} {}] {} dataProcessing. vendorId: {}", tid, evseNo, request.getMessageId(),
            request.getVendorId());

        DataTransferConfirmation confirmation = new DataTransferConfirmation(
            DataTransferStatus.Accepted);
        confirmation.setBase(base);

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        RcdBillReqDto dto = JsonUtils.fromJson(request.getData(), RcdBillReqDto.class);
        log.info("[{} {}] 订单结束上报请求。request: {}", tid, evseNo, dto);
        if (dto.getStartTime() != null) {
            dto.setStartTime(OcppZonedDateTime.fromRcdUtcTime(
                dto.getStartTime(), evse.getTimeZone()));
        }
        if (dto.getStopTime() != null) {
            dto.setStopTime(OcppZonedDateTime.fromRcdUtcTime(
                dto.getStopTime(), evse.getTimeZone()));
        }

        Optional.ofNullable(RcdStopReason.valueOfMsg(dto.getReason())).ifPresent(x -> {
            if (RcdStopReason.abnormalReason(x)) {
                dto.setCompleteCode(OrderCompleteCode.ABNORMAL_STOP.getCode());
                dto.setErrorCode(x.getCode());
            }
        });

        log.info("[{} {}] 订单结束上报请求。request: {}", tid, evseNo, dto);

        if (NumberUtils.isZero(base.getPlugIdx()) && NumberUtils.gtZero(dto.getConnector())) {
            base.setPlugIdx(dto.getConnector());
        }

        Integer transId = dto.getSerial();
        OrderData oldOrder = evseOrderRepository.getOrder(transId);
        if (oldOrder == null) {
            log.error("[{}] 订单不存在 transId: {}, connector: {}", tid, transId,
                dto.getConnector());
            confirmation.setStatus(DataTransferStatus.Rejected);
            return confirmation;
        }

        OrderData orderUpdate = new OrderData(oldOrder.getOrderNo());
        orderUpdate.setRcdBillReqDto(dto);
        evseOrderRepository.updateOrder(orderUpdate);

        RcdBillConfVo confVo = new RcdBillConfVo();
        confVo.setMessageId(DataTransferMessage.BILL_CONF.getStr());
        confVo.setResult(IotGwConstants.SUCCESS);
        confVo.setSerial(dto.getSerial());
        confirmation.setData(JsonUtils.toJsonString(confVo));

        return confirmation;
    }

}
