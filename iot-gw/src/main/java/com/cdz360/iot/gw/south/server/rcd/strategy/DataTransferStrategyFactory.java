package com.cdz360.iot.gw.south.server.rcd.strategy;

import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataTransferStrategyFactory {

    private Map<DataTransferMessage, IDataTransferStrategy> strategyMap = new ConcurrentHashMap<>();

    protected void addStrategy(DataTransferMessage proDefKey, IDataTransferStrategy strategy) {
        this.strategyMap.put(proDefKey, strategy);
    }

    public IDataTransferStrategy getStrategy(DataTransferMessage proDefKey) {
        return this.strategyMap.get(proDefKey);
    }

}
