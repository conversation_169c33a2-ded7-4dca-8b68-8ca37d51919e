package com.cdz360.iot.gw.south.server.rcd.strategy;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.evse.rcd.RcdBaseConfVo;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RateConfTransferStrategy implements IDataTransferStrategy {

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @PostConstruct
    public void init() {
        strategyFactory.addStrategy(DataTransferMessage.BILL_CONF, this);
    }

    @Override
    public DataTransferConfirmation dataProcessing(DataTransferRequest request) {
        String tid = request.getBase().getTid();
        log.info("[{}] {} dataProcessing. vendorId: {}", tid, request.getMessageId(),
            request.getVendorId());

        DataTransferConfirmation confirmation = new DataTransferConfirmation(
            DataTransferStatus.Accepted);
        confirmation.setBase(request.getBase());

        RcdBaseConfVo confVo = new RcdBaseConfVo();
        confVo.setResult(IotGwConstants.SUCCESS);
        confVo.setMessageId(DataTransferMessage.STATUS_AC_CONF.getStr());
        confirmation.setData(JsonUtils.toJsonString(confVo));

        return confirmation;
    }

}
