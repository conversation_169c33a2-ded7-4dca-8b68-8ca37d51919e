package com.cdz360.iot.gw.south.server.rcd.strategy;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.evse.rcd.RcdBaseConfVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdStartAuthReqVo;
import com.cdz360.iot.gw.model.type.EvseBrand;
import com.cdz360.iot.gw.south.server.JsonServerImpl;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StartAuthReqTransferStrategy implements IDataTransferStrategy {

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseRepository evseRepository;

    @Autowired
    private JsonServerImpl jsonServer;

    @PostConstruct
    public void init() {
        strategyFactory.addStrategy(DataTransferMessage.START_TRANSACTION_AUTH_REQ, this);
    }

    @Override
    public DataTransferConfirmation dataProcessing(DataTransferRequest request) {
        String tid = request.getBase().getTid();
        log.info("[{}] {} dataProcessing. vendorId: {}", tid, request.getMessageId(),
            request.getVendorId());

        DataTransferConfirmation confirmation = new DataTransferConfirmation(
            DataTransferStatus.Accepted);
        confirmation.setBase(request.getBase());

        RcdStartAuthReqVo reqVo = JsonUtils.fromJson(request.getData(), RcdStartAuthReqVo.class);
        log.info("[{}] dataProcessing. rcdStartAuthReqVo: {}", tid, reqVo);


        RcdBaseConfVo confVo = new RcdBaseConfVo();
        confVo.setResult(IotGwConstants.SUCCESS);
        confVo.setMessageId(DataTransferMessage.START_TRANSACTION_AUTH_CONF.getStr());
        confirmation.setData(JsonUtils.toJsonString(confVo));

        if (EvseBrand.RCD.getVendorId().equals(request.getVendorId())) {
            // TODO: 2025/3/13 WZFIX 下发 SetChargingProfile
        }

        return confirmation;
    }

}
