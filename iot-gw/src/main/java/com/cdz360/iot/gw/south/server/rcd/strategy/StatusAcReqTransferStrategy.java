package com.cdz360.iot.gw.south.server.rcd.strategy;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.EvseOrderRepository;
import com.cdz360.iot.gw.biz.ds.EvseRepository;
import com.cdz360.iot.gw.config.IotGwConstants;
import com.cdz360.iot.gw.model.GwEvseVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdBaseConfVo;
import com.cdz360.iot.gw.model.evse.rcd.RcdStatusAcReqDto;
import com.cdz360.iot.gw.north.model.OrderData;
import com.cdz360.iot.gw.north.model.OrderData.LatestOrderFee;
import com.cdz360.iot.gw.util.OcppZonedDateTime;
import eu.chargetime.ocpp.model.core.DataTransferConfirmation;
import eu.chargetime.ocpp.model.core.DataTransferRequest;
import eu.chargetime.ocpp.model.core.DataTransferStatus;
import eu.chargetime.ocpp.model.dc.type.DataTransferMessage;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StatusAcReqTransferStrategy implements IDataTransferStrategy {

    @Autowired
    private DataTransferStrategyFactory strategyFactory;

    @Autowired
    private EvseOrderRepository evseOrderRepository;

    @Autowired
    private EvseRepository evseRepository;

    @PostConstruct
    public void init() {
        strategyFactory.addStrategy(DataTransferMessage.STATUS_AC_REQ, this);
    }

    @Override
    public DataTransferConfirmation dataProcessing(DataTransferRequest request) {
        String tid = request.getBase().getTid();
        String evseNo = request.getBase().getEvseNo();
        log.info("[{}] {} dataProcessing. vendorId: {}", tid, request.getMessageId(),
            request.getVendorId());

        DataTransferConfirmation confirmation = new DataTransferConfirmation(
            DataTransferStatus.Accepted);
        confirmation.setBase(request.getBase());

        GwEvseVo evse = evseRepository.getEvse(evseNo);
        RcdStatusAcReqDto dto = JsonUtils.fromJson(request.getData(), RcdStatusAcReqDto.class);
        log.info("[{} {}] 桩上报充电中数据。request: {}", tid, evseNo, dto);
        if (dto.getCurrentTime() != null) {
            dto.setCurrentTime(
                OcppZonedDateTime.fromRcdUtcTime(dto.getCurrentTime(), evse.getTimeZone()));
        }
        log.info("[{} {}] 桩上报充电中数据。request: {}", tid, evseNo, dto);
        Integer transId = dto.getTransactionId();
        OrderData oldOrder = evseOrderRepository.getOrder(transId);
        if (oldOrder == null) {
            log.error("[{}] 订单不存在 transId: {}, connector: {}", tid, transId,
                dto.getConnector());
            confirmation.setStatus(DataTransferStatus.Rejected);
            return confirmation;
        }
        boolean needUpdate = false;

        OrderData orderUpdate = new OrderData(oldOrder.getOrderNo());
        LatestOrderFee orderFee = new LatestOrderFee();
        if (dto.getChargeTime() != null) {
            orderFee.setChargeTime(dto.getChargeTime());
        }
        // orderFee.setChargeTime(dto.getAVoltage()); // TODO: 2025/3/10 WZFIX 询问aVoltage是什么含义（区分交直流）
        // orderFee.setChargeTime(dto.getAVoltage()); // TODO: 2025/3/10 WZFIX 询问aCurrent是什么含义（区分交直流）

        if (dto.getAmount() != null) {
            orderFee.setOrderAmount(DecimalUtils.divide100(dto.getAmount()));
            needUpdate = true;
        }
        if (dto.getEnergyAmount() != null) {
            orderFee.setElecAmount(DecimalUtils.divide100(dto.getEnergyAmount()));
            needUpdate = true;
        }
        if (dto.getServerAmount() != null) {
            orderFee.setServAmount(DecimalUtils.divide100(dto.getServerAmount()));
            needUpdate = true;
        }

        if (needUpdate) {
            orderUpdate.setLatestOrderFee(orderFee);
            evseOrderRepository.updateOrder(orderUpdate);
        }

        RcdBaseConfVo confVo = new RcdBaseConfVo();
        confVo.setResult(IotGwConstants.SUCCESS);
        confVo.setMessageId(DataTransferMessage.STATUS_AC_CONF.getStr());
        confirmation.setData(JsonUtils.toJsonString(confVo));

        return confirmation;
    }

}
