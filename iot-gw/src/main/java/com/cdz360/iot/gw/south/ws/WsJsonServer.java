package com.cdz360.iot.gw.south.ws;


import com.cdz360.iot.gw.config.ws.ApplicationConfiguration;
import eu.chargetime.ocpp.JSONServer;
import eu.chargetime.ocpp.ServerEvents;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class WsJsonServer {

    private final ServerEvents serverEvents;
    private final JSONServer server;
    private final ApplicationConfiguration applicationConfiguration;

    public void startServer() {
        Integer port = applicationConfiguration.getWsPort();
        log.info("Starting server on port {}", port);
        server.open("0.0.0.0", port, serverEvents);
    }

}
