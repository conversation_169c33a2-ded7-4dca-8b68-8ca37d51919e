package com.cdz360.iot.gw.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DBUtil {

    private static final Logger logger = LoggerFactory.getLogger(DBUtil.class);
    private static Connection connection = null;
    private static Statement statement = null;

    /**
     * @describe: 设置连接 * @params: * @Author: Kanyun * @Date: 2018/7/12 9:54
     */
    public static void setConnection(String url) {
        try {
            connection = DriverManager.getConnection(url);
//            声明
            statement = connection.createStatement();
        } catch (Exception e) {
            throw new RuntimeException("建立Sqlite连接失败");
        }


    }

    /**
     * @describe: 关闭链接
     * @params:
     * @Author: Kanyun
     * @Date: 2018/7/12 10:11
     */
    public static void endConnection() {
        try {
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void excuteByScriptExecuteor(String scriptPath) {
        try{
            SqlFileExecutor.execute(connection, scriptPath);

        } catch (Exception e){
            e.printStackTrace();
        }
    }

}