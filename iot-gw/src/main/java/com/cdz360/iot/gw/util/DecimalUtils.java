package com.cdz360.iot.gw.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DecimalUtils {
    /**
     * 2位小数的分转化为元
     *
     * @param fen
     * @return
     */
    public static BigDecimal fen2Yuan(long fen) {
        return DecimalUtils.divide100(fen);
    }

    public static BigDecimal divide10(Integer inVal) {
        return DecimalUtils.divide10(inVal, null);
    }

    public static BigDecimal divide10(Integer inVal, Integer defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.divide(BigDecimal.TEN, 1, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide100(Integer inVal) {
        return DecimalUtils.divide100(inVal, null);
    }

    public static BigDecimal divide100(Integer inVal, Integer defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide100(Long inVal) {
        return DecimalUtils.divide100(inVal, null);
    }

    public static BigDecimal divide100(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide100(BigDecimal inVal) {
        return inVal.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide10000(Long inVal) {
        return DecimalUtils.divide10000(inVal, null);
    }

    public static BigDecimal divide10000(Long inVal, Long defaultValue) {
        BigDecimal inDecimal;
        if (inVal == null && defaultValue == null) {
            return null;
        } else if (inVal == null) {
            inDecimal = new BigDecimal(defaultValue);
        } else {
            inDecimal = new BigDecimal(inVal);
        }
        return inDecimal.divide(new BigDecimal(10000), 4, RoundingMode.HALF_UP);
    }

    public static void multiply100ToInt(byte[] data, BigDecimal src, int offset) {
        int a = src.multiply(new BigDecimal(100)).intValue();
        IntUtil.transfer(data, a, offset);
    }

    public static long multiply100(BigDecimal src) {
        return src.multiply(new BigDecimal(100)).longValue();
    }

    public static long multiply10000(BigDecimal src) {
        return src.multiply(new BigDecimal(10000)).longValue();
    }



    public static boolean isZero(BigDecimal val) {
        return BigDecimal.ZERO.compareTo(val) == 0;
    }
    /**
     *
     * @param left left value
     * @param right right value
     * @return true if left is less than right
     */
    public static boolean lt(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) < 0;
    }

    public static boolean lte(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) < 1;
    }

    /**
     * 判断 val 是否 < 0
     * @param val 参数
     * @return val < 0 返回 true; 否则返回 false
     */
    public static boolean ltZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 判断 val 是否 <= 0
     * @param val 参数
     * @return val <= 0 返回 true; 否则返回 false
     */
    public static boolean lteZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) < 1;
    }

    public static boolean gt(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) > 0;
    }
    public static boolean gte(BigDecimal left, BigDecimal right) {
        return left.compareTo(right) > -1;
    }

    /**
     * 判断 val 是否 > 0
     * @param val 参数
     * @return val > 0 返回 true; 否则返回 false
     */
    public static boolean gtZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断 val 是否 >= 0
     * @param val 参数
     * @return val >= 0 返回 true; 否则返回 false
     */
    public static boolean gteZero(BigDecimal val) {
        return val.compareTo(BigDecimal.ZERO) > -1;
    }


    public static BigDecimal min(BigDecimal left, BigDecimal right) {
        if(DecimalUtils.gt(left, right)) {
            return right;
        }
        else {
            return left;
        }
    }
}
