package com.cdz360.iot.gw.util;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @Description //文件处理
 * @Date 2019/3/11
 **/
public class FileUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    private static final String MODE = "rw";

    /**
     * 读取文件前几行
     *
     * @param fileName
     * @param lineNum
     * @return
     */
    public static List<String> readFirstLines(String fileName, int lineNum) {
        List<String> strList = new ArrayList<String>();
        RandomAccessFile raf = null;
        try {
            raf = new RandomAccessFile(fileName, MODE);
            //Initial write position
            long writePosition = raf.getFilePointer();
            for (int i = 0; i < lineNum; i++) {
                String line = raf.readLine();
                if (line == null) {
                    break;
                }
                // byte[] bytes = line.getBytes("utf-8");
                // byte[] bytes = line.getBytes();
                // strList.add(line);
                strList.add(new String(line.getBytes("8859_1"), StandardCharsets.UTF_8));

            }

        } catch (IOException e) {
            logger.error("readFirstLines error", e);
            //throw e;
        } finally {
            try {
                if (raf != null) {
                    raf.close();
                }
            } catch (IOException e) {
                logger.error("close RandomAccessFile error", e);
                //throw e;
            }
        }

        return strList;
    }

    /**
     * 读取前几行信息，并且在文件中删除它们
     *
     * @param fileName
     * @param lineNum
     * @return
     */
    public static List<String> readAndRemoveFirstLines(String fileName, int lineNum) {
        List<String> strList = new ArrayList<String>();
        RandomAccessFile raf = null;
        try {
            raf = new RandomAccessFile(fileName, MODE);
            //Initial write position
            long writePosition = raf.getFilePointer();
            for (int i = 0; i < lineNum; i++) {
                String line = raf.readLine();
                if (line == null) {
                    break;
                }
                // byte[] bytes = line.getBytes("utf-8");
                // byte[] bytes = line.getBytes();
                // strList.add(line);
                strList.add(new String(line.getBytes("8859_1"), "utf-8"));

            }
            // Shift the next lines upwards.
            long readPosition = raf.getFilePointer();

            //long tttt = readPosition;

            byte[] buff = new byte[1024];
            int n;
            while (-1 != (n = raf.read(buff))) {
                raf.seek(writePosition);
                raf.write(buff, 0, n);
                readPosition += n;
                writePosition += n;
                raf.seek(readPosition);
            }
            raf.setLength(writePosition);
            //raf.setLength(writePosition + tttt);
        } catch (IOException e) {
            logger.error("readAndRemoveFirstLines error", e);
            //throw e;
        } finally {
            try {
                if (raf != null) {
                    raf.close();
                }
            } catch (IOException e) {
                logger.error("close RandomAccessFile error", e);
                //throw e;
            }
        }

        return strList;
    }


    /**
     * @param skip     跳过多少过字节进行插入数据,完成后换行
     * @param str      要插入的字符串
     * @param fileName 文件路径
     */
    private static void saveStrSkip(long skip, String str, String fileName) {
        try {
            //str = str + "\r\n";//换行
            RandomAccessFile raf = new RandomAccessFile(fileName, MODE);
            if (skip < 0 || skip > raf.length()) {
                logger.error("跳过字节数无效");
                return;
            }
            byte[] b = str.getBytes(StandardCharsets.UTF_8);
            //byte[] b = str.getBytes();
            raf.setLength(raf.length() + b.length);
            for (long i = raf.length() - 1; i > b.length + skip - 1; i--) {
                raf.seek(i - b.length);
                byte temp = raf.readByte();
                raf.seek(i);
                raf.writeByte(temp);
            }
            raf.seek(skip);
            raf.write(b);
            raf.close();
        } catch (Exception e) {
            logger.error("saveStrSkipAndNewline error", e);
        }
    }

    /**
     * 从尾部插入多行数据,注意换行
     *
     * @param strList
     * @param fileName
     */
    public static void saveLines(List<String> strList, String fileName) {
        File file = new File(fileName);
        StringBuffer sb = new StringBuffer();
        if (!CollectionUtils.isEmpty(strList)) {
            for (String s : strList) {
                sb.append(s + "\r\n");//换行
            }
            saveStrSkip(file.length(), sb.toString(), fileName);
        }

    }

    /**
     * 获取文件大小 字节
     *
     * @param file
     * @return
     */
    public static long getFileSize(File file) {
        long fileSize = 0;
        if (file.exists() && file.isFile()) {
            String fileName = file.getName();
            logger.info("file fileSize {}", file.length());
            fileSize = file.length();
        }
        return fileSize;

    }

    /**
     * 取出文件夹下的所有文件txt文件，排序
     *
     * @param strPath
     * @return
     */
    public static List<File> getFileListSort(String strPath) {
        File dir = new File(strPath);
        File[] files = dir.listFiles(); // 该文件目录下文件全部放入数组
        List<File> filelist = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                String fileName = file.getName();
                if (fileName.endsWith("txt")) { // 判断文件名是否以.txt结尾
                    // String strFileName = files[i].getAbsolutePath();
                    // logger.info("---" + strFileName);
                    filelist.add(file);
                }
            }
        }
        //排序
        if (filelist.size() > 0) {
            filelist.sort(new Comparator<File>() {
                public int compare(File o1, File o2) {
                    if (o1.isDirectory() && o2.isFile())
                        return -1;
                    if (o1.isFile() && o2.isDirectory())
                        return 1;
                    return o1.getName().compareTo(o2.getName());
                }
            });
        }

        return filelist;
    }

    /**
     * 删除单个文件
     *
     * @param fileName 要删除的文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                logger.info("删除单个文件{}成功！", fileName);
                return true;
            } else {
                logger.info("删除单个文件{}失败！", fileName);
                return false;
            }
        } else {
            logger.info("删除单个文件失败：{}不存在！", fileName);
            return false;
        }
    }


    public static void main(String[] args) {
        //File file = new File("D:\\test.txt");
        //String fileName = "D:\\test.txt";
        String fileName = "cache\\test.txt";
        File file = new File(fileName);
        try {
            // saveStrAndNewline("插入字符。22", fileName);
            List<String> strings1 = readFirstLines(fileName, 2);
            List<String> strings = readAndRemoveFirstLines(fileName, 2);
            getFileSize(file);
            String x = "";
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
