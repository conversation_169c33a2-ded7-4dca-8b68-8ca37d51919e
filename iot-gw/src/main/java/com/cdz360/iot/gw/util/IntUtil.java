package com.cdz360.iot.gw.util;

public class IntUtil {

    /**
     * 将int转为低字节在前，高字节在后的byte数组
     */
    public static byte[] toLH(int n) {
        byte[] b = new byte[4];
        b[0] = (byte) (n & 0xff);
        b[1] = (byte) (n >> 8 & 0xff);
        b[2] = (byte) (n >> 16 & 0xff);
        b[3] = (byte) (n >> 24 & 0xff);
        return b;
    }

    /**
     * 将int转为高字节在前，低字节在后的byte数组
     *
     * @param n int
     * @return byte[]
     */
    public static byte[] toHH(int n) {
        byte[] b = new byte[4];
        b[3] = (byte) (n & 0xff);
        b[2] = (byte) (n >> 8 & 0xff);
        b[1] = (byte) (n >> 16 & 0xff);
        b[0] = (byte) (n >> 24 & 0xff);
        return b;
    }

    public static void transfer(byte[] data, int src, int offset) {
        int idx = offset;
        for (int i = idx; i < offset + 4; i++) {
            data[i] = (byte) (src & 0xFF);
            src >>= 8;
        }
    }

    public static void transfer(byte[] data, Long src, int offset) {
        int idx = offset;
        if(src == null || src.longValue() > Integer.MAX_VALUE) {
            for (int i = idx; i < offset + 4; i++) {
                data[i] = (byte) (0xFF);
            }
        }
        else {
            int intV = src.intValue();
            for (int i = idx; i < offset + 4; i++) {
                data[i] = (byte) (intV & 0xFF);
                intV >>= 8;
            }
        }
    }

    public static void transferShort(byte[] data, Integer src, int offset) {
        int idx = offset;
        if(src == null || src.longValue() > Short.MAX_VALUE) {
            for (int i = idx; i < offset + 2; i++) {
                data[i] = (byte) (0xFF);
            }
        }
        else {
            short intV = src.shortValue();
            for (int i = idx; i < offset + 2; i++) {
                data[i] = (byte) (intV & 0xFF);
                intV >>= 8;
            }
        }
    }
}
