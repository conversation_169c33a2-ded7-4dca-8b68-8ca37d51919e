package com.cdz360.iot.gw.util;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.iot.gw.model.type.EvseBrand;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OcppZonedDateTime {

    /**
     * 是否需要对RCD桩进行特殊适配
     */
    private static boolean rcdTimeConvert = true;

    @Value("${cdz360.rcd.timeConvert:true}")
    public void setRcdTimeConvert(boolean convert) {
        rcdTimeConvert = convert;
    }

    /**
     * 解析时区，如果为空则使用系统默认时区
     *
     * @param timeZone
     * @return
     */
    public static ZoneOffset parseTimeZoneOrDefault(String timeZone) {
        return StringUtils.isNotBlank(timeZone) ? ZoneOffset.of(timeZone)
            : ZoneId.systemDefault().getRules().getOffset(Instant.now());
    }

    /**
     * 根据桩时区生成时间
     *
     * @param timeZone 时区，例如：+8 或 +02:00
     * @return
     */
    public static ZonedDateTime nowTz(String timeZone) {
        return ZonedDateTime.now(ZoneOffset.of(timeZone));
    }

    /**
     * 下发时间给RCD桩时使用此方法
     * <p> RCD桩特殊适配：保持本地时间数值不变，只将时区转换为UTC </p>
     * <p> 例如：北京时间2023-01-01 12:00:00 +08:00 会被转换为 2023-01-01 12:00:00 Z (UTC) </p>
     *
     * @param timeZone        时区，例如：+8 或 +02:00
     * @param defaultTimeZone 默认时区，系统默认（例如：Asia/Shanghai）
     * @return 转换后的时间
     */
    private static ZonedDateTime rcdZonedDateTime(String timeZone, String defaultTimeZone) {
        ZonedDateTime nowByTz;
        if (StringUtils.isNotBlank(timeZone)) {
            nowByTz = ZonedDateTime.now(ZoneOffset.of(timeZone));
        } else {
            nowByTz = ZonedDateTime.now(ZoneId.of(defaultTimeZone));
        }
        if (rcdTimeConvert) {
            // RCD适配：保持本地时间数值，只将时区转换为UTC
            return nowByTz.withZoneSameLocal(ZoneOffset.UTC);
        } else {
            return nowByTz;
        }
    }

    /**
     * 下发时间给RCD桩时使用此方法（使用系统默认时区）
     *
     * @param timeZone 时区，例如：+8 或 +02:00
     * @return 转换后的时间
     */
    private static ZonedDateTime rcdZonedDateTime(String timeZone) {
        return rcdZonedDateTime(timeZone, ZoneId.systemDefault().getId());
    }

    /**
     * 根据桩品牌获取要下发的时间
     * <p>
     * 如果是RCD桩，会进行特殊处理
     * </p>
     *
     * @param evseBrand 桩品牌
     * @param timeZone  时区，例如：+8 或 +02:00
     * @return 当前时间
     */
    public static ZonedDateTime issueTime(EvseBrand evseBrand, String timeZone) {
        ZonedDateTime nowByTz = ZonedDateTime.now(ZoneOffset.of(timeZone));
        if (EvseBrand.RCD.equals(evseBrand)) {
            nowByTz = rcdZonedDateTime(timeZone);
        }
        return nowByTz;
    }

    /**
     * 处理桩上报的时间
     *
     * @param utcTime  桩上报的UTC时间
     * @param timeZone 实际时区，例如：+8 或 +02:00
     * @return 转换后的实际时间
     */
    public static ZonedDateTime fromUtcTime(ZonedDateTime utcTime, EvseBrand brand,
        String timeZone) {
        if (utcTime == null) {
            return utcTime;
        }

        if (EvseBrand.RCD.equals(brand)) {
            return fromRcdUtcTime(utcTime, timeZone);
        }

        // 将UTC时间转换为实际时区
        ZoneOffset zoneOffset = parseTimeZoneOrDefault(timeZone);
        return utcTime.withZoneSameInstant(zoneOffset);
    }

    /**
     * 转换为给桩下发的时间
     *
     * @param date     要下发的时间
     * @param brand    桩品牌
     * @param timeZone 实际时区，例如：+8 或 +02:00
     * @return 转换后的给桩下发的时间
     */
    public static ZonedDateTime toUtcTime(Date date, EvseBrand brand, String timeZone) {
        if (date == null) {
            return null;
        }
        ZoneOffset zoneOffset = parseTimeZoneOrDefault(timeZone);
        ZonedDateTime realTime = date.toInstant().atZone(zoneOffset);
        if (EvseBrand.RCD.equals(brand) && rcdTimeConvert) {
            // 日期和时间保持不变，只改变时区为UTC
            return realTime.withZoneSameLocal(ZoneOffset.UTC);
        }

        // 将实际时间转换为UTC时间
        return realTime.withZoneSameInstant(ZoneOffset.UTC);
    }

    /**
     * 处理RCD桩上报的时间
     * <p> RCD桩上报的时间是UTC时区，但时间值是本地时间，需要转换回实际时区 </p>
     * <p> 例如：桩上报 2023-01-01 12:00:00 Z (UTC)，实际应该是 2023-01-01 12:00:00 +08:00 (北京时间) </p>
     *
     * @param utcTime  桩上报的UTC时间
     * @param timeZone 实际时区，例如：+8 或 +02:00
     * @return 转换后的实际时间
     */
    private static ZonedDateTime fromRcdUtcTime(ZonedDateTime utcTime, String timeZone) {
        if (utcTime == null || !rcdTimeConvert) {
            return utcTime;
        }

        // 将UTC时间转换为实际时区，保持时间值不变
        ZoneOffset zoneOffset = parseTimeZoneOrDefault(timeZone);
        return utcTime.withZoneSameLocal(zoneOffset);
    }

    /**
     * 处理RCD桩上报的时间
     * <p> RCD桩上报的时间是UTC时区，但时间值是本地时间，需要转换回实际时区 </p>
     * <p> 例如：桩上报 2023-01-01 12:00:00 Z (UTC)，实际应该是 2023-01-01 12:00:00 +08:00 (北京时间) </p>
     *
     * @param utcTime  桩上报的UTC时间 (Date类型)
     * @param timeZone 实际时区，例如：+8 或 +02:00
     * @return 转换后的实际时间
     */
    public static Date fromRcdUtcTime(Date utcTime, String timeZone) {
        if (utcTime == null || !rcdTimeConvert) {
            return utcTime;
        }

        // 将 Date 转换为 ZonedDateTime (UTC)
        ZonedDateTime zdtUtc = utcTime.toInstant().atZone(ZoneOffset.UTC);

        // 将UTC时间转换为实际时区，保持时间值不变
        ZoneOffset zoneOffset = parseTimeZoneOrDefault(timeZone);

        ZonedDateTime zonedDateTime = zdtUtc.withZoneSameLocal(zoneOffset);
        return Date.from(zonedDateTime.toInstant());
    }

}
