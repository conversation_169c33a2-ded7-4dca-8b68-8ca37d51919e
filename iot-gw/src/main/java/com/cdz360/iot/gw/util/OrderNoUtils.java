package com.cdz360.iot.gw.util;

public class OrderNoUtils {

    /**
     * 使用卡号 + 桩编号的最后2位 + 启动充电时间作为订单号
     */
    public static String genOfflineOrderNo(String evseNo, String account, long startTime) {
        int evseNoLen = evseNo.length();
        return account + evseNo.substring(evseNoLen - 2) + ByteUtil.hexDump(
            ByteUtil.longToByte(startTime));
    }

    /**
     * 异常情况时，生成订单号，避免丢单
     */
    public static String genOrderNo4Abnormal(String evseNo) {
        long time = System.currentTimeMillis() / 1000;
        int evseNoLen = evseNo.length();
        return evseNo.substring(evseNoLen - 2) + ByteUtil.hexDump(
            ByteUtil.longToByte(time));
    }

}
