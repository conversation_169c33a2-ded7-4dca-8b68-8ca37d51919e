package com.cdz360.iot.gw.util;

import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;

public class PriceUtil {


    /**
     * 解析时间字符串，特殊处理"24:00"的情况
     *
     * @param timeStr 时间字符串（如"14:30"或"24:00"）
     * @return LocalTime对象，解析失败时返回null
     */
    public static Optional<LocalTime> parseTimeString(String timeStr) {
        LocalTime res;
        try {
            String formattedTimeStr = formatTimeString(timeStr);
            // 特殊处理"24:00"的情况，将其转换为LocalTime.MAX（23:59:59.999999999）
            if ("24:00".equals(formattedTimeStr)) {
                res = LocalTime.MAX;
            } else {
                res = LocalTime.parse(formattedTimeStr, DateTimeFormatter.ofPattern("HH:mm"));
            }
        } catch (Exception e) {
            return Optional.empty();
        }
        return Optional.of(res);
    }

    /**
     * 检查时间字符串格式并转换
     * <p>如果格式是"HHmm"，则转换为"HH:mm"</p>
     * <p>如果已经是"HH:mm"格式，则直接返回</p>
     *
     * @param timeStr 时间字符串
     * @return 格式化后的时间字符串
     */
    public static String formatTimeString(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return timeStr;
        }

        // 已经是HH:mm格式
        if (timeStr.contains(":")) {
            return timeStr;
        }

        // 尝试将HHmm转换为HH:mm
        if (timeStr.matches("\\d{3,4}")) {
            // 补齐4位数
            String paddedTime = String.format("%04d", Integer.parseInt(timeStr));
            return paddedTime.substring(0, 2) + ":" + paddedTime.substring(2, 4);
        }

        // 其他情况，返回原字符串
        return timeStr;
    }

    public static Pair<Integer, Integer> splitTimeStr(String timeStr) {
        String str = formatTimeString(timeStr);
        return Pair.of(Integer.valueOf(str.substring(0, 2)), Integer.valueOf(str.substring(3, 5)));
    }

    /**
     * 判断当前时间是否在指定的时间范围内
     * <P>格式为HHMM. 开始时间取闭区间, 结束时间取开区间. 每日的开始时间为 0000, 当日结束时间为 2400</P>
     *
     * @param currentTime  当前时间
     * @param startTimeStr 开始时间
     * @param endTimeStr   结束时间
     * @return 是否在范围内
     */
    public static boolean isTimeInRange(LocalTime currentTime, String startTimeStr,
        String endTimeStr) {
        Optional<LocalTime> startTimeOpt = PriceUtil.parseTimeString(startTimeStr);
        Optional<LocalTime> stopTimeOpt = PriceUtil.parseTimeString(endTimeStr);
        if (startTimeOpt.isEmpty() || stopTimeOpt.isEmpty()) {
            return false;
        }
        LocalTime startTime = startTimeOpt.get();
        LocalTime stopTime = stopTimeOpt.get();

        if (stopTime.equals(LocalTime.MIDNIGHT)) {
            // 如果结束时间是00:00，视为24:00
            return !currentTime.isBefore(startTime);
        } else if (stopTime.isBefore(startTime)) {
            // 处理跨天的情况（如23:00-01:00）
            return !currentTime.isBefore(startTime) && currentTime.isBefore(LocalTime.MAX)
                || !currentTime.isBefore(LocalTime.MIN) && currentTime.isBefore(stopTime);
        }

        // 普通情况
        return !currentTime.isBefore(startTime) && currentTime.isBefore(stopTime);
    }

    /**
     * 判断价格时间段是否能完全覆盖充电时间段
     *
     * @param chargeStartTime 充电开始时间
     * @param chargeStopTime  充电结束时间
     * @param priceStartTime  价格开始时间
     * @param priceStopTime   价格结束时间
     * @return 是否能完全覆盖
     */
    public static boolean isPriceTimeRangeCoveringChargeTimeRange(LocalTime chargeStartTime,
        LocalTime chargeStopTime, LocalTime priceStartTime, LocalTime priceStopTime) {
        if (priceStopTime.equals(LocalTime.MIDNIGHT)) {
            // 如果结束时间是00:00，视为24:00，只需要充电开始时间不早于价格开始时间
            return !chargeStartTime.isBefore(priceStartTime);
        } else if (priceStopTime.isBefore(priceStartTime)) {
            // 处理跨天的情况（价格时段跨天，如23:00-01:00）
            boolean boo = (!chargeStartTime.isBefore(priceStartTime) && chargeStartTime.isBefore(
                LocalTime.MAX)) || (!chargeStartTime.isBefore(LocalTime.MIN)
                && chargeStartTime.isBefore(priceStopTime));
            boo = boo && (
                (!chargeStopTime.isBefore(priceStartTime) && chargeStopTime.isBefore(LocalTime.MAX))
                    || (!chargeStopTime.isBefore(LocalTime.MIN) && chargeStopTime.isBefore(
                    priceStopTime)));
            return boo;
        }

        // 标准的包含关系检查：充电开始时间 >= 价格开始时间 && 充电结束时间 < 价格结束时间
        return !chargeStartTime.isBefore(priceStartTime) && chargeStopTime.isBefore(priceStopTime);
    }

    public static List<ChargePriceItem> sortPriceItems(List<ChargePriceItem> priceItemList) {
        return priceItemList.stream().sorted((p1, p2) -> {
            Optional<LocalTime> t1 = PriceUtil.parseTimeString(p1.getStartTime());
            Optional<LocalTime> t2 = PriceUtil.parseTimeString(p2.getStartTime());
            if (t1.isEmpty() || t2.isEmpty()) {
                return 0;
            }
            return t1.get().compareTo(t2.get());
        }).toList();
    }

}
