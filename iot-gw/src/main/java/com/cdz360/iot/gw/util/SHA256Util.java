package com.cdz360.iot.gw.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SHA256Util {

    private static final Logger log = LoggerFactory.getLogger(SHA256Util.class);

    public static String generate(String key) throws NoSuchAlgorithmException {

        return bytesToHex(MessageDigest.getInstance("SHA-256").digest(
            key.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * use a custom byte to hex converter to get the hashed value in hexadecimal
     *
     * @param hash
     * @return
     */
    private static String bytesToHex(byte[] hash) {
        StringBuffer hexString = new StringBuffer();

        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString();
    }
}
