package com.cdz360.iot.gw.util;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import org.springframework.stereotype.Component;

/**
 * 时间戳生成器
 */
@Component
public class TimestampGenerator {

    /**
     * 小端16进制表示
     *
     * @return
     */
    public static byte[] Generate() {
        // 初始化时区对象，北京时间是UTC+8，所以入参为8
        ZoneOffset zoneOffset = ZoneOffset.ofHours(8);

        // 初始化LocalDateTime对象
        LocalDateTime localDateTime = LocalDateTime.now();

        // 获取LocalDateTime对象对应时区的Unix时间戳
        //System.out.println();

        return IntUtil.toLH((int) localDateTime.toEpochSecond(zoneOffset));
    }

    public static long genUnixTimestamp() {
        // 初始化时区对象，北京时间是UTC+8，所以入参为8
        ZoneOffset zoneOffset = ZoneOffset.ofHours(8);

        // 初始化LocalDateTime对象
        LocalDateTime localDateTime = LocalDateTime.now();

        // 获取LocalDateTime对象对应时区的Unix时间戳
        //System.out.println();

        return localDateTime.toEpochSecond(zoneOffset);
    }
}
