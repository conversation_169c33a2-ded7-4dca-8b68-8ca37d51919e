package com.cdz360.iot.gw.util;

import com.cdz360.base.utils.StringUtils;
import java.net.MalformedURLException;
import java.net.URL;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class URLUtil {

    public static void main(String[] args) {
        String baseUrl1 = "https://backstage.rainbowcharging.com/h5";
        String baseUrl2 = "https://test01-commercial-charge.iot.renwochong.com/#/qr/";
        String relativePath = "524337121060362901";

        String concatenatedUrl1 = concatenateURL(baseUrl1, relativePath);
        System.out.println("Concatenated URL 1: " + concatenatedUrl1);

        String concatenatedUrl2 = concatenateURL(baseUrl2, relativePath);
        System.out.println("Concatenated URL 2: " + concatenatedUrl2);
    }

    public static String concatenateURL(String baseUrl, String relativePath) {
        // 确保basePath以"/"结尾
       if (!baseUrl.endsWith("/")) {
           baseUrl += "/";
       }

        return baseUrl + relativePath;
    }

    /**
     * 格式：ftp://username:password@hostname:port/path
     * 例如：ftp://ftprcd:x5c8%tf4K@************:5579/RCD/DCEUROPE/37/rcd_dceurope_d80_v50.85.9.bin
     *
     * @param ftpUrl
     * @param accountNo
     * @param password
     * @return
     */
    public static String convertFtpUrl2RcdLocation(String ftpUrl, String accountNo, String password) {
        if (StringUtils.isEmpty(ftpUrl)) {
            return null;
        }

        if (StringUtils.isEmpty(accountNo) || StringUtils.isEmpty(password)) {
            return ftpUrl;
        }

        try {
            URL url = new URL(ftpUrl);
            String host = url.getHost();
            int port = url.getPort();
            String path = url.getPath();

            // 格式：ftp://{accountNo}:{password}@{host}:{port}{path}
            return String.format("ftp://%s:%s@%s:%d%s", accountNo, password,
                host, port, path);
        } catch (MalformedURLException e) {
            log.error("Convert FTP URL error: {}", e.getMessage(), e);
            return null;
        }
    }

}
