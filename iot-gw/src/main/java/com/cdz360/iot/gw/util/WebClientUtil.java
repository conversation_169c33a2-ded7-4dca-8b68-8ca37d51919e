package com.cdz360.iot.gw.util;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.iot.gw.biz.ds.IotGwCacheService;
import com.cdz360.iot.gw.config.GwConfigProperties;
import com.fasterxml.jackson.databind.JsonNode;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * WebClient 封装
 */
@Component
public class WebClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(WebClientUtil.class);

    private static final MediaType MEDIATYPE = MediaType.APPLICATION_JSON_UTF8;

    private static IotGwCacheService cacheService;

    private static String baseUrl;

    private static GwConfigProperties gwConfig;

    /**
     * 同步 post
     *
     * @param url
     * @param resultType
     * @param <V>
     * @return
     */
    public static <V> V syncPost(String url, Class<V> resultType) {
        WebClient.RequestBodySpec uri = uri(url, HttpMethod.POST);
        defaultHeader(uri);

        return uri.contentType(MEDIATYPE)
            .retrieve()
            .onStatus(HttpStatusCode::is4xxClientError, res -> {
                logger.debug("Access fail: {}.", res.statusCode());

                // 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .bodyToMono(resultType)
            .block();
    }

    private static String formatResExceptionMsg(String reqUrl, ClientResponse clientResponse) {
        HttpStatusCode statusCode = clientResponse.statusCode();
        if (statusCode instanceof HttpStatus) {
            return "请求云端接口 " + reqUrl + " 返回 http 响应 " + statusCode.value() + " : "
                + ((HttpStatus) statusCode).getReasonPhrase();
        } else {
            return "请求云端接口 " + reqUrl + " 返回 http.code = " + statusCode.value();
        }
    }

    /**
     * 同步 post
     *
     * @param param
     * @param url
     * @param resultType
     * @param <V>
     * @return
     */
    public static <V> V syncPost2(Object param, String url, Class<V> resultType) {
        WebClient.RequestBodySpec uri = uri(url, HttpMethod.POST);
        defaultHeader(uri);

        V ret = uri.contentType(MEDIATYPE)
            .syncBody(param)
            .retrieve()
            .onStatus(HttpStatusCode::is4xxClientError, res -> {
                logger.debug("Access fail: {}.", res);

                // 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .bodyToMono(JsonNode.class)
            .map(json -> {
                logger.info("云端反馈信息: request: {}, response: {}",
                    JsonUtils.toJsonString(param), JsonUtils.toJsonString(json));
                return JsonUtils.fromJson(json, resultType);
            })
            .block();
        return ret;
    }

    /**
     * 同步 post
     *
     * @param param
     * @param url
     * @param resultType
     * @param <V>
     * @return
     */
    public static <V> V syncPost(Object param, String url, Class<V> resultType) {
        WebClient.RequestBodySpec uri = uri(url, HttpMethod.POST);
        defaultHeader(uri);

        V ret = uri.contentType(MEDIATYPE)
            .syncBody(param)
            .retrieve()
            .onStatus(HttpStatusCode::is4xxClientError, res -> {
                logger.debug("Access fail: {}.", res);

                // 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .bodyToMono(JsonNode.class)
            .map(json -> {
                logger.info("云端反馈信息: {}.", JsonUtils.toJsonString(json));
                if (json.get("status").asText().equals("0")) {
                    JsonNode data = json.get("data");
                    return JsonUtils.fromJson(null == data ? json : data, resultType);
                } else {
                    logger.error("error status: {}", json.get("status"));
                    return JsonUtils.fromJson(json, resultType);
                }
            })
            .block();
        return ret;
    }


    public static <V> V syncPostRaw(Object param, String url, Class<V> resultType) {
        WebClient.RequestBodySpec uri = uri(url, HttpMethod.POST);
        defaultHeader(uri);

        V ret = uri.contentType(MEDIATYPE)
            .syncBody(param)
            .retrieve()
            .onStatus(HttpStatusCode::is4xxClientError, res -> {
                logger.warn("Access fail: {}.", res);

                // 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .bodyToMono(resultType)
            .map(json -> {

                logger.info("云端反馈信息: {}.", JsonUtils.toJsonString(json));

                // return JsonUtils.fromJson(json, resultType);
                return json;

            })
            .block();
        return ret;
    }


    /**
     * 异步 post
     *
     * @param param
     * @param url
     * @param resultType
     * @return
     */
    public static <V> Mono<V> asyncPost(String url, Object param, Class<V> resultType) {

        // logger.info("url: {}, param: {}, resultType: {}", url, param, resultType);

        WebClient.RequestBodySpec uri = uri(url, HttpMethod.POST);
        defaultHeader(uri);
        uri.contentType(MEDIATYPE);
        if (param != null) {
            uri.syncBody(param);
        }

        return uri.retrieve()
            .onStatus(HttpStatusCode::is4xxClientError, res -> {
                logger.error("Access fail: url: {} , param: {} , res: {}", url,
                    JsonUtils.toJsonString(param), res);// 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .onStatus(HttpStatusCode::is5xxServerError, res -> {
                logger.error("Access fail: url: {} , param: {} , res: {}", url,
                    JsonUtils.toJsonString(param), res);// 更新token
                updateToken(res);

                return Mono.error(
                    new DcServiceException(WebClientUtil.formatResExceptionMsg(url, res),
                        Level.WARN));
            })
            .bodyToMono(resultType);
    }

    /**
     * 构建 WebClient
     *
     * @param url
     * @param method
     * @return
     */
    private static WebClient.RequestBodySpec uri(String url, HttpMethod method) {
        WebClient.Builder builder = WebClient.builder();
        builder.baseUrl(baseUrl);

        return builder
            .filter(logRequest()).filter(logResponse())
            .build()
            .method(method)
            // .uri(url, version, gwno)
            .uri(url, gwConfig.getVersion(), gwConfig.getGwno())
            .accept(MediaType.APPLICATION_JSON); // 默认都带上version, gwno
    }

    /**
     * 默认添加 token
     *
     * @param uri
     */
    private static void defaultHeader(WebClient.RequestBodySpec uri) {
        // 固定token
        // String token = cacheService.getToken(gwno);
        String token = cacheService.getToken(gwConfig.getGwno());
        logger.info("timestamp: {}, current token: {}.", System.nanoTime(),
            token);//由于log文件中日志并非按照时间顺序写入，增加timestamp用于在高并发场景下对请求顺序跟踪。
        if (null != token) {
            uri.header("Authorization", "Basic " + token);
        }
    }

    private static ExchangeFilterFunction logRequest() {
        return (clientRequest, next) -> {
            logger.info("Request: {} {}", clientRequest.method(), clientRequest.url());
            clientRequest.headers()
                .forEach((name, values) -> {
                    //精简部分日志
                    if (!(name.equals("Accept") || name.equals("Content-Type"))) {
                        values.forEach(value -> logger.info("{}={}", name, value));
                    }
                });
            // logger.info("Request: body {}", clientRequest.body().toString());

            return next.exchange(clientRequest);
        };
    }

    private static ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            logger.info("Response: {}", clientResponse.statusCode());
            clientResponse.headers().asHttpHeaders()
                .forEach((name, values) -> {
                    //精简部分日志
                    values.forEach(value -> {
                        if (!(name.equals("Date")
                            || name.equals("vary")
                            || name.equals("Content-Type")
                            || name.equals("Transfer-Encoding")
                            || name.equals("Content-Length"))) {
                            logger.info("{}={}", name, value);
                        }
                    });
                });
            return Mono.just(clientResponse);
        });
    }

    @Autowired
    public void setCacheService(IotGwCacheService cacheService) {
        WebClientUtil.cacheService = cacheService;
    }

    @Value("${cdz360.api}")
    public void setBaseUrl(String baseUrl) {
        WebClientUtil.baseUrl = baseUrl;
    }

    @Autowired
    public void setGwConfigProperties(GwConfigProperties gwConfigProperties) {
        WebClientUtil.gwConfig = gwConfigProperties;
    }

    /**
     * 请求过程中token过期，重新更新token值
     *
     * @param res
     */
    private static void updateToken(ClientResponse res) {
        if (HttpStatus.UNAUTHORIZED == res.statusCode()) {
            // 同步调用注册
            List<String> authList = res.headers().header("WWW-Authenticate");
            String realm = authList.get(0).substring(12).replaceAll("\"", ""); // 截取 realm

            // 更新token
            try {
                // 生成token[SHA256(网关编号:密码:realm)]并缓存本地
                String newToken = SHA256Util.generate(
                    gwConfig.getGwno() + ":" + gwConfig.getPasscode() + ":" + realm);
                cacheService.updateToken(gwConfig.getGwno(), newToken);
                logger.info("token已更新。 gwno={}, passcode={}, realm={}, new token: {}.",
                    gwConfig.getGwno(), gwConfig.getPasscode(), realm, newToken);
            } catch (NoSuchAlgorithmException e) {
                logger.error("SHA256Util.generate error.", e);
            }
        } else {
            logger.error("平台返回的状态码错误。statusCode: {}", res.statusCode().value());
        }
    }
}
