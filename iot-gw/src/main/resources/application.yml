
cdz360:
  profiles:
#    path: C:/DingChong/iot-gw
    path: .
    rtCfgPath: ./data
  # 网关初始化配置信息
  gw-info:
    name: gw-info
    file: classpath:gw-info.yml
  #  gw:
  #    version: 1.0
  #    gwno: factory001
  #    passcode: factory001
  #    realm: factory001
#  api: http://10.82.0.94:7101/  # test01地址
  api: http://10.82.0.108:7101/  # test04地址
  retry: 5
  check:
    deviation-time: 60000 # 60s
    time-interval: 600000 # 10m

  cache:
    max-size: 5000   # 幂等性缓存的能够缓存记录的最大条数
    clean-size: 1000 # 超过幂等性缓存最大条数后每次清理的条数
  timer:
    heartbeat-timeout: 120000 # 2min 接收桩心跳的超时时间
    check-client-timeout: 120000 # 2min 检测桩TCP连接是否正常的时间
    cfg-report-timeout: 300000 # 5min 检测客户端配置是否上报的超时时间
    upgrade-report-timeout: 5400000 # 90min 检测客户端升级是否上报的超时时间
    charge-idle-timeout: 180000 # 3min 平台开启充电报文未响应的超时时间（适配永联直流桩，放大到3min）
    plug-idle-timeout: 60 # 60min 枪头状态补传机制-枪长时间不在线后每隔1小时上传枪状态(单位是分钟)
  merge-num: 4
  heartbeat:
    processor-threads: 16 #处理心跳的线程数量
  evse:
    debugFilePath: ./evse_debug
logging:
  level:
    com.cdz360.iot.gw: 'DEBUG'
    org.springframework: 'INFO'
    sun.rmi: 'WARN'
    javax.management: 'WARN'
    java.io.serialization: 'WARN'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %-5level - %logger{36}.%M\\(%line\\) - %msg%n"

#  file: logs/gw.log
  config: classpath:logback-spring2.xml

webSocketServer:
  port: 2801

netty:
  tcp-port: 8191
  boss-count: 1
  worker-count: 50
  keep-alive: true
  backlog: 100

sqlite:
  path: cache
  name1: data0001
  name2: dataCoreDepend
  initBusinessSql: initBusiness.sql
  initCoreDependSql: initCoreDepend.sql

offline:
  enable: false
  path: offlineDir
  maxMB: 500

ssh:
  sh-file-name: ssh_reverse_tunnel.sh # ssh反向通道创建即停止脚本

spring:
  main:
    allow-circular-references: true
  rabbitmq:
    host: test04-dz-db.rnd.iot.renwochong.com
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower
    publisher-confirm-type: NONE

  data:
    redis:
      database: 0
      host: test04-dz-db.rnd.iot.renwochong.com
      port: 6379
      password: dingchong
      timeout: 300s
      # Enable SSL support.
      ssl:
        enabled: false
      lettuce:
        pool:
          max-active: 50
          max-idle: 50
          min-idle: 20

---
spring:
  config:
    activate:
      on-profile: test01
  rabbitmq:
    host: test01-dz-db.rnd.iot.renwochong.com
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower
    aliResourceId: 1620936650641344

  data:
    redis:
      database: 0
      #spring.redis.url= # Connection URL, will override host, port and password (user will be ignored), e.g. redis://user:<EMAIL>:6379
      # Redis server host.
      host: test01-dz-db.rnd.iot.renwochong.com
      port: 6379
      password: dingchong
      timeout: 300s
      # Enable SSL support.
      ssl:
        enabled: false
      lettuce:
        pool:
          max-active: 50
          max-idle: 50
          min-idle: 20

cdz360:
  api: http://test01.rnd.iot.renwochong.com:7101
  profiles:
    path: /opt/app/iot-gw-ocpp16
  gw-info:
    name: gw-info
    file: gw-info.yml

---
spring:
  config:
    activate:
      on-profile: test02
  rabbitmq:
    host: test02-dz-db.rnd.iot.renwochong.com
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower
    aliResourceId: 1620936650641344

  data:
    redis:
      database: 0
      #spring.redis.url= # Connection URL, will override host, port and password (user will be ignored), e.g. redis://user:<EMAIL>:6379
      # Redis server host.
      host: test02-dz-db.rnd.iot.renwochong.com
      port: 6379
      password: dingchong
      timeout: 300s
      # Enable SSL support.
      ssl:
        enabled: false
      lettuce:
        pool:
          max-active: 50
          max-idle: 50
          min-idle: 20

cdz360:
  api: http://test02.rnd.iot.renwochong.com:7101
  profiles:
    path: /opt/app/iot-gw-ocpp16
  gw-info:
    name: gw-info
    file: gw-info.yml


---
spring:
  config:
    activate:
      on-profile: test04
  rabbitmq:
    host: test04-dz-db.rnd.iot.renwochong.com
    port: 5672
    username: topower
    password: topower123
    virtual-host: topower
    virtualHost: topower
  data:
    redis:
      database: 0
      #spring.redis.url= # Connection URL, will override host, port and password (user will be ignored), e.g. redis://user:<EMAIL>:6379
      # Redis server host.
      #host: **************
      host: ***********
      port: 6379
      password: dingchong
      timeout: 300s
      # Enable SSL support.
      ssl:
        enabled: false
      lettuce:
        pool:
          max-active: 50
          max-idle: 50
          min-idle: 20

cdz360:
  api: http://test04.rnd.iot.renwochong.com:7101
  profiles:
    path: /opt/app/iot-gw-ocpp16
  gw-info:
    name: gw-info
    file: gw-info.yml




