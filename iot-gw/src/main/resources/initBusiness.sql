
create table if not exists Persistent
(
   type           varchar(40),
   key            varchar(100),
   val            text,
   createTime      datetime,
   updateTime      datetime,
   bak1      varchar(100),
   bak2      varchar(100),
   bak3      varchar(100)
);

-- 桩注册报文保存
create table if not exists evse_register_msg (
  evse_no     varchar (60),   -- 桩编号编号
  msg         varchar (255),  -- 注册报文
  create_time datetime,       -- 创建时间
  update_time datetime        -- 更新时间
)
