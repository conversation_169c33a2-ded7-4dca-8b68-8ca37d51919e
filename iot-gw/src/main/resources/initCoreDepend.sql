
-- 网关信息表
create table if not exists gw_info (
  cancel      boolean,        -- 是否取消
  version     varchar (20),   -- 网关版本
  gwno        varchar (60),   -- 网关编号
  passcode    varchar (255),  -- 网关密码
  create_time datetime,       -- 创建时间
  update_time datetime        -- 更新时间
);

-- MQTT信息表
create table if not exists mqtt_info (
  cancel      boolean,        -- 是否取消
  url     varchar (60),
  clientId        varchar (60),
  username    varchar (60),
  password    varchar (60),
  topic    varchar (60),
  create_time datetime,       -- 创建时间
  update_time datetime        -- 更新时间
);

-- SSH 反向通道需要关闭信息表
create table if not exists ssh_reverse_close (
  flag        varchar(255) PRIMARY KEY NOT NULL,    -- 识别号
  pid         int NOT NULL,                -- 进程id
  expire      int NOT NULL,                -- 有效时间
  create_time datetime,                    -- 创建时间
  update_time datetime                     -- 更新时间
);

CREATE TABLE  if not exists [gw_config](
  [id] INTeger PRIMARY KEY AUTOINCREMENT UNIQUE,
  [config_key] VARCHAR(50) UNIQUE,
  [config_value] VARCHAR(50),
  [value_type] VARCHAR(10) DEFAULT STRING,
  [is_enabled] BOOL NOT NULL DEFAULT 1,
  [config_description] VARCHAR(100),
  [create_time] DATETIME DEFAULT (DATETIME ('now')),
  [update_time] DATETIME DEFAULT (DATETIME ('now')));
