<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <contextName>IotGW</contextName>
    <property name="log.path" value="/opt/logs/"/>
    <timestamp key="byDay" datePattern="yyyyMMdd"/>

    <!-- 文件输出的格式设置 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志日常打印文件: latest.log -->
        <file>logs/latest.log</file>

        <!-- 配置日志所生成的目录以及生成文件名的规则 在logs/201902/yyyyMMDD_trace.log.tgz -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/gw_%d{yyyyMMdd}_%i.log</fileNamePattern>
            <!--<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
            <!--&lt;!&ndash; 文件达到最大500MB时会被压缩和切割 &ndash;&gt;-->
            <!--<maxFileSize>500MB</maxFileSize>-->
            <!--</timeBasedFileNamingAndTriggeringPolicy>-->
            <!-- 日志文件最大尺寸 -->
            <maxFileSize>500MB</maxFileSize>
            <!--日志文件保留天数-->
            <MaxHistory>180</MaxHistory>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>

        <!-- 文件输出的日志的格式 -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - %-5level [%thread] %logger{36}.%M\(%line\) - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- Safely log to the same file from multiple JVMs. Degrades performance! -->
        <prudent>false</prudent>
    </appender>


    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>10000</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 控制台输出的格式设置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} - %-5level [%thread] %logger{36}.%M\(%line\) - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 生产环境下，将此级别配置为适合的级别，以免日志文件太多或影响程序性能 -->
    <logger name="reactor" level="WARN" additivity="false">
        <appender-ref ref="FILE"/>
        <!--
        <appender-ref ref="STDOUT"/>
        -->
    </logger>
    <logger name="io.lettuce" level="WARN" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="javax.management" level="WARN" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>
    <logger name="org.springframework.web.reactive" level="WARN" additivity="false">
        <appender-ref ref="FILE"/>
        <appender-ref ref="STDOUT"/>

    </logger>


    <root level="TRACE">
        <appender-ref ref="FILE"/>
        <!-- 生产环境将请 stdout 去掉-->
        <appender-ref ref="STDOUT"/>

    </root>
</configuration>