package com.cdz360.iot.gw.biz;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.model.core.StartTransactionRequest;
import eu.chargetime.ocpp.model.core.StatusNotificationRequest;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * EvseStatusQueryService 单元测试
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@SpringJUnitConfig
class GetTransDataServiceTest {

    @InjectMocks
    private GetTransDataService getTransDataService;

    @BeforeEach
    void setUp() {
        // 测试准备
    }

    @Test
    void testQueryStatusNotificationMsg_WithValidEvseNo() {
        // 测试有效的桩编号 - 使用当前活跃日志文件中存在的桩编号
        String tid = "123TID";
        String evseNo = "2025y06i05";
        List<Integer> needReportPlugIdxList = Arrays.asList(1, 2);

        EvseMsgBase base = new EvseMsgBase();
        base.setTid(tid);
        base.setEvseNo(evseNo);
        Map<Integer, StatusNotificationRequest> result = getTransDataService.queryStatusNotificationMsg(
            base, needReportPlugIdxList);

        assertNotNull(result);

        // 验证结果是Map类型
        assertTrue(result instanceof Map);

        System.out.println("找到的StatusNotification数量: " + result.size());
        System.out.println("查询结果: " + result.values().toString());
    }

    @Test
    void testQueryStatusNotificationMsg_WithInvalidEvseNo() {
        // 测试无效的桩编号
        String tid = "123TID";
        String evseNo = "INVALID_EVSE_NO";
        List<Integer> needReportPlugIdxList = Arrays.asList(1, 2);

        EvseMsgBase base = new EvseMsgBase();
        base.setTid(tid);
        base.setEvseNo(evseNo);
        Map<Integer, StatusNotificationRequest> result = getTransDataService.queryStatusNotificationMsg(
            base, needReportPlugIdxList);

        assertNotNull(result);

        // 当找不到记录时，应该返回空Map
        assertTrue(result.isEmpty());

        System.out.println("无效桩编号查询结果: " + result);
    }

    @Test
    void testQueryStartTransactionMsg() {
        String tid = "123TID";
        String evseNo = "2025y06i05";
        String idTag = "0000020755113059";

        Optional<StartTransactionRequest> result = getTransDataService.queryStartTransactionMsg(tid,
            evseNo, idTag, null);

        assertNotNull(result);

        System.out.println("查询结果: " + result.isPresent());
        result.ifPresent(startTransactionRequest -> System.out.println(
            "查询结果: " + JsonUtils.toJsonTimeString(startTransactionRequest)));
    }

} 