package com.cdz360.iot.gw.core.biz;

import jakarta.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
@SpringJUnitConfig
public class OrderNoGeneratorTest {

    @Resource
    private OrderNoGenerator orderNoGenerator;

    @Test
    public void testGenerateOrderNumber() throws InterruptedException {
        // 测试生成10个订单号
        for (int i = 0; i < 10; i++) {
            Thread.sleep(1000); // 减少等待时间用于测试
            Pair<String, Integer> pair = orderNoGenerator.generateOrderNumber();
            System.out.println("订单号: " + pair.getLeft());
            System.out.println("交易ID: " + pair.getRight());
            System.out.println("---");
        }
    }
} 