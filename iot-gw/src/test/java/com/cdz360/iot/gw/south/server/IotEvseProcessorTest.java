package com.cdz360.iot.gw.south.server;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * IotEvseProcessor 的 getPriceItemCoveringTimeRange 方法单元测试
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class IotEvseProcessorTest {

    private IotEvseProcessor iotEvseProcessor;
    private ChargePriceVo testPriceVo;

    @BeforeEach
    void setUp() {
        iotEvseProcessor = new IotEvseProcessor();
        testPriceVo = createTestPriceVo();
    }

    /**
     * 创建测试用的价格数据 基于用户提供的JSON数据
     */
    private ChargePriceVo createTestPriceVo() {
        String jsonStr = "{\"id\":1101,\"enable\":false,\"itemList\":[{\"code\":4,\"startTime\":\"00:00\",\"endTime\":\"13:30\",\"elecPrice\":0.5433,\"servPrice\":0.4566},{\"code\":3,\"startTime\":\"13:30\",\"endTime\":\"14:00\",\"elecPrice\":0.7722,\"servPrice\":0.4566},{\"code\":2,\"startTime\":\"14:00\",\"endTime\":\"14:30\",\"elecPrice\":0.9233,\"servPrice\":0.4566},{\"code\":1,\"startTime\":\"14:30\",\"endTime\":\"15:00\",\"elecPrice\":1.025,\"servPrice\":0.4566},{\"code\":4,\"startTime\":\"15:00\",\"endTime\":\"15:30\",\"elecPrice\":0.5433,\"servPrice\":0.4566},{\"code\":3,\"startTime\":\"15:30\",\"endTime\":\"16:00\",\"elecPrice\":0.7722,\"servPrice\":0.4566},{\"code\":2,\"startTime\":\"16:00\",\"endTime\":\"16:30\",\"elecPrice\":0.9233,\"servPrice\":0.4566},{\"code\":1,\"startTime\":\"16:30\",\"endTime\":\"17:00\",\"elecPrice\":1.025,\"servPrice\":0.4566},{\"code\":4,\"startTime\":\"17:00\",\"endTime\":\"24:00\",\"elecPrice\":0.5433,\"servPrice\":0.4566}]}";
        return JsonUtils.fromJson(jsonStr, ChargePriceVo.class);
    }

    private ChargePriceItem createPriceItem(int code, String startTime, String endTime,
        double elecPrice, double servPrice) {
        ChargePriceItem item = new ChargePriceItem();
        item.setCode(code);
        item.setStartTime(startTime);
        item.setEndTime(endTime);
        item.setElecPrice(BigDecimal.valueOf(elecPrice));
        item.setServPrice(BigDecimal.valueOf(servPrice));
        return item;
    }

    /**
     * 将时间转换为时间戳
     */
    private long toEpochSecond(int hour, int minute, ZoneId zoneId) {
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, hour, minute);
        return ZonedDateTime.of(dateTime, zoneId).toEpochSecond();
    }

    @Test
    @DisplayName("测试无效输入参数")
    void testGetPriceItemCoveringTimeRange_InvalidInputs() {
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试开始时间 <= 0
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            0, toEpochSecond(10, 0, testZone), testPriceVo, testZone.toString());
        assertNull(result, "当开始时间为0时应返回null");

        // 测试结束时间 <= 0
        result = invokeGetPriceItemCoveringTimeRange(
            toEpochSecond(9, 0, testZone), 0, testPriceVo, testZone.toString());
        assertNull(result, "当结束时间为0时应返回null");

        // 测试开始时间 >= 结束时间
        result = invokeGetPriceItemCoveringTimeRange(
            toEpochSecond(10, 0, testZone), toEpochSecond(9, 0, testZone), testPriceVo,
            testZone.toString());
        assertNull(result, "当开始时间晚于结束时间时应返回null");

        // 测试价格信息为null
        result = invokeGetPriceItemCoveringTimeRange(
            toEpochSecond(9, 0, testZone), toEpochSecond(10, 0, testZone), null,
            testZone.toString());
        assertNull(result, "当价格信息为null时应返回null");

        // 测试价格列表为空
        ChargePriceVo emptyPriceVo = new ChargePriceVo();
        emptyPriceVo.setItemList(new ArrayList<>());
        result = invokeGetPriceItemCoveringTimeRange(
            toEpochSecond(9, 0, testZone), toEpochSecond(10, 0, testZone), emptyPriceVo,
            testZone.toString());
        assertNull(result, "当价格列表为空时应返回null");
    }

    @Test
    @DisplayName("测试完全覆盖的时间段匹配")
    void testGetPriceItemCoveringTimeRange_CoveringMatching() {
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试在00:00-13:30时段内的充电：09:00-10:00
        long startTime = toEpochSecond(9, 0, testZone);
        long stopTime = toEpochSecond(10, 0, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");
        assertEquals("00:00", result.getStartTime(), "开始时间应该是00:00");
        assertEquals("13:30", result.getEndTime(), "结束时间应该是13:30");

        // 测试在14:00-14:30时段内的充电：14:10-14:20
        startTime = toEpochSecond(14, 10, testZone);
        stopTime = toEpochSecond(14, 20, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(2, result.getCode(), "价格代码应该是2");
        assertEquals("14:00", result.getStartTime(), "开始时间应该是14:00");
        assertEquals("14:30", result.getEndTime(), "结束时间应该是14:30");
    }

    @Test
    @DisplayName("测试边界时间的覆盖匹配")
    void testGetPriceItemCoveringTimeRange_BoundaryMatching() {
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试刚好在时段开始边界：13:30-13:45（应该被13:30-14:00覆盖）
        long startTime = toEpochSecond(13, 30, testZone);
        long stopTime = toEpochSecond(13, 45, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(3, result.getCode(), "价格代码应该是3");
        assertEquals("13:30", result.getStartTime(), "开始时间应该是13:30");
        assertEquals("14:00", result.getEndTime(), "结束时间应该是14:00");

        // 测试刚好在时段结束边界：13:45-14:00（应该被13:30-14:00覆盖，但结束时间需要严格小于边界）
        startTime = toEpochSecond(13, 45, testZone);
        stopTime = toEpochSecond(13, 59, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(3, result.getCode(), "价格代码应该是3");
    }

    @Test
    @DisplayName("测试跨时段充电不被覆盖")
    void testGetPriceItemCoveringTimeRange_CrossTimeRange() {
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试跨越13:30-14:00和14:00-14:30两个时段的充电：13:45-14:15
        long startTime = toEpochSecond(13, 45, testZone);
        long stopTime = toEpochSecond(14, 15, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());

        assertNull(result, "跨越多个价格时段的充电应该返回null");

        // 测试跨越14:30-15:00和15:00-15:30两个时段的充电：14:45-15:15
        startTime = toEpochSecond(14, 45, testZone);
        stopTime = toEpochSecond(15, 15, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());

        assertNull(result, "跨越多个价格时段的充电应该返回null");
    }

    @Test
    @DisplayName("测试不同时区的时间转换")
    void testGetPriceItemCoveringTimeRange_DifferentTimeZones() {
        // 测试+09:00时区 (UTC+9)
        ZoneId utcPlus9Zone = ZoneId.of("+09:00");
        long startTime = toEpochSecond(9, 0, utcPlus9Zone);
        long stopTime = toEpochSecond(10, 0, utcPlus9Zone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, "+09:00");
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");

        // 测试+00:00时区 (UTC+0)
        ZoneId utcZone = ZoneId.of("+00:00");
        startTime = toEpochSecond(14, 10, utcZone);
        stopTime = toEpochSecond(14, 20, utcZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, "+00:00");
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(2, result.getCode(), "价格代码应该是2");

        // 测试+08:00时区完整格式 (UTC+8)
        ZoneId utcPlus8Zone = ZoneId.of("+08:00");
        startTime = toEpochSecond(9, 0, utcPlus8Zone);
        stopTime = toEpochSecond(10, 0, utcPlus8Zone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, "+08:00");
        System.out.println("result3 = " + result);

        assertNotNull(result, "应该找到能覆盖时段的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");

        // 测试简化时区格式 "+8"
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, "+8");
        System.out.println("result4 = " + result);

        assertNotNull(result, "简化时区格式应该正常工作");
        assertEquals(4, result.getCode(), "价格代码应该是4");

        // 测试空时区，应该使用系统默认时区
        startTime = toEpochSecond(9, 0, ZoneId.systemDefault());
        stopTime = toEpochSecond(10, 0, ZoneId.systemDefault());
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, "");
        System.out.println("result5 = " + result);

        assertNotNull(result, "空时区应该使用系统默认时区");
        assertEquals(4, result.getCode(), "价格代码应该是4");

        // 测试null时区，应该使用系统默认时区
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, null);
        System.out.println("result6 = " + result);

        assertNotNull(result, "null时区应该使用系统默认时区");
        assertEquals(4, result.getCode(), "价格代码应该是4");
    }

    @Test
    @DisplayName("测试夜间时段的覆盖")
    void testGetPriceItemCoveringTimeRange_NightTimeRange() {
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试在17:00-24:00时段内的充电：20:00-22:00
        long startTime = toEpochSecond(20, 0, testZone);
        long stopTime = toEpochSecond(22, 0, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到能覆盖夜间时段的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");
        assertEquals("17:00", result.getStartTime(), "开始时间应该是17:00");
        assertEquals("24:00", result.getEndTime(), "结束时间应该是24:00");

        // 测试接近午夜的充电：23:30-23:59
        startTime = toEpochSecond(23, 30, testZone);
        stopTime = toEpochSecond(23, 59, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, testPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到能覆盖接近午夜时段的价格信息");
        assertEquals(4, result.getCode(), "价格代码应该是4");
    }

    @Test
    @DisplayName("测试跨天价格时段的覆盖")
    void testGetPriceItemCoveringTimeRange_CrossDayTimeRange() {
        // 创建包含跨天时段的价格信息
        ChargePriceVo crossDayPriceVo = new ChargePriceVo();
        List<ChargePriceItem> itemList = new ArrayList<>();

        // 跨天时段：23:00-01:00
        itemList.add(createPriceItem(1, "23:00", "01:00", 0.6, 0.3));
        // 正常时段：01:00-18:00
        itemList.add(createPriceItem(2, "01:00", "18:00", 0.8, 0.4));

        crossDayPriceVo.setItemList(itemList);
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试在跨天时段内的深夜充电：23:30-23:55
        long startTime = toEpochSecond(23, 30, testZone);
        long stopTime = toEpochSecond(23, 55, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, crossDayPriceVo, testZone.toString());
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到跨天时段的价格信息");
        assertEquals(1, result.getCode(), "23:30-23:55应该匹配跨天时段");
        assertEquals("23:00", result.getStartTime());
        assertEquals("01:00", result.getEndTime());

        // 测试在跨天时段内的凌晨充电：00:30-00:55
        startTime = toEpochSecond(0, 30, testZone);
        stopTime = toEpochSecond(0, 55, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, crossDayPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到跨天时段的价格信息");
        assertEquals(1, result.getCode(), "00:30-00:55应该匹配跨天时段");

        // 测试跨越跨天时段边界的充电：00:30-01:30（应该返回null，因为跨越了时段边界）
        startTime = toEpochSecond(0, 30, testZone);
        stopTime = toEpochSecond(1, 30, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, crossDayPriceVo, testZone.toString());
        System.out.println("result3 = " + result);

        assertNull(result, "跨越跨天时段边界的充电应该返回null");

        // 测试正常时段内的充电：10:00-12:00
        startTime = toEpochSecond(10, 0, testZone);
        stopTime = toEpochSecond(12, 0, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, crossDayPriceVo, testZone.toString());
        System.out.println("result4 = " + result);

        assertNotNull(result, "应该找到正常时段的价格信息");
        assertEquals(2, result.getCode(), "10:00-12:00应该匹配正常时段");
    }

    @Test
    @DisplayName("测试24:00结束时间的特殊处理")
    void testGetPriceItemCoveringTimeRange_MidnightEndTime() {
        // 创建以24:00结束的价格时段
        ChargePriceVo midnightPriceVo = new ChargePriceVo();
        List<ChargePriceItem> itemList = new ArrayList<>();

        // 时段：20:00-24:00（到午夜结束）
        itemList.add(createPriceItem(1, "20:00", "24:00", 0.6, 0.3));

        midnightPriceVo.setItemList(itemList);
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试在20:00-24:00时段内的充电：21:00-23:00
        long startTime = toEpochSecond(21, 0, testZone);
        long stopTime = toEpochSecond(23, 0, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, midnightPriceVo, testZone.toString());
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到能覆盖到午夜的时段");
        assertEquals(1, result.getCode(), "价格代码应该是1");
        assertEquals("20:00", result.getStartTime());
        assertEquals("24:00", result.getEndTime());

        // 测试从20:00开始的长时充电：20:00-23:30
        startTime = toEpochSecond(20, 0, testZone);
        stopTime = toEpochSecond(23, 30, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, midnightPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNotNull(result, "应该找到能覆盖从开始时间到接近午夜的时段");
        assertEquals(1, result.getCode(), "价格代码应该是1");
    }

    @Test
    @DisplayName("测试价格项时间格式无效时的处理")
    void testGetPriceItemCoveringTimeRange_InvalidTimeFormat() {
        // 创建包含无效时间格式的价格信息
        ChargePriceVo invalidPriceVo = new ChargePriceVo();
        List<ChargePriceItem> itemList = new ArrayList<>();

        // 添加无效时间格式的价格项
        ChargePriceItem invalidItem = new ChargePriceItem();
        invalidItem.setCode(1);
        invalidItem.setStartTime("invalid");
        invalidItem.setEndTime("25:00");
        invalidItem.setElecPrice(BigDecimal.valueOf(1.0));
        invalidItem.setServPrice(BigDecimal.valueOf(0.5));
        itemList.add(invalidItem);

        // 添加有效的价格项
        itemList.add(createPriceItem(2, "10:00", "12:00", 0.8, 0.4));

        invalidPriceVo.setItemList(itemList);
        ZoneOffset testZone = ZoneId.systemDefault().getRules().getOffset(java.time.Instant.now());

        // 测试时间在有效价格项范围内
        long startTime = toEpochSecond(10, 30, testZone);
        long stopTime = toEpochSecond(11, 30, testZone);
        ChargePriceItem result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, invalidPriceVo, testZone.toString());
        System.out.println("result1 = " + result);

        assertNotNull(result, "应该找到有效的价格信息");
        assertEquals(2, result.getCode(), "应该匹配有效的价格项");

        // 测试时间不在任何有效价格项范围内
        startTime = toEpochSecond(15, 0, testZone);
        stopTime = toEpochSecond(16, 0, testZone);
        result = invokeGetPriceItemCoveringTimeRange(
            startTime, stopTime, invalidPriceVo, testZone.toString());
        System.out.println("result2 = " + result);

        assertNull(result, "当时间不在任何有效价格项范围内时应返回null");
    }

    /**
     * 通过反射调用私有方法 getPriceItemCoveringTimeRange
     * {@link IotEvseProcessor#getPriceItemCoveringTimeRange(long, long, ChargePriceVo, String)}
     */
    private ChargePriceItem invokeGetPriceItemCoveringTimeRange(long startTimeEpoch,
        long stopTimeEpoch,
        ChargePriceVo priceVo, String timeZone) {
        return (ChargePriceItem) ReflectionTestUtils.invokeMethod(iotEvseProcessor,
            "getPriceItemCoveringTimeRange", startTimeEpoch, stopTimeEpoch, priceVo, timeZone);
    }
} 