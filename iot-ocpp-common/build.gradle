plugins {
    id 'java'
}

bootJar {
    enabled = false
}

jar {
    enabled = true
}


dependencies {
    implementation 'jakarta.xml.bind:jakarta.xml.bind-api'
    runtimeOnly 'com.sun.xml.bind:jaxb-impl'
    // SOAP API
    implementation 'jakarta.xml.ws:jakarta.xml.ws-api:4.0.0'
    implementation 'jakarta.xml.soap:jakarta.xml.soap-api:3.0.0'

    // 运行时实现
    runtimeOnly 'com.sun.xml.ws:jaxws-rt:4.0.0'
    runtimeOnly 'com.sun.xml.messaging.saaj:saaj-impl:3.0.0'


    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.11.0'
    testImplementation 'org.hamcrest:hamcrest-core:1.3'

}

test {
    useJUnitPlatform()
}
