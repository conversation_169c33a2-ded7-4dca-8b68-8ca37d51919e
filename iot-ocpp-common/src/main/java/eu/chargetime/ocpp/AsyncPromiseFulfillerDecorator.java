package eu.chargetime.ocpp;
/*
   ChargeTime.eu - Java-OCA-OCPP

   MIT License

   Copyright (C) 2016-2018 <PERSON> <<EMAIL>>

   Permission is hereby granted, free of charge, to any person obtaining a copy
   of this software and associated documentation files (the "Software"), to deal
   in the Software without restriction, including without limitation the rights
   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   copies of the Software, and to permit persons to whom the Software is
   furnished to do so, subject to the following conditions:

   The above copyright notice and this permission notice shall be included in all
   copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
   SOFTWARE.
*/

import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.Request;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AsyncPromiseFulfillerDecorator implements PromiseFulfiller {
  private static final Logger logger = LoggerFactory.getLogger(AsyncPromiseFulfillerDecorator.class);

  private final PromiseFulfiller promiseFulfiller;

  private static ExecutorService executor = Executors.newCachedThreadPool();

  public static void setExecutor(ExecutorService newExecutor) {
    executor = newExecutor;
  }

  @Override
  public void fulfill(
      CompletableFuture<Confirmation> promise, SessionEvents eventHandler, Request request) {
    logger.info("[{}] AsyncPromiseFulfillerDecorator fulfill", request.getBase().getTid());
    executor.submit(() -> promiseFulfiller.fulfill(promise, eventHandler, request));
  }

  public AsyncPromiseFulfillerDecorator(PromiseFulfiller promiseFulfiller) {

    this.promiseFulfiller = promiseFulfiller;
  }
}
