package eu.chargetime.ocpp;
/*
   ChargeTime.eu - Java-OCA-OCPP

   MIT License

   Copyright (C) 2016-2018 <PERSON> <<EMAIL>>

   Permission is hereby granted, free of charge, to any person obtaining a copy
   of this software and associated documentation files (the "Software"), to deal
   in the Software without restriction, including without limitation the rights
   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   copies of the Software, and to permit persons to whom the Software is
   furnished to do so, subject to the following conditions:

   The above copyright notice and this permission notice shall be included in all
   copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
   SOFTWARE.
*/

import static eu.chargetime.ocpp.utilities.EvseUtil.getEvseNo;
import static eu.chargetime.ocpp.utilities.TraceUtil.shortSession;

import com.cdz360.base.utils.JsonUtils;
import eu.chargetime.ocpp.feature.Feature;
import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.model.Request;
import eu.chargetime.ocpp.model.SessionInformation;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Handles basic server logic: Holds a list of supported features. Keeps track of outgoing requests.
 * Calls back when a confirmation is received.
 */
public class Server {

    public static final int INITIAL_SESSIONS_NUMBER = 1000;
    private static final Logger logger = LoggerFactory.getLogger(Server.class);
    private final Map<UUID, ISession> sessions;
    private final Listener listener;
    private final IPromiseRepository promiseRepository;

    /**
     * Constructor. Handles the required injections.
     *
     * @param listener          injected listener.
     * @param promiseRepository injected promise repository.
     */
    public Server(Listener listener, IPromiseRepository promiseRepository) {
        this.listener = listener;
        this.promiseRepository = promiseRepository;
        this.sessions = new ConcurrentHashMap<>(INITIAL_SESSIONS_NUMBER);
    }

    /**
     * Start listening for clients.
     *
     * @param hostname     Url or IP of the server as String.
     * @param port         the port number of the server.
     * @param serverEvents Callback handler for server specific events.
     */
    public void open(String hostname, int port, ServerEvents serverEvents) {
        logger.info("WS Opening server on {}:{}", hostname, port);
        listener.open(hostname, port, new ListenerEvents() {
            @Override
            public void authenticateSession(SessionInformation information, String username,
                String password) throws AuthenticationException {
                String tid = information.getTid();
                String evseNo = getEvseNo(information.getIdentifier());
                logger.info(
                    "[{} {}] Server authenticateSession. username: {}, password: {}, information: {}",
                    tid, evseNo, username, password, JsonUtils.toJsonString(information));
                serverEvents.authenticateSession(information, username, password);
            }

            @Override
            public void newSession(ISession session, SessionInformation information) {
                String tid = information.getTid();
                String evseNo = getEvseNo(information.getIdentifier());
                String socketAddr = information.getSocketAddress();
                logger.info("[{} {}] Server newSession 新会话建立. information: {}", tid, evseNo,
                    JsonUtils.toJsonString(information));

                session.accept(new SessionEvents() {
                    @Override
                    public void handleConfirmation(String uniqueId, Confirmation confirmation) {
                        String tid = getTid(confirmation);
                        logger.info(
                            "[{} {}] SessionEvents handleConfirmation. uniqueId: {}, confirmation: {}",
                            tid, evseNo, uniqueId, confirmation);
                        Optional<CompletableFuture<Confirmation>> promiseOptional = promiseRepository.getPromise(
                            uniqueId);
                        if (promiseOptional.isPresent()) {
                            promiseOptional.get().complete(confirmation);
                            promiseRepository.removePromise(uniqueId);
                        } else {
                            logger.warn("[{} {}] Promise not found for confirmation {}", tid,
                                evseNo, confirmation);
                        }
                    }

                    @Override
                    public Confirmation handleRequest(Request request)
                        throws UnsupportedFeatureException {
                        String tid = request.getBase().getTid();
                        String evseNo = request.getBase().getEvseNo();
                        logger.info("[{} {}] SessionEvents handleRequest. request: {}",
                            tid, evseNo, request);
                        Optional<Feature> featureOptional = session.getFeatureRepository()
                            .findFeature(request);
                        if (featureOptional.isPresent()) {
                            Optional<UUID> sessionIdOptional = getSessionID(session);
                            if (sessionIdOptional.isPresent()) {
                                return featureOptional.get()
                                    .handleRequest(sessionIdOptional.get(), request);
                            } else {
                                logger.error(
                                    "[{} {}] Unable to handle request ({}), the active session was not found for {}.",
                                    tid, evseNo, request, session.getSessionId());
                                throw new IllegalStateException("Active session not found");
                            }
                        } else {
                            logger.warn("[{} {}] Unable to handle request ({}), feature not found.",
                                tid, evseNo, request);
                            throw new UnsupportedFeatureException();
                        }
                    }

                    @Override
                    public boolean asyncCompleteRequest(String uniqueId, Confirmation confirmation)
                        throws UnsupportedFeatureException, OccurenceConstraintException {
                        String tid = getTid(confirmation);
                        logger.info(
                            "[{} {}] SessionEvents asyncCompleteRequest. uniqueId: {}, confirmation: {}",
                            tid, evseNo, uniqueId, confirmation);

//                        String chKey = confirmation.getBase().getChKey();
//                        SaveTransDataService.evseImport(tid, chKey, evseNo,
//                            confirmation.toString());

                        return session.completePendingPromise(uniqueId, confirmation);
                    }

                    @Override
                    public void handleError(String uniqueId, String errorCode,
                        String errorDescription, Object payload) {
                        logger.info(
                            "[ {}] SessionEvents handleError. uniqueId: {}, errorCode: {}, errorDescription: {}, payload: {}",
                            evseNo, uniqueId, errorCode, errorDescription, payload);
                        Optional<CompletableFuture<Confirmation>> promiseOptional = promiseRepository.getPromise(
                            uniqueId);
                        if (promiseOptional.isPresent()) {
                            promiseOptional.get().completeExceptionally(
                                new CallErrorException(errorCode, errorDescription, payload));
                            promiseRepository.removePromise(uniqueId);
                        } else {
                            logger.error("[ {}]Promise not found for error {}", evseNo,
                                errorDescription);
                        }
                    }

                    @Override
                    public void handleConnectionClosed() {
                        logger.info("[ {}] SessionEvents handleConnectionClosed.", evseNo);
                        Optional<UUID> sessionIdOptional = getSessionID(session);
                        if (sessionIdOptional.isPresent()) {
                            serverEvents.lostSession(sessionIdOptional.get(), socketAddr);
                            sessions.remove(sessionIdOptional.get());
                        } else {
                            logger.warn("Active session not found for {}", session.getSessionId());
                        }
                    }

                    @Override
                    public void handleConnectionOpened() {
                        logger.info("[ {}] SessionEvents handleConnectionOpened.", evseNo);
                    }
                });

                sessions.put(session.getSessionId(), session);

                Optional<UUID> sessionIdOptional = getSessionID(session);
                if (sessionIdOptional.isPresent()) {
                    serverEvents.newSession(sessionIdOptional.get(), information);
                    logger.debug("[{} {}] Session created: {}", tid, evseNo,
                        session.getSessionId());
                } else {
                    throw new IllegalStateException("Failed to create a session");
                }
            }
        });
    }

    private String getTid(Confirmation conf) {
        return Optional.ofNullable(conf)
            .map(Confirmation::getBase)
            .map(EvseMsgBase::getTid)
            .orElse("");
    }

    private Optional<UUID> getSessionID(ISession session) {
        if (!sessions.containsKey(session.getSessionId())) {
            return Optional.empty();
        }

        return Optional.of(session.getSessionId());
    }

    /**
     * Close all connections and stop listening for clients.
     */
    public void close() {
        logger.info("listener.close");
        listener.close();
    }

    /**
     * Send a message to a client.
     *
     * @param sessionIndex Session index of the client.
     * @param request      Request for the client.
     * @return Callback handler for when the client responds.
     * @throws UnsupportedFeatureException  Thrown if the feature isn't among the list of supported
     *                                      featured.
     * @throws OccurenceConstraintException Thrown if the request isn't valid.
     */
    public CompletableFuture<Confirmation> send(UUID sessionIndex, Request request)
        throws UnsupportedFeatureException, OccurenceConstraintException, NotConnectedException {
        String tid = request.getBase().getTid();
        logger.info("[{} {}] Server send. request: {}", tid, shortSession(sessionIndex),
            JsonUtils.toJsonTimeString(request));
        ISession session = sessions.get(sessionIndex);

        if (session == null) {
            logger.warn("[{}] Session not found by index: {}", tid, sessionIndex);

            // No session found means client disconnected and request should be cancelled
            throw new NotConnectedException();
        }

        Optional<Feature> featureOptional = session.getFeatureRepository().findFeature(request);
        if (!featureOptional.isPresent()) {
            throw new UnsupportedFeatureException();
        }

        if (!request.validate()) {
            throw new OccurenceConstraintException();
        }

        String id = session.storeRequest(request);
        CompletableFuture<Confirmation> promise = promiseRepository.createPromise(id);
        session.sendRequest(featureOptional.get().getAction(), request, id);
        return promise;
    }

    /**
     * Indicate completion of a pending request.
     *
     * @param sessionIndex Session index of the client.
     * @param uniqueId     the unique id used for the original {@link Request}.
     * @param confirmation the {@link Confirmation} to the original {@link Request}.
     * @return a boolean indicating if pending request was found.
     * @throws NotConnectedException Thrown if session with passed sessionIndex is not found
     */
    public boolean asyncCompleteRequest(UUID sessionIndex, String uniqueId,
        Confirmation confirmation)
        throws NotConnectedException, UnsupportedFeatureException, OccurenceConstraintException {
        String tid = getTid(confirmation);
        logger.info("[{} {}] Server asyncCompleteRequest. uniqueId: {}, confirmation: {}", tid,
            shortSession(sessionIndex), uniqueId, JsonUtils.toJsonTimeString(confirmation));
        ISession session = sessions.get(sessionIndex);

        if (session == null) {
            logger.warn("[{}] Session not found by index: {}", tid, sessionIndex);

            // No session found means client disconnected and request should be cancelled
            throw new NotConnectedException();
        }

        return session.completePendingPromise(uniqueId, confirmation);
    }


    public boolean isSessionOpen(UUID sessionIndex) {
        return sessions.containsKey(sessionIndex);
    }

    /**
     * Close connection to a client
     *
     * @param sessionIndex Session index of the client.
     */
    public void closeSession(UUID sessionIndex) {
        logger.info("Close session: {}", shortSession(sessionIndex));
        ISession session = sessions.get(sessionIndex);
        if (session != null) {
            session.close();
        }
    }
}
