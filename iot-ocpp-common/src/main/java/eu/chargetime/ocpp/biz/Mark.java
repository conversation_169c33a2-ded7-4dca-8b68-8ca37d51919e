package eu.chargetime.ocpp.biz;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.util.ResourceUtils;

/**
 * 记录分片信息
 */
public class Mark {

    // 系统换行符
    public static String NEW_LINE = System.getProperty("line.separator");

    private String filePath = null;
    private String path = "trace_data/";
    private String name = "mark.txt";

    // 分片日期: 当前分片
    private String currDate = null; // yyyyMMdd
    // 分片索引: 当前分片
    private short currIndex = -1;

    /**
     * 构造函数，文件内容初始化到内存
     *
     * @throws IOException
     */
    public Mark() throws IOException {
        String root = ResourceUtils.getURL("*/").getPath().replaceAll("\\*", "");
        String dirPath = root + this.path;
        this.filePath = dirPath + this.name;

        // 目录文件夹
        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }

        // 文件
        file = new File(this.filePath);
        if (!file.exists()) {
            file.createNewFile();
            this.currDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
            this.currIndex = 0;
            this.update2File();
            return;
        }

        BufferedReader reader = new BufferedReader(new FileReader(file));
        String line = null;
        int idx = 0;
        // 一次读入一行，直到读入null为文件结束
        while ((line = reader.readLine()) != null) {
            // 去除换行符
            line = line.replace(NEW_LINE, "").trim();

            // 显示行号
            if (0 == idx) {
                this.currDate = line;
            } else if (1 == idx) {
                this.currIndex = Byte.valueOf(line);
                break;
            }

            idx++;
        }

        // 文件空内容
        if (0 == idx) {
            this.currDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
            this.currIndex = 0;
            reader.close();
            this.update2File();
            return;
        }

        reader.close();
    }

    public String getCurrDate() {
        return currDate;
    }

    public short getCurrIndex() {
        return currIndex;
    }

    /**
     * 更新分片标识
     */
    public void update() {
        String nowDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        if (nowDate.equals(this.currDate)) {
            this.currIndex += 1;
        } else {
            this.currDate = nowDate;
            this.currIndex = 0;
        }

        // 同步到文件
        this.update2File();
    }

    /**
     * 更新文件标识
     */
    private void update2File() {
        FileWriter fw = null;
        try {
            fw = new FileWriter(this.filePath);
            fw.write(this.currDate + NEW_LINE);
            fw.write(this.currIndex + NEW_LINE);
            fw.flush();
            fw.close();
        } catch (IOException e) {
            // error
        } finally {
            if (null != fw) {
                try {
                    fw.close();
                } catch (IOException e) {
                    // error
                }
            }
        }
    }

    /**
     * 获取当前文件名
     *
     * @param copy true: 用于拷贝文件名 false: 非用于拷贝文件名
     * @return
     */
    public String getCurrFileName(boolean copy) {
        if (copy && !this.isNewDay()) {
            return this.currDate + "_trace_" + this.currIndex + ".log";
        } else {
            if (this.currIndex > 0 && copy) {
                return this.currDate + "_trace_" + this.currIndex + ".log";
            } else {
                return this.currDate + "_trace" + ".log";
            }
        }
    }

    /**
     * @return true: 已经新的一天 false: 非新一天
     */
    public boolean isNewDay() {
        return !this.currDate.equals(new SimpleDateFormat("yyyyMMdd").format(new Date()));
    }
}
