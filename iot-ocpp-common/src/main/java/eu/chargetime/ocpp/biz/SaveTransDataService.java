package eu.chargetime.ocpp.biz;

import com.cdz360.base.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SaveTransDataService {

    private static final Logger logger = LoggerFactory.getLogger(SaveTransDataService.class);

    // 用于压缩JSON的ObjectMapper，禁用美化打印
    private static final ObjectMapper compactMapper = new ObjectMapper();

    private static Timer timer = new Timer();

    /**
     * 压缩JSON字符串，去除不必要的换行和空格
     *
     * @param msg 原始消息内容
     * @return 压缩后的消息内容
     */
    private static String compactJsonIfNeeded(String traceId, String msg) {
        if (msg == null) {
            return null;
        }

        // 判断是否包含JSON格式的内容（简单检测）
        String trimmed = msg.trim();
        if ((trimmed.startsWith("{") && trimmed.endsWith("}")) || (trimmed.startsWith("[")
            && trimmed.endsWith("]"))) {
            try {
                // 尝试解析并重新序列化为紧凑格式
                Object parsed = compactMapper.readValue(msg, Object.class);
                return compactMapper.writeValueAsString(parsed);
            } catch (JsonProcessingException e) {
                // 如果解析失败，直接返回原始内容
                logger.debug("[{}] 无法解析JSON内容，保持原样: {}", traceId, e.getMessage());
            }
        }

        return msg;
    }

    /**
     * 写入文件
     *
     * @param msg 需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void write(String traceId, String msg) {

        //logger.info("文件写入。traceId: {}, msg:{}", traceId, msg);

        synchronized (SaveTransDataService.timer) {
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    String pre = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS").format(new Date());
                    try {
                        TransDataFile.write(pre + msg);
                    } catch (IOException e) {
                        // write to file error
                        logger.error("[{}] write to file error: {}.", traceId, e.getMessage(), e);

                        // 重新写入
                        TransDataFile.init();
                    }
                }
            }, 0);
        }
    }

    /**
     * 桩下行
     *
     * @param msg 无需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void evseImport(String traceId, String peerAddr, String evseNo,
        String msg) {
        StringBuilder str = new StringBuilder(" [下] [");
        str.append(evseNo).append("] [");
        if (StringUtils.isNotBlank(peerAddr)) {
            str.append(peerAddr.replaceFirst("/", ""));
        } else {
            str.append(peerAddr);
        }
        str.append("] ").append(compactJsonIfNeeded(traceId, msg));
        SaveTransDataService.write(traceId, str.toString());
    }

    /**
     * 桩上行
     *
     * @param msg 无需要带上 [桩/云] [上行/下行]
     */
    public static synchronized void evseExport(String traceId, String peerAddr, String evseNo,
        String msg) {
        StringBuilder str = new StringBuilder(" [上] [");
        str.append(evseNo).append("] [");
        if (StringUtils.isNotBlank(peerAddr)) {
            str.append(peerAddr.replaceFirst("/", ""));
        } else {
            str.append(peerAddr);
        }
        str.append("] ").append(compactJsonIfNeeded(traceId, msg));
        SaveTransDataService.write(traceId, str.toString());
    }
}
