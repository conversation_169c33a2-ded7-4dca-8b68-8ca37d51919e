package eu.chargetime.ocpp.biz;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.ResourceUtils;

@Component
public class TransDataFile {

    private static final Logger logger = LoggerFactory.getLogger(TransDataFile.class);

    private static final Integer MAX_SIZE = 500 * 1000 * 1000; // 文件分片大小 500M

    private static String path = "trace_data/";

    // 标记
    private static Mark mark = null;

    private static BufferedOutputStream out = null;

    // 初始化
    static {
        init();
    }

    public static synchronized void init() {
        logger.info("init...................");
        try {
            // 加载标识文件内容
            TransDataFile.mark = new Mark();

            // 文件写句柄
            TransDataFile.out = new BufferedOutputStream(
                new FileOutputStream(TransDataFile.getPath(false), true));
        } catch (IOException e) {
            // error
            logger.info("init error: {}.", e.getMessage(), e);
        }
    }

    public static synchronized void write(String msg) throws IOException {
        // write before
        TransDataFile.before();

        if (null == TransDataFile.out) {
            TransDataFile.out = new BufferedOutputStream(
                new FileOutputStream(TransDataFile.getPath(false), true));
        }

        out.write((msg + Mark.NEW_LINE).getBytes(StandardCharsets.UTF_8));
        out.flush();
    }

    private static void flush() {
        try {
            TransDataFile.out.flush();
        } catch (IOException e) {
            // flush error
            logger.info("flush error: {}", e.getMessage(), e);
        }
    }

    private static void close() throws IOException {
        logger.info("close file.");
        if (null != TransDataFile.out) {
            TransDataFile.flush();
            TransDataFile.out.close();
            TransDataFile.out = null;
        }
    }

    private static String getPath(boolean copy) throws IOException {
        String root = ResourceUtils.getURL("*/").getPath().replaceAll("\\*", "");
        String dirPath = root + TransDataFile.path;

        // logger.debug("获取文件路径。root: {}, path: {}", root, dirPath);

        String filePath = null;
        synchronized (TransDataFile.mark) {
            filePath = root + TransDataFile.path + TransDataFile.mark.getCurrFileName(copy);
        }

        // logger.debug("file path: {}.", filePath);

        File file = new File(dirPath);
        if (!file.exists()) {
            file.mkdirs();
        }

        file = new File(filePath);
        if (!file.exists()) {
            file.createNewFile();
        }

        return filePath;
    }

    private static synchronized void before() throws IOException {
        // 更新到文件
        TransDataFile.flush();

        // 检查文件大小
        String srcFilePath = TransDataFile.getPath(false);
        File file = new File(srcFilePath);

        // 达到分片条件
        long size = file.length();
        synchronized (TransDataFile.mark) {
            if (size >= MAX_SIZE || TransDataFile.mark.isNewDay()) {
                // 关闭
                TransDataFile.close();

                String newFilePath = TransDataFile.getPath(true);
                if (!srcFilePath.equals(newFilePath)) {
                    // 重命名文件
                    FileCopyUtils.copy(file, new File(newFilePath));

                    // 删除文件
                    file.delete();
                }

                // 更新
                TransDataFile.mark.update();
            }
        }
    }

    /**
     * 获取当前正在写入的日志文件路径
     * 提供给读取服务使用
     * 
     * @return 当前日志文件的绝对路径
     */
    public static String getCurrentLogFilePath() {
        try {
            return getPath(false);
        } catch (IOException e) {
            logger.error("获取当前日志文件路径失败: {}", e.getMessage(), e);
            return null;
        }
    }

}
