package eu.chargetime.ocpp.model;

import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;

public abstract class RequestWithId implements Request {

    @Exclude
    private String ocppMessageId;

    @Exclude
    private EvseMsgBase base;

    @Override
    public String getOcppMessageId() {
        return ocppMessageId;
    }

    @Override
    public void setOcppMessageId(String requestId) {
        this.ocppMessageId = requestId;
    }

    @Override
    public EvseMsgBase getBase() {
        return base;
    }

    @Override
    public void setBase(EvseMsgBase base) {
        this.base = base;
    }

}
