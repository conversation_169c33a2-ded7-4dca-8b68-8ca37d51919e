package eu.chargetime.ocpp.model.dc.evse.protocol;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.PlugNoUtils;
import eu.chargetime.ocpp.model.dc.type.EvseMessageType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseMsgBase {

    // traceId
    private String tid;

    /**
     * 桩IP地址
     */
    private String evseIp;

    // tcp channel key
    private String chKey;

    private EvseMessageType cmdCode;

    private String evseNo;

    private int plugIdx;

    private long seq;

    private long timestamp;

    private int length;

    private String sign;

    // 收到报文的时间, nano time
    private long recvTime;

    public EvseMsgBase() {
        // default
    }

    public EvseMsgBase(String tid, String chKey) {
        this.chKey = chKey;
        this.tid = tid;
        this.recvTime = System.nanoTime();
    }

    public String getPlugNo() {
        return PlugNoUtils.formatPlugNo(this.evseNo, this.plugIdx);
    }

    public String getSeqNo4Plug() {
        StringBuilder buf = new StringBuilder();
        buf.append(this.chKey)
            .append("-")
            .append(this.seq).append("-")
            .append(this.plugIdx).append("-");
        if (this.cmdCode == null) {
            buf.append("no_cmd_cod");
        } else {
            buf.append(this.cmdCode.getCode());
        }
        return buf.toString();
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
