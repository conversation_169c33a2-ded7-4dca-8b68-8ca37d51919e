package eu.chargetime.ocpp.model.dc.type;

import lombok.Getter;

@Getter
public enum DataTransferMessage {

    UNKNOWN("unknown"),
    STATUS_AC_REQ("status_ac_req"), // 桩上报充电中数据
    STATUS_AC_CONF("status_ac_conf"),
    BILL_REQ("bill_req"), // 桩上报充电结算数据
    BILL_CONF("bill_conf"),
    RATE_REQ("rate_req"), // 平台下发费率
    RATE_CONF("rate_conf"),
    MESSAGE_TRIGGER_REQ("message_trigger_req"), // 桩请求下发费率
    MESSAGE_TRIGGER_CONF("message_trigger_conf"),
    START_TRANSACTION_AUTH_REQ("start_transaction_auth_req"), // 桩请求下发负载均衡配置
    START_TRANSACTION_AUTH_CONF("start_transaction_auth_conf"),
    QRCODE_REQ("qrcode_req"), // 平台下发二维码
    QRCODE_CONF("qrcode_conf"),
    REMOTE_START_REQ("remote_start_req"), // 远程启动充电（这种方式RCD桩端才计费）
    REMOTE_START_CONF("remote_start_conf"),
    ;

    private final String str;

    DataTransferMessage(String str) {
        this.str = str;
    }

    public static DataTransferMessage getByName(String str) {
        if (str == null) {
            return UNKNOWN;
        }
        for (DataTransferMessage msg : values()) {
            if (msg.getStr().equals(str)) {
                return msg;
            }
        }
        return UNKNOWN;
    }

}
