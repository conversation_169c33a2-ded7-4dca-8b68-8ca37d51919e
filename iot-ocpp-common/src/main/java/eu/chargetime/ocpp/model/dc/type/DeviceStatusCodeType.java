package eu.chargetime.ocpp.model.dc.type;

/**
 * @Classname DeviceStatusCodeType
 * @Description
 * @Date 5/9/2020 6:29 PM
 */
public enum DeviceStatusCodeType {
    C00(0, "正常停充"),
    C01(1, "充电机绝缘故障"),
    C02(2, "BMS绝缘"),
    C03(3, "充电机泄放电路故障"),
    C04(4, "漏电故障"),
    C05(5, "急停故障"),
    C06(6, ""),
    C07(7, ""),
    C08(8, ""),
    C09(9, ""),
    C10(10, "连接器故障(导引电路检测到故障)-枪过温"),
    C11(11, "连接器故障(导引电路检测到故障)-枪锁反馈"),
    C12(12, "连接器故障(导引电路检测到故障)-CC1信号"),
    C13(13, "充电机其他故障"),
    C14(14, "电子锁锁定错误"),
    C15(15, "K1K2外侧电压检测失败1"),
    C16(16, "K1K2外侧电压检测失败2"),
    C17(17, "预充电电压调整失败"),
    C18(18, "输出接触器故障检测"),
    C19(19, "电池反接"),
    C20(20, "充电机过温-直流模块过温/或者通讯故障"),
    C21(21, "充电机内部过温-箱体温度"),
    C22(22, "充电机电量不能传送"),
    C23(23, "充电机检测到充电电流不匹配"),
    C24(24, "充电机检测到充电电压异常"),
    C25(25, "输出短路"),
    C26(26, "主从机通讯故障"),
    C27(27, "桩体风扇故障"),
    C28(28, ""),
    C29(29, ""),
    C30(30, "输出连接器过温"),
    C31(31, "输出连接器过温-BMS元件"),
    C32(32, "连接器故障(导引电路检测到故障，充电连接器)-BMS"),
    C33(33, "电池组温度过高"),
    C34(34, "车辆连接器粘连(高压继电器)"),
    C35(35, "CC2电压检测故障"),
    C36(36, "BST报告其他故障"),
    C37(37, "BMS检测到充电电流过大"),
    C38(38, "BMS检测到充电电压异常"),
    C39(39, "电池单体电压过高"),
    C40(40, "电池单体电压过低"),
    C41(41, "SOC过高"),
    C42(42, "SOC过低"),
    C43(43, "蓄电池输出连接器状态"),
    C44(44, "BMS电池过温(BMS)"),
    C45(45, "BMS过流"),
    C46(46, "BMS绝缘"),
    C47(47, "BMS主动停充"),
    C48(48, ""),
    C49(49, ""),
    C50(50, "接收超时BRM"),
    C51(51, "接收超时BCP"),
    C52(52, "接收超时BRO"),
    C53(53, "接收超时BCS"),
    C54(54, "接收超时BCL"),
    C55(55, "接收超时BST"),
    C56(56, "接收超时BSD"),
    C57(57, "接收超时BHM/BRM"),
    C58(58, ""),
    C59(59, ""),
    C60(60, "接收超时CRM_00"),
    C61(61, "接收超时CRM_AA"),
    C62(62, "接收超时CTS，CML"),
    C63(63, "接收超时CRO"),
    C64(64, "接收超时CCS"),
    C65(65, "接收超时CST"),
    C66(66, "接收超时CSD"),
    C67(67, "其他"),
    C68(68, ""),
    C69(69, ""),
    C70(70, "烟雾报警"),
    C71(71, "水浸检测"),
    C72(72, "倾斜检测"),
    C73(73, "开门检测"),
    C74(74, "后台通讯"),
    C75(75, "屏通讯"),
    C76(76, "读卡器"),
    C77(77, "输入接触器检测"),
    C78(78, "功率分配接触器故障"),
    C79(79, "(模块)输入过欠压"),
    C80(80, "多抢，模块输入过欠压，不均流"),
    C81(81, "UI-安全管理板通讯中断"),
    C82(82, "安全管理板-UI通讯中断"),
    C83(83, "安全管理板-接触板通讯中断"),
    C84(84, "防雷器故障"),
    C85(85, "UI板或安全管理板5V跌落"),
    C86(86, "充电中桩掉电"),
    C87(87, "UI-充电控制板通讯中断"),
    C88(88, "充电控制板-UI通讯中断"),
    C89(89, "安全管理板-充电控制板通讯中断"),
    C90(90, "充电控制板-安全管理板通讯中断"),
    C91(91, "直流功率表通讯故障"),
    C92(92, "绝缘检测表通讯故障"),
    C93(93, "电池充电安全检测"),
    C94(94, "泄放电阻温度"),
    C95(95, "泄放电路驱动故障"),
    C96(96, "与BMS通讯中断(3次超时)"),
    C97(97, "充电前功率组未就绪"),
    C98(98, "BMS辅组电源电压故障"),
    C99(99, "VIN码未找到"),

    C100(100, "内存不足故障"),
    C103(103, "SD卡格式错误"),
    C104(104, "FTP模式配置失败"),
    C105(105, "RFID通信超时"),
    C106(106, "压力传感器通信超时"),
    C107(107, "摄像头通信超时"),
    C108(108, "绝缘模块检测电压失败"),
    C109(109, "电表电量数据异常"),
    C110(110, "急停处理泄放超时"),
    C111(111, "急停处理解锁超时"),
    C112(112, "正常停止处理泄放超时"),
    C113(113, "正常停止处理解锁超时"),
    C114(114, "绝缘检测电压"),
    C115(115, "绝缘检测数据"),
    C116(116, "绝缘检测报警"),
    C117(117, "防盗检测"),
    C119(119, "单枪，模块输出过欠压，不均流"),

    C130(130, "PC01板内存不足"),
    C131(131, "PDU通信超时故障"),
    C132(132, "PDU控制命令错误"),
    C133(133, "调整功率组输出电压超时"),
    C134(134, "CTT通道执行断开操作超时"),
    C135(135, "CTT通道执行闭合操作超时"),
    C136(136, "CTT通道粘连"),
    C137(137, "CTT通道驱动失效"),
    C138(138, "CTT通道其他故障"),
    C139(139, "PDU故障"),
    C140(140, "UI板写订单记录故障"),
    C141(141, "主动充电安全故障"),
    C142(142, "熔断器故障"),
    C143(143, "压力过小故障"),
    C144(144, "压力过大故障"),
    C145(145, "CP 采样板通讯故障"),
    C146(146, "电动推杆通讯故障"),

    C148(148, "BRM 报文数据异常"),
    C149(149, "BCP 报文数据异常"),
    C150(150, "接收超时 BRO_00"),
    C151(151, "接收超时 BRO_AA"),
    C157(157, "充电中有电压无电流"),
    C158(158, "车辆最高允许充电电压低于充电机最低输出电压"),
    C159(159, "电池电压过低"),
    C160(160, "电池电压过高"),
    C161(161, "K1K2后端电压大于车辆最高允许电压"),


    C1001(1001, "网关离线"),
    C1101(1101, "桩离线"),
    C1111(1111, "下发校时指令桩端返回失败"),
    C1112(1112, "电价下发桩端返回失败"),
    C1501(1501, "桩端返回启动充电失败"),
    C1502(1502, "桩端返回停止充电失败"),
    C1503(1503, "桩端返回预约充电失败"),
    C1504(1504, "充电控制板-UI通讯中断"),
    C1505(1505, "安全管理板-充电控制板通讯中断"),
    C1506(1506, "桩端刷卡(离线卡)失败"),
    C1901(1901, "下行指令发送超时"),
    C1002(1002, "新网关MAC地址重复"),
    C1003(1003, "新网关MAC地址为空\t"),
    C1120(1120, "桩端（心跳）返回故障码"),
    C1130(1130, "桩端（心跳）返回告警码"),

    C1005(1005, "交流-急停故障"),
    C1010(1010, "交流-枪高温"),
    C1012(1012, "交流-枪连接异常"),
    C1014(1014, "交流-枪锁异常"),
    C1018(1018, "交流-交流接触器"),
    C1021(1021, "交流-枪体高温"),
    C1023(1023, "交流-枪过流"),
    C1029(1029, "交流-充电电流为0"),
    C1058(1058, "交流-枪低温"),
    C1059(1059, "交流-桩温度传感器故障"),
    C1068(1068, "交流-枪温度传感器故障"),
    C1069(1069, "交流-S2开关异常"),
    C1073(1073, "交流-门禁"),
    C1074(1074, "交流-网络通讯"),
    C1075(1075, "交流-DWIN通讯故障"),
    C1076(1076, "交流-读卡器"),
    C1079(1079, "交流-交流欠压"),
    C1080(1080, "交流-交流过压"),
    C1083(1083, "交流-电表通讯"),
    C1084(1084, "交流-CP电压采样异常"),
    C1086(1086, "交流-充电中桩掉电"),

    /*=控制器告警==============================*/
    C2001(2001, "场站控制器-告警-负载告警"),
    C2002(2002, "场站控制器-告警-配电柜温度告警"),
    /*=控制器故障==============================*/
    C2501(2501, "场站控制器-故障-控制器配置信息异常"),
    C2502(2502, "场站控制器-故障-负载异常"),
    C2503(2503, "场站控制器-故障-配电柜温度异常"),

    C3600(3600, "微网控制器CPU利用率过高"),
    C3700(3700, "微网控制器硬盘利用率过高"),
    C3800(3800, "微网控制器内存利用率过高"),
    C3900(3900, "微网控制器机器温度过高"),

    /* OCPP桩故障码 */
    C5000(5000, "与车通信失败"),
    C5001(5001, "内部硬件或软件组件出错"),
    C5002(5002, "本地列表冲突"),
    C5003(5003, "过流故障"),
    C5004(5004, "过压故障"),
    C5005(5005, "无法读取电表/能量表/电能表"),
    C5006(5006, "电源开关故障"),
    C5007(5007, "idTag读取器失败"),
    C5008(5008, "重置故障"),
    C5009(5009, "欠压故障"),
    C5010(5010, "无线信号弱"),

    /* OCPP-润诚达 */
    C5200(5200, "网络异常"),
    C5201(5201, "电缆过温"),
    C5202(5202, "LED_A_丢失"),
    C5203(5203, "LED_B_丢失"),
    C5204(5204, "EMS断开"),
    C5205(5205, "避雷器告警"),
    C5206(5206, "屏幕未连接"),
    C5207(5207, "网线故障"),
    C5208(5208, "未连接服务器"),
    C5209(5209, "充电桩过温"),
    C5210(5210, "交流电表通讯故障"),
    C5211(5211, "电表故障"),
    C5212(5212, "交流输入过流报警"),
    C5213(5213, "欠压报警"),
    C5214(5214, "过压报警"),
    C5215(5215, "直流过流故障"),
    C5216(5216, "直流输出过压报警"),
    C5217(5217, "交流输入接触器故障"),
    C5218(5218, "接触器故障"),
    C5219(5219, "直连并联接触器故障"),
    C5220(5220, "电池短路故障"),
    C5221(5221, "线头过温"),
    C5222(5222, "充电枪超温故障"),
    C5223(5223, "地锁通讯故障"),
    C5224(5224, "电池反接"),
    C5225(5225, "电子锁故障"),
    C5226(5226, "液冷枪通信故障"),
    C5227(5227, "设备停用"),
    C5228(5228, "模块被占用"),
    C5229(5229, "电源模块通信故障"),
    C5230(5230, "SECC通信故障"),
    C5231(5231, "急停故障"),
    C5232(5232, "门禁控制器故障"),
    C5233(5233, "风扇故障"),
    C5234(5234, "掉电故障"),
    C5235(5235, "漏电故障"),
    C5236(5236, "烟雾报警"),
    C5237(5237, "充电桩过温故障"),
    C5238(5238, "线头过温故障"),
    C5239(5239, "水浸故障"),
    C5240(5240, "电源模块通信故障(已废弃)"),
    C5241(5241, "从机地址冲突"),
    C5242(5242, "主从通讯故障"),
    C5243(5243, "功率堆故障"),
    C5244(5244, "接地故障"),

    /*固德威逆变器故障码开始*/
    C10000(10000, "漏电流检测设备检测失败"),
    C10001(10001, "输出电流传感器检测失败"),
    C10002(10002, "TBD"), // 预留
    C10003(10003, "DC注入不一致"),
    C10004(10004, "漏电流检测不一致"),
    C10005(10005, "TBD"), // 预留
    C10006(10006, "漏电流检测设备故障"),
    C10007(10007, "继电器故障"),
    C10008(10008, "输出电流传感器故障"),
    C10009(10009, "交流侧无电压"),
    C10010(10010, "接地失效"),
    C10011(10011, "直流总线电压过高"),
    C10012(10012, "内部风扇故障"),
    C10013(10013, "过温告警"),
    C10014(10014, "自动检测失败"),
    C10015(10015, "光伏过压"),
    C10016(10016, "外部风扇故障"),
    C10017(10017, "VAC失效"),
    C10018(10018, "对地绝缘阻抗失效"),
    C10019(10019, "DC注入过高"),
    C10020(10020, "TBD"), // 预留
    C10021(10021, "TBD"), // 预留
    C10022(10022, "FAC不一致"),
    C10023(10023, "VAC不一致"),
    C10024(10024, "TBD"), // 预留
    C10025(10025, "继电器检测失败"),
    C10026(10026, "TBD"), // 预留
    C10027(10027, "TBD"), // 预留
    C10028(10028, "TBD"), // 预留
    C10029(10029, "FAC失效"),
    C10030(10030, "EEPROM读写失败"),
    C10031(10031, "内部通讯失败"),
    /*固德威逆变器故障码结束*/

    /*欣顿逆变器故障码开始*/
//    C10100(10100, "无"),
    C10101(10101, "电池过压"),
    C10102(10102, "电池过压告警"),
    C10103(10103, "电池低压"),
    // TODO:欣顿逆变器故障码目前没用到

    /*华为逆变器故障码开始*/
    C10200(10200, "输入组串电压高"),
    C10201(10201, "直流电弧故障[1]"),
    C10202(10202, "组串反接"),
    C10203(10203, "组串电流反灌"),
    C10204(10204, "组串功率异常"),
    C10205(10205, "AFCI 自检失败"),
    C10206(10206, "电网相线对PE短路"),
    C10207(10207, "电网掉电"),
    C10208(10208, "电网欠压"),
    C10209(10209, "电网过压"),
    C10210(10210, "电网电压不平衡"),
    C10211(10211, "电网过频"),
    C10212(10212, "电网欠频"),
    C10213(10213, "电网频率不稳定"),
    C10214(10214, "输出过流"),
    C10215(10215, "输出电流直流分量过大"),
    C10216(10216, "残余电流异常"),
    C10217(10217, "系统接地异常"),
    C10218(10218, "绝缘阻抗低"),
    C10219(10219, "温度过高"),
    C10220(10220, "设备异常"),
    C10221(10221, "升级失败或版本不匹配"),
    C10222(10222, "License 到期"),
    C10223(10223, "监控单元故障"),
    C10224(10224, "功率采集器故障[2]"),
    C10225(10225, "储能设备异常"),
    C10226(10226, "主动孤岛"),
    C10227(10227, "被动孤岛"),
    C10228(10228, "瞬时交流过压"),
    C10229(10229, "外部设备端口短路"),
    C10230(10230, "离网输出过载"),
    C10231(10231, "电池板配置异常"),
    C10232(10232, "优化器故障"),
    C10233(10233, "内置PID工作异常"),
    C10234(10234, "输入组串对地电压高"),
    C10235(10235, "外部风扇异常"),
    C10236(10236, "储能反接"),
    C10237(10237, "并离网控制器异常"),
    C10238(10238, "组串丢失"),
    C10239(10239, "内部风扇异常"),
    C10240(10240, "直流保护单元异常"),
    /*华为逆变器故障码结束*/

    /**
     * 辐射仪告警开始
     **/
    C20000(20000, "辐射仪离线"),
    /**辐射仪告警结束**/


    /**
     * SIM卡告警开始
     **/
    C30000(30000, "SIM卡状态异常"),
    /**SIM卡告警结束**/

    /**
     * 永联OCPP桩欧日标故障码 START
     * <p>40000 ~ 90000</p>
     */
    C4_1002(40000 + 0x1002, "余额不足"),
    C4_1003(40000 + 0x1003, "刷卡停止"),
    C4_1004(40000 + 0x1004, "OCPP远程停止"),
    C4_1005(40000 + 0x1005, "达到设置充电时长停止"),
    C4_1006(40000 + 0x1006, "达到设置充电电量停止"),
    C4_1007(40000 + 0x1007, "达到设置充电金额停止"),
    C4_1008(40000 + 0x1008, "屏上主动点击停止"),
    C4_1009(40000 + 0x1009, "无有效电流停止"),
    C4_100B(40000 + 0x100B, "车辆主动正常停止"),
    C4_2008(40000 + 0x2008, "后台鉴权失败"),
    C4_3002(40000 + 0x3002, "门禁故障"),
    C4_3003(40000 + 0x3003, "急停故障"),
    C4_3004(40000 + 0x3004, "风扇故障"),
    C4_3007(40000 + 0x3007, "刷卡器通讯故障"),
    C4_300A(40000 + 0x300A, "模块故障"),
    C4_300D(40000 + 0x300D, "交流电表通讯故障"),
    C4_300E(40000 + 0x300E, "交流电表数据异常故障"),
    C4_300F(40000 + 0x300F, "输出接触器粘连"),
    C4_3010(40000 + 0x3010, "直流接触器故障"),
    C4_3011(40000 + 0x3011, "直流熔断器故障"),
    C4_3014(40000 + 0x3014, "绝缘监测故障"),
    C4_3015(40000 + 0x3015, "泄放回路故障"),
    C4_3016(40000 + 0x3016, "充电桩过温报警"),
    C4_3017(40000 + 0x3017, "充电接口过温保护"),
    C4_301A(40000 + 0x301A, "充电桩内部通讯故障(CCM-PCM)"),
    C4_301B(40000 + 0x301B, "充电中控制导引故障"),
    C4_301D(40000 + 0x301D, "充电桩其他故障"),
    C4_302A(40000 + 0x302A, "交流接触器故障"),
    C4_302C(40000 + 0x302C, "自检功率分配超时"),
    C4_302D(40000 + 0x302D, "母联接触器故障"),
    C4_302E(40000 + 0x302E, "预充完成超时"),
    C4_302F(40000 + 0x302F, "启动充电超时"),
    C4_3031(40000 + 0x3031, "模块开机超时"),
    C4_3032(40000 + 0x3032, "TCU-CCM通讯故障"),
    C4_303C(40000 + 0x303C, "预充功率分配超时"),
    C4_303E(40000 + 0x303E, "功率控制器系统故障"),
    C4_3044(40000 + 0x3044, "MCU-SECC通讯故障"),
    C4_3046(40000 + 0x3046, "MCU启动应答失败(启动完成、停止完成)"),
    C4_3047(40000 + 0x3047, "MCU和TCU状态不一致"),
    C4_3048(40000 + 0x3048, "TCU自检总超时"),
    C4_3049(40000 + 0x3049, "出风口温感失效故障"),
    C4_304A(40000 + 0x304A, "枪温失效"),
    C4_3053(40000 + 0x3053, "液冷高压报警"),
    C4_3054(40000 + 0x3054, "液冷低压报警"),
    C4_3055(40000 + 0x3055, "液冷低液位报警"),
    C4_3056(40000 + 0x3056, "液冷循环泵过流"),
    C4_3057(40000 + 0x3057, "液冷循环泵欠压"),
    C4_3058(40000 + 0x3058, "液冷循环泵过压"),
    C4_3059(40000 + 0x3059, "液冷循环泵过温"),
    C4_305a(40000 + 0x305a, "液冷循环泵堵转"),
    C4_305b(40000 + 0x305b, "液冷循环泵通讯故障"),
    C4_305c(40000 + 0x305c, "液冷低流量报警"),
    C4_305d(40000 + 0x305d, "液冷高流量报警"),
    C4_305e(40000 + 0x305e, "液冷风机过流"),
    C4_305f(40000 + 0x305f, "液冷机通讯故障"),
    C4_3070(40000 + 0x3070, "绝缘值变化过大"),
    C4_3080(40000 + 0x3080, "插枪次数过多告警"),
    C4_3081(40000 + 0x3081, "插枪次数过多故障"),
    C4_3082(40000 + 0x3082, "绝缘斜率告警"),
    C4_3083(40000 + 0x3083, "枪温斜率告警"),
    C4_3084(40000 + 0x3084, "枪不可用状态"),
    C4_4001(40000 + 0x4001, "输出电压过压故障"),
    C4_4002(40000 + 0x4002, "输出电流过流故障"),
    C4_4004(40000 + 0x4004, "输出短路故障"),
    C4_4005(40000 + 0x4005, "BMS握手超时故障"),
    C4_4006(40000 + 0x4006, "直流接触器外部电压大于10V"),
    C4_400C(40000 + 0x400C, "交流防雷告警"),
    C4_5001(40000 + 0x5001, "BCP充电参数配置报文超时"),
    C4_5002(40000 + 0x5002, "BRO充电准备就绪报文超时"),
    C4_5003(40000 + 0x5003, "BCS电池充电状态报文超时"),
    C4_5004(40000 + 0x5004, "BCL电池充电需求报文超时"),
    C4_5007(40000 + 0x5007, "BSM动力蓄电池状态报文超时"),
    C4_5008(40000 + 0x5008, "BMS故障_BmsBRO超时"),
    C4_5009(40000 + 0x5009, "车辆BHM通信超时"),
    C4_500A(40000 + 0x500A, "BRM车辆识别报文超时"),
    C4_5011(40000 + 0x5011, "BMS其他故障"),
    C4_501A(40000 + 0x501A, "电池反接故障"),
    C4_502B(40000 + 0x502B, "车辆未就绪"),
    C4_502C(40000 + 0x502C, "电池电压异常"),
    C4_503C(40000 + 0x503C, "电池最高电压低于站端最小输出电压"),
    C4_503D(40000 + 0x503D, "电池当前电压低于站端最小输出电压"),
    C4_503e(40000 + 0x503e, "电池电压高于站端最大输出电压"),
    C4_503F(40000 + 0x503F, "电池电压高于BCP允许的最大充电电压"),
    C4_5043(40000 + 0x5043, "车辆电压需求高于车辆最大充电电压"),
    C4_5510(40000 + 0x5510, "[CCS]:GQ 初始化错误-一般性错误）"),
    C4_5511(40000 + 0x5511, "[CCS]:GQ 初始化错误-地址问题"),
    C4_5512(40000 + 0x5512, "[CCS]:GQ 初始化错误-线程"),
    C4_5513(40000 + 0x5513, "[CCS]:GQ 初始化错误-打开通道"),
    C4_5514(40000 + 0x5514, "[CCS]:GQ 初始化错误-KEY"),
    C4_5520(40000 + 0x5520, "[CCS]:GQ SLAC-一般错误"),
    C4_5521(40000 + 0x5521, "[CCS]:GQ SLAC错误-计时器初始化"),
    C4_5522(40000 + 0x5522, "[CCS]:GQ SLAC错误-计时器超时"),
    C4_5523(40000 + 0x5523, "[CCS]:GQ SLAC错误-计时器选项"),
    C4_5524(40000 + 0x5524, "[CCS]:GQ SLAC错误-参数超时"),
    C4_5525(40000 + 0x5525, "[CCS]:GQ SLAC错误-参数连接"),
    C4_5526(40000 + 0x5526, "[CCS]:GQ SLAC错误-开始计算衰减字符超时"),
    C4_5527(40000 + 0x5527, "[CCS]:GQ SLAC错误-MNBC超时"),
    C4_5528(40000 + 0x5528, "[CCS]:GQ SLAC错误-衰减字符超时"),
    C4_5529(40000 + 0x5529, "[CCS]:GQ SLAC错误-衰减字符连接"),
    C4_552A(40000 + 0x552A, "[CCS]:GQ SLAC错误-验证超时"),
    C4_552B(40000 + 0x552B, "[CCS]:GQ SLAC错误-SOCKET-1接口验证"),
    C4_552C(40000 + 0x552C, "[CCS]:GQ SLAC错误-验证超时"),
    C4_552D(40000 + 0x552D, "[CCS]:GQ SLAC错误-SOCKET-2接口验证"),
    C4_552E(40000 + 0x552E, "[CCS]:GQ SLAC错误-BCB切换超时"),
    C4_552F(40000 + 0x552F, "[CCS]:GQ SLAC错误-参数匹配超时"),
    C4_5530(40000 + 0x5530, "[CCS]:GQ SLAC错误-SOCKET匹配"),
    C4_5531(40000 + 0x5531, "[CCS]:GQ SLAC错误-SOCKET读取"),
    C4_5532(40000 + 0x5532, "[CCS]:GQ SLAC错误-设置KEY"),
    C4_5540(40000 + 0x5540, "[CCS]:GQ SDP一般性错误"),
    C4_5541(40000 + 0x5541, "[CCS]:GQ SDP错误-SOCKET初始化"),
    C4_5542(40000 + 0x5542, "[CCS]:GQ SDP错误-初始化SOCKOPT1"),
    C4_5543(40000 + 0x5543, "[CCS]:GQ SDP错误-初始化SOCKOPT2"),
    C4_5544(40000 + 0x5544, "[CCS]:GQ SDP错误-初始化绑定"),
    C4_5545(40000 + 0x5545, "[CCS]:GQ SDP错误-线程套接字1"),
    C4_5546(40000 + 0x5546, "[CCS]:GQ SDP错误-线程套接字2"),
    C4_5547(40000 + 0x5547, "[CCS]:GQ SDP错误-超时"),
    C4_5548(40000 + 0x5548, "[CCS]:GQ DIN-硬件线路连接异常"),
    C4_5550(40000 + 0x5550, "[CCS]:GQ DIN-一般错误"),
    C4_5551(40000 + 0x5551, "[CCS]:GQ DIN错误-SOCKET初始化"),
    C4_5552(40000 + 0x5552, "[CCS]:GQ DIN错误-初始化套接字"),
    C4_5553(40000 + 0x5553, "[CCS]:GQ DIN错误-初始化绑定"),
    C4_5554(40000 + 0x5554, "[CCS]:GQ DIN错误-初始化监听"),
    C4_5555(40000 + 0x5555, "[CCS]:GQ DIN错误-初始化选择"),
    C4_5556(40000 + 0x5556, "[CCS]:GQ DIN错误-初始化接收"),
    C4_5557(40000 + 0x5557, "[CCS]:GQ DIN错误-通讯超时"),
    C4_5558(40000 + 0x5558, "[CCS]:GQ DIN错误-V2GTP标头"),
    C4_5559(40000 + 0x5559, "[CCS]:GQ DIN错误-V2GTP标头长度"),
    C4_555A(40000 + 0x555A, "[CCS]:GQ DIN错误-EXI解码"),
    C4_555B(40000 + 0x555B, "[CCS]:GQ DIN错误-创建响应失败"),
    C4_555C(40000 + 0x555C, "[CCS]:GQ DIN错误-EXI编码"),
    C4_555D(40000 + 0x555D, "[CCS]:GQ DIN错误-V2GTP标头写入"),
    C4_555E(40000 + 0x555E, "[CCS]:GQ DIN错误-套接字异常"),
    C4_555F(40000 + 0x555F, "[CCS]:GQ DIN错误-发送连接失败"),
    C4_5560(40000 + 0x5560, "[CCS]:GQ DIN错误-没有兼容的充电协议"),
    C4_5601(40000 + 0x5601, "[CCS/CHAdeMo]:EV充电失败-RESS温度限制"),
    C4_5602(40000 + 0x5602, "[CCS/CHAdeMo]:EV充电失败-EV换挡位置错误"),
    C4_5603(40000 + 0x5603, "[CCS/CHAdeMo]:EV充电失败-充电器连接器锁定故障"),
    C4_5604(40000 + 0x5604, "EV充电失败-EVRESS故障"),
    C4_5605(40000 + 0x5605, "[CCS/CHAdeMo]:EV充电失败-充电电流差异常"),
    C4_5606(40000 + 0x5606, "[CCS/CHAdeMo]:EV充电失败-电压超出范围"),
    C4_5607(40000 + 0x5607, "[CCS/CHAdeMo]:EV预留-A"),
    C4_5608(40000 + 0x5608, "[CCS/CHAdeMo]:EV预留-B"),
    C4_5609(40000 + 0x5609, "[CCS/CHAdeMo]:EV预留-C"),
    C4_5610(40000 + 0x5610, "[CCS/CHAdeMo]:EV充电失败-充电系统不兼容"),
    C4_5611(40000 + 0x5611, "[CCS/CHAdeMo]:EV无数据"),
    C4_5612(40000 + 0x5612, "[CCS/CHAdeMo]:EV充电失败-SECC导致EVRESS故障"),
    C4_5613(40000 + 0x5613, "[CCS/CHAdeMo]:EV充电失败-电压超出范围（SECC）"),
    C4_5614(40000 + 0x5614, "[CCS/CHAdeMo]:EV正常停止请求-充电前车辆正常停止请求"),
    C4_5615(40000 + 0x5615, "[CCS/CHAdeMo]:EVCAN数据超时"),
    C4_560A(40000 + 0x560A, "[CCS/CHAdeMo]:EV充电失败-充电系统不兼容"),
    C4_560B(40000 + 0x560B, "[CCS/CHAdeMo]:EV无数据"),
    C4_560C(40000 + 0x560C, "[CCS/CHAdeMo]:EV充电失败-SECC导致EVRESS故障"),
    C4_560D(40000 + 0x560D, "[CCS/CHAdeMo]:EV充电失败-电压超出范围(SECC)"),
    C4_560E(40000 + 0x560E, "[CCS/CHAdeMo]:EV正常停止请求-充电前由EVCC发起"),
    C4_560F(40000 + 0x560F, "[CCS/CHAdeMo]:EV CAN数据超时"),
    C4_5650(40000 + 0x5650, "[CCS/CHAdeMo]:EVSE 能力超时"),
    C4_5651(40000 + 0x5651, "[CCS/CHAdeMo]:EVSE 充电信息超时"),
    C4_5652(40000 + 0x5652, "[CCS/CHAdeMo]:EVSE 校验超时"),
    C4_5653(40000 + 0x5653, "[CCS/CHAdeMo]:EVSE 请求信息超时"),
    C4_5654(40000 + 0x5654, "[CCS/CHAdeMo]:EVSE 主动请求停止"),
    C4_800A(40000 + 0x800A, "CCU停止-正常"),
    C4_800B(40000 + 0x800B, "CCU停止-紧急"),
    C4_800C(40000 + 0x800C, "车辆剩余时间为0-正常停止"),
    C4_800D(40000 + 0x800D, "车辆充电禁用-正常停止"),
    C4_800E(40000 + 0x800E, "车辆请求切换到0-正常停止"),
    C4_800F(40000 + 0x800F, "车辆充电前-正常停止"),
    C4_8010(40000 + 0x8010, "车辆充电-充电权限禁用"),
    C4_8011(40000 + 0x8011, "CCS2行程-电动车完成停止请求"),
    C4_8012(40000 + 0x8012, "CCS2行程-SOC满停止请求"),
    C4_8013(40000 + 0x8013, "CHAdeMo 锁定错误"),
    C4_8014(40000 + 0x8014, "CHAdeMo 启动未插入超时"),
    C4_8015(40000 + 0x8015, "CHAdeMo 等待通信超时"),
    C4_8016(40000 + 0x8016, "CHAdeMo 车辆换挡位置错误"),
    C4_8017(40000 + 0x8017, "CHAdeMo 通信-车辆参数更新超时"),
    C4_8018(40000 + 0x8018, "CHAdeMo 通信-请求电压高于最大电压"),
    C4_8019(40000 + 0x8019, "CHAdeMo 通信-请求电压低于可用电压"),
    C4_801A(40000 + 0x801A, "CHAdeMo 电动车允许充电-超时"),
    C4_801B(40000 + 0x801B, "CHAdeMo 等待EVSE自检通过-超时"),
    C4_801C(40000 + 0x801C, "CHAdeMo CP3充电前-充电权限错误"),
    C4_801D(40000 + 0x801D, "CHAdeMo 102.5充电位前错误"),
    C4_801E(40000 + 0x801E, "CHAdeMo 预充电等待EV接触器-超时"),
    C4_801F(40000 + 0x801F, "CHAdeMo 预充电等待EV请求电流-超时"),
    C4_8020(40000 + 0x8020, "CHAdeMo 车辆过电压错误"),
    C4_8021(40000 + 0x8021, "CHAdeMo 车辆欠电压错误"),
    C4_8022(40000 + 0x8022, "CHAdeMo 车辆电流差异错误"),
    C4_8023(40000 + 0x8023, "CHAdeMo 车辆温度错误"),
    C4_8024(40000 + 0x8024, "CHAdeMo 车辆电压差异错误"),
    C4_8025(40000 + 0x8025, "CHAdeMo 车辆通用错误"),
    C4_8026(40000 + 0x8026, "CHAdeMo 充电权限禁用"),
    C4_8027(40000 + 0x8027, "CHAdeMo EVSE超时错误"),
    C4_8028(40000 + 0x8028, "CHAdeMo EV超时100,101,102错误"),
    C4_8029(40000 + 0x8029, "CCS2 快速保护CPilot检测"),
    C4_802A(40000 + 0x802A, "CCS2 SLAC错误"),
    C4_802B(40000 + 0x802B, "CCS2 会话错误"),
    C4_802C(40000 + 0x802C, "CCS2 EVSE超时错误"),
    C4_802D(40000 + 0x802D, "CCS2 SLAC检测检查超时"),
    C4_802E(40000 + 0x802E, "CCS2 启动未插入超时"),
    C4_802F(40000 + 0x802F, "CCS2 等待网络初始化完成超时"),
    C4_8030(40000 + 0x8030, "CCS2 等待SDP超时"),
    C4_8031(40000 + 0x8031, "CCS2 等待SAP超时"),
    C4_8032(40000 + 0x8032, "CCS2 SLAC静态设置密钥超时"),
    C4_8033(40000 + 0x8033, "CCS2 SLAC等待CM_SLAC_PARM_REQ超时"),
    C4_8034(40000 + 0x8034, "CCS2 SLAC等待ATTEN_CHAR_IND超时"),
    C4_8035(40000 + 0x8035, "CCS2 SLAC等待ATTEN_CHAR_RSP超时"),
    C4_8036(40000 + 0x8036, "CCS2 SLAC等待MATCH或VALIDATE超时"),
    C4_8037(40000 + 0x8037, "CCS2 SLAC等待LinkDetected超时"),
    C4_8038(40000 + 0x8038, "CCS2 服务类别标志错误"),
    C4_8039(40000 + 0x8039, "CCS2 选定支付选项错误"),
    C4_803A(40000 + 0x803A, "CCS2 生成挑战错误"),
    C4_803B(40000 + 0x803B, "CCS2 AC电动车充电参数错误"),
    C4_803C(40000 + 0x803C, "CCS2 物理值倍数错误"),
    C4_803D(40000 + 0x803D, "CCS2 物理值单位错误"),
    C4_803E(40000 + 0x803E, "CCS2 请求的能源传输类型错误"),
    C4_803F(40000 + 0x803F, "CCS2 电动车最大电压低于二次最小电压错误"),
    C4_8040(40000 + 0x8040, "CCS2 电动车最大电流低于二次最小电流错误"),
    C4_8041(40000 + 0x8041, "CCS2 电动车请求电压低于二次最小电压错误"),
    C4_8042(40000 + 0x8042, "CCS2 电动车请求电压高于二次最大电压错误"),
    C4_8043(40000 + 0x8043, "CCS2 电动车请求电压高于电动车最大电压错误"),
    C4_8044(40000 + 0x8044, "CCS2 电动车请求电流高于电动车最大电流错误"),
    C4_8045(40000 + 0x8045, "CCS2 会话未知错误"),
    C4_8046(40000 + 0x8046, "CCS2 CP错误"),
    C4_8047(40000 + 0x8047, "CCS2 无DC电动车电源传递参数错误"),
    C4_8048(40000 + 0x8048, "CCS2 充电配置文件无效错误"),
    C4_8049(40000 + 0x8049, "CCS2 电动车关闭Socket错误"),
    C4_804A(40000 + 0x804A, "CCS2 序列超时设置计时器错误"),
    C4_804B(40000 + 0x804B, "CCS2 序列超时应用会话错误"),
    C4_804C(40000 + 0x804C, "CCS2 CP状态检测超时错误"),
    C4_804D(40000 + 0x804D, "CCS2 电动车电池温度错误"),
    C4_804E(40000 + 0x804E, "CCS2 电动车换挡位置错误"),
    C4_804F(40000 + 0x804F, "CCS2 电动车连接器锁定错误"),
    C4_8050(40000 + 0x8050, "CCS2 电动车电池故障错误"),
    C4_8051(40000 + 0x8051, "CCS2 电动车电流差异错误"),
    C4_8052(40000 + 0x8052, "CCS2 电动车电压超出范围错误"),
    C4_8053(40000 + 0x8053, "CCS2 电动车系统不兼容错误"),
    C4_8054(40000 + 0x8054, "CCS2 电动车未知错误"),
    C4_8055(40000 + 0x8055, "CCS2 服务ID无效错误"),
    C4_8056(40000 + 0x8056, "通信超时启动充电"),
    C4_8057(40000 + 0x8057, "通信超时参数同步"),
    C4_8058(40000 + 0x8058, "通信超时自检完成"),
    C4_8059(40000 + 0x8059, "通信超时充电参数"),
    C4_805A(40000 + 0x805A, "通信超时停止充电"),
    C4_9011(40000 + 0x9011, "达到后台设置SOC停止"),
    /**
     * <p>40000 ~ 90000</p>
     * 永联OCPP桩欧日标故障码 END
     */


    /**
     * 储能设备故障码 START
     * <p>规则：(300 + 设备类型ID前两位) * 100000 + 设备对应告警表序号(三位) * 100 + bit位</p>
     */

    // BMS (tab20-1)
    C31020102(31020102, "单体温差"),
    C31020104(31020104, "充电过流"),
    C31020106(31020106, "放电过流"),
    C31020107(31020107, "极柱过温"),
    C31020108(31020108, "单体过压"),
    C31020109(31020109, "单体压差"),
    C31020110(31020110, "放电低温"),
    C31020111(31020111, "低压关机"),
    C31020112(31020112, "单体欠压"),
    C31020113(31020113, "ISO通讯故障 "),
    C31020114(31020114, "LMU SN 重复"),
    C31020115(31020115, "BMU SN 重复"),
    C31020116(31020116, "绝缘故障"),
    C31020117(31020117, "LMU通讯故障"),
    C31020118(31020118, "单体过温"),
    C31020119(31020119, "BMU通讯故障"),
    C31020120(31020120, "INV通讯故障"),
    C31020121(31020121, "充电低温"),
    C31020122(31020122, "TOPBMU 通讯故障"),
    C31020123(31020123, "总压异常"),
    C31020124(31020124, "线束故障"),
    C31020125(31020125, "簇切除故障"),
    C31020126(31020126, "继电器故障"),
    C31020131(31020131, "温度传感器故障"),

    // EMS (tab10-1)
    C32010100(32010100, "PCS通讯丢失"),
    C32010101(32010101, "电网关口电表丢失"),
    C32010102(32010102, "BMS通讯丢失"),

    C32010104(32010104, "光伏关口电表通讯丢失"),

    C32010107(32010107, "SD卡丢失"),

    C32010117(32010117, "光伏逆变器通讯丢失"),
    C32010118(32010118, "油机和光伏冲突"),
    C32010119(32010119, "光伏逆变器故障"),
    C32010120(32010120, "空调故障"),
    C32010121(32010121, "消防故障"),
    C32010122(32010122, "消防控制器故障"),
    C32010123(32010123, "油机故障"),
    C32010124(32010124, "空调通讯丢失"),
    C32010125(32010125, "过流故障"),
    C32010126(32010126, "PCS工作模式错误"),
    C32010127(32010127, "电池能量低故障"),
    C32010128(32010128, "充电桩电表通讯丢失"),
    C32010129(32010129, "储能并网点电表通讯丢失"),


    // PCS中DCAC故障信息(tab40-1)
    C32140100(32140100, "AC模块组：交流母线过压"),
    C32140101(32140101, "AC模块组：交流母线过频"),
    C32140102(32140102, "AC模块组：交流母线欠压"),
    C32140103(32140103, "AC模块组：逆变器孤岛保护"),
    C32140104(32140104, "AC模块组：直流输入过压"),
    C32140105(32140105, "AC模块组：并离网切换错误"),
    C32140106(32140106, "AC模块组：交流反序"),
    C32140107(32140107, "AC模块组：直流输入欠压"),
    C32140108(32140108, "AC模块组：过载报警"),
    C32140109(32140109, "AC模块组：交流母线电压异常"),
    C32140110(32140110, "AC模块组：交流缺相"),
    C32140111(32140111, "AC模块组：交流母线电压不平衡"),
    C32140112(32140112, "AC模块组：交流母线欠频"),
    C32140113(32140113, "AC模块组：电池电量不足"),
    C32140114(32140114, "AC模块组：直流输入过流"),
    C32140115(32140115, "AC模块组：离网电压反序"),
    C32140116(32140116, "AC模块组：锁相失败"),
    C32140117(32140117, "AC模块组：环境温度过温"),
    C32140118(32140118, "AC模块组：环温探头故障"),
    C32140119(32140119, "AC模块组：柜温探头故障"),
    C32140120(32140120, "AC模块组：机柜温度过温"),
    C32140121(32140121, "AC模块组：离网电压缺相"),


    // DCAC故障信息表2(tab40-2)
    C32140200(32140200, "AC模块组: 24V辅助电源故障"),
    C32140201(32140201, "AC模块组: 紧急停机"),
    C32140202(32140202, "AC模块组: 接地故障"),
    C32140203(32140203, "AC模块组: 直流母线过压"),
    C32140204(32140204, "AC模块组: 模块温度过温"),
    C32140205(32140205, "AC模块组: 模块电流不平衡"),
    C32140206(32140206, "AC模块组: 风扇故障"),
    C32140207(32140207, "AC模块组: 直流继电器开路"),
    C32140208(32140208, "AC模块组: 校准参数异常"),
    C32140209(32140209, "AC模块组: 母线电压不平衡"),
    C32140210(32140210, "AC模块组: 保险故障"),
    C32140211(32140211, "AC模块组: DSP初始化故障"),
    C32140212(32140212, "AC模块组: 直流软启动失败"),
    C32140213(32140213, "AC模块组: CANA通讯故障"),
    C32140214(32140214, "AC模块组: 直流输入反接"),
    C32140215(32140215, "AC模块组: 交流电流直流分量异常"),
    C32140216(32140216, "AC模块组: 变压器过温"),
    C32140217(32140217, "AC模块组: U2通信异常2"),
    C32140218(32140218, "AC模块组: BMS故障或直流开关断开"),
    C32140219(32140219, "AC模块组: 防雷器故障"),
    C32140220(32140220, "AC模块组: 过载超时故障"),
    C32140221(32140221, "AC模块组: 交流软启动失败"),
    C32140222(32140222, "AC模块组: 同步信号故障1"),
    C32140223(32140223, "AC模块组: DSP版本故障"),
    C32140224(32140224, "AC模块组: 交流继电器开路"),
    C32140225(32140225, "AC模块组: 采样零点异常"),
    C32140226(32140226, "AC模块组: U2通信异常1"),
    C32140227(32140227, "AC模块组: 15V辅助电源故障"),
    C32140228(32140228, "AC模块组: 模块重号故障"),
    C32140229(32140229, "AC模块组: RS485通讯故障"),
    C32140230(32140231, "AC模块组: CANB通讯故障"),
    C32140231(32140231, "AC模块组: 重启过多"),


    // PCS中DCAC故障(tab40-3)
    C32140300(32140300, "AC模块组: CPLD版本故障"),
    C32140301(32140301, "AC模块组: 硬件版本故障"),
    C32140302(32140302, "AC模块组: 直流继电器短路"),
    C32140303(32140303, "AC模块组: 直流母线欠压"),
    C32140304(32140304, "AC模块组: 交流继电器短路"),
    C32140305(32140305, "AC模块组: 同步信号故障2"),
    C32140306(32140306, "AC模块组: 参数不匹配"),
    C32140307(32140307, "AC模块组: CANC通讯故障"),
    C32140308(32140308, "AC模块组: 环境湿度过高"),
    C32140309(32140309, "AC模块组: BMS电压异常"),
    C32140310(32140310, "AC模块组: BMS电流异常"),
    C32140311(32140311, "AC模块组: BMS温度异常"),
    C32140312(32140312, "AC模块组: BMS跳机"),
    C32140313(32140313, "AC模块组: 绝缘检测异常"),
    C32140314(32140314, "AC模块组: 硬件采样异常"),
    C32140315(32140315, "AC模块组: 远程通信丢失"),


    // STS (tab30-1)
    C32230100(32230100, "STS: 电网电压反序"),
    C32230101(32230101, "STS: 电网电压缺相"),
    C32230102(32230102, "STS: 离网电压反序"),
    C32230103(32230103, "STS: 离网电压缺相"),
    C32230104(32230104, "STS: 校准参数异常"),
    C32230105(32230105, "STS: 采样零点异常"),
    C32230106(32230106, "STS: 过载报警"),
    C32230107(32230107, "STS: 环境温度过温"),
    C32230108(32230108, "STS: 锁相失败"),
    C32230109(32230109, "STS: 电网电压不平衡"),
    C32230110(32230110, "STS: 电网侧欠压"),
    C32230111(32230111, "STS: 电网侧过压"),
    C32230112(32230112, "STS: 电网侧欠频"),
    C32230113(32230113, "STS: 电网侧过频"),
    C32230114(32230114, "STS: 频繁切换故障"),
    C32230115(32230115, "STS: 电网掉电"),
    C32230116(32230116, "STS: 紧急停机"),
    C32230117(32230117, "STS: 15V辅助电源故障"),
    C32230118(32230118, "STS: 24V辅助电源故障"),
    C32230119(32230119, "STS: CANA通讯故障"),
    C32230120(32230120, "STS: CANB通讯故障"),
    C32230121(32230121, "STS: RS-485通讯故障"),
    C32230122(32230122, "STS: DSP初始化故障"),
    C32230123(32230123, "STS: 并网空开开路"),
    C32230124(32230124, "STS: 并网空开短路"),
    C32230125(32230125, "STS: 过载超时故障"),
    C32230126(32230126, "STS: 同步信号1故障"),
    C32230127(32230127, "STS: 环温探头故障"),
    C32230128(32230128, "STS: 柜温探头故障"),
    C32230129(32230129, "STS: 机柜过温故障"),
    C32230130(32230130, "STS: 模块温度过温"),
    C32230131(32230131, "STS: 风扇故障"),


    // STS (tab30-2)
    C32230200(32230200, "STS: DSP版本故障"),
    C32230201(32230201, "STS: CPLD版本故障"),
    C32230202(32230202, "STS: 旁路故障"),
    C32230203(32230203, "STS: 防雷器故障"),
    C32230204(32230204, "STS: 模块温度探头故障"),
    C32230205(32230205, "STS: 电网电压畸变"),


    // BatteryStack中BMS故障(tab20-1)
    C33020102(33020102, "单体温差"),
    C33020104(33020104, "充电过流"),
    C33020106(33020106, "放电过流"),
    C33020107(33020107, "级柱过温"),
    C33020108(33020108, "单体过压"),
    C33020109(33020109, "单体压差"),
    C33020110(33020110, "放电低温"),
    C33020112(33020112, "单体欠压"),
    C33020116(33020116, "绝缘故障"),
    C33020117(33020117, "LMU通讯故障"),
    C33020118(33020118, "单体过温"),
    C33020119(33020119, "BMU通讯故障"),
    C33020121(33020121, "充电低温"),
    C33020123(33020123, "总压异常"),
    C33020124(33020124, "线束故障"),
    C33020126(33020126, "继电器故障"),
    C33020131(33020131, "温度传感器故障"),


    // DCAC (tab40-1)
    C37040100(37040100, "AC模块组: 交流母线过压"),
    C37040101(37040101, "AC模块组: 交流母线过频"),
    C37040102(37040102, "AC模块组: 交流母线欠压"),
    C37040103(37040103, "AC模块组: 逆变器孤岛保护"),
    C37040104(37040104, "AC模块组: 直流输入过压"),
    C37040105(37040105, "AC模块组: 并离网切换错误"),
    C37040106(37040106, "AC模块组: 交流反序"),
    C37040107(37040107, "AC模块组: 直流输入欠压"),
    C37040108(37040108, "AC模块组: 过载报警"),
    C37040109(37040109, "AC模块组: 交流母线电压异常"),
    C37040110(37040110, "AC模块组: 交流缺相"),
    C37040111(37040111, "AC模块组: 交流母线电压不平衡"),
    C37040112(37040112, "AC模块组: 交流母线欠频"),
    C37040113(37040113, "AC模块组: 电池电量不足"),
    C37040114(37040114, "AC模块组: 直流输入过流"),
    C37040115(37040115, "AC模块组: 离网电压反序"),
    C37040116(37040116, "AC模块组: 锁相失败"),
    C37040117(37040117, "AC模块组: 环境温度过温"),
    C37040118(37040118, "AC模块组: 环温探头故障"),
    C37040119(37040119, "AC模块组: 柜温探头故障"),
    C37040120(37040120, "AC模块组: 机柜温度过温"),
    C37040121(37040121, "AC模块组: 离网电压缺相"),


    // DCAC (tab40-2)
    C37040200(37040200, "AC模块组: 24V辅助电源故障"),
    C37040201(37040201, "AC模块组: 紧急停机"),
    C37040202(37040202, "AC模块组: 接地故障"),
    C37040203(37040203, "AC模块组: 直流母线过压"),
    C37040204(37040204, "AC模块组: 模块温度过温"),
    C37040205(37040205, "AC模块组: 模块电流不平衡"),
    C37040206(37040206, "AC模块组: 风扇故障"),
    C37040207(37040207, "AC模块组: 直流继电器开路"),
    C37040208(37040208, "AC模块组: 校准参数异常"),
    C37040209(37040209, "AC模块组: 母线电压不平衡"),
    C37040210(37040210, "AC模块组: 保险故障"),
    C37040211(37040211, "AC模块组: DSP初始化故障"),
    C37040212(37040212, "AC模块组: 直流软启动失败"),
    C37040213(37040213, "AC模块组: CANA通讯故障"),
    C37040214(37040214, "AC模块组: 直流输入反接"),
    C37040215(37040215, "AC模块组: 交流电流直流分量异常"),
    C37040216(37040216, "AC模块组: 变压器过温"),
    C37040217(37040217, "AC模块组: U2通信异常2"),
    C37040218(37040218, "AC模块组: BMS故障或直流开关断开"),
    C37040219(37040219, "AC模块组: 防雷器故障"),
    C37040220(37040220, "AC模块组: 过载超时故障"),
    C37040221(37040221, "AC模块组: 交流软启动失败"),
    C37040222(37040222, "AC模块组: 同步信号故障1"),
    C37040223(37040223, "AC模块组: DSP版本故障"),
    C37040224(37040224, "AC模块组: 交流继电器开路"),
    C37040225(37040225, "AC模块组: 采样零点异常"),
    C37040226(37040226, "AC模块组: U2通信异常1"),
    C37040227(37040227, "AC模块组: 15V辅助电源故障"),
    C37040228(37040228, "AC模块组: 模块重号故障"),
    C37040229(37040229, "AC模块组: RS485通讯故障"),
    C37040230(37040230, "AC模块组: CANB通讯故障"),
    C37040231(37040231, "AC模块组: 重启过多"),


    // DCAC (tab40-3)
    C37040300(37040300, "AC模块组: CPLD版本故障"),
    C37040301(37040301, "AC模块组: 硬件版本故障"),
    C37040302(37040302, "AC模块组: 直流继电器短路"),
    C37040303(37040303, "AC模块组: 直流母线欠压"),
    C37040304(37040304, "AC模块组: 交流继电器短路"),
    C37040305(37040305, "AC模块组: 同步信号故障2"),
    C37040306(37040306, "AC模块组: 参数不匹配"),
    C37040307(37040307, "AC模块组: CANC通讯故障"),
    C37040308(37040308, "AC模块组: 环境湿度过高"),
    C37040309(37040309, "AC模块组: BMS电压异常"),
    C37040310(37040310, "AC模块组: BMS电流异常"),
    C37040311(37040311, "AC模块组: BMS温度异常"),
    C37040312(37040312, "AC模块组: BMS跳机"),
    C37040313(37040313, "AC模块组: 绝缘检测异常"),
    C37040314(37040314, "AC模块组: 硬件采样异常"),
    C37040315(37040315, "AC模块组: 远程通信丢失"),


    // DCAC (tab41-6)
    C37041600(37041600, "DCAC模块1故障"),
    C37041601(37041601, "DCAC模块2故障"),
    C37041602(37041602, "DCAC模块3故障"),
    C37041603(37041603, "DCAC模块4故障"),
    C37041604(37041604, "DCAC模块5故障"),
    C37041605(37041605, "DCAC模块6故障"),
    C37041606(37041606, "DCAC模块7故障"),
    C37041607(37041607, "DCAC模块8故障"),
    C37041608(37041608, "DCAC模块9故障"),
    C37041609(37041609, "DCAC模块10故障"),
    C37041610(37041610, "机架功能板故障"),


    // DCAC (tab41-7)
    C37041700(37041700, "PCS: 光伏欠压"),
    C37041701(37041701, "PCS: 光伏过压"),
    C37041702(37041702, "PCS: 光伏接触器短路"),
    C37041703(37041703, "PCS: 光伏接触器开路"),
    C37041704(37041704, "PCS: PE-N接触器短路"),
    C37041705(37041705, "PCS: PE-N接触器开路"),
    C37041706(37041706, "PCS: N接触器短路"),
    C37041707(37041707, "PCS: N接触器开路"),


    // DCDC (tab50-1)
    C37150100(37150100, "DC模块组: 直流母线过压"),
    C37150101(37150101, "DC模块组: 直流母线欠压"),
    C37150102(37150102, "DC模块组: 直流输入过压"),
    C37150103(37150103, "DC模块组: 直流输入欠压"),
    C37150104(37150104, "DC模块组: 直流输入过流"),
    C37150105(37150105, "DC模块组: 直流输入掉电"),
    C37150106(37150106, "DC模块组: 重启过多"),
    C37150107(37150107, "DC模块组: 电池继电器短路"),
    C37150108(37150108, "DC模块组: 光伏能量不足"),
    C37150109(37150109, "DC模块组: 电池电量不足"),
    C37150110(37150110, "DC模块组: 环境温度过高"),
    C37150111(37150111, "DC模块组: BMS故障或直流开关断开"),
    C37150112(37150112, "DC模块组: U2通信异常1"),
    C37150113(37150113, "DC模块组: 机柜温度过高"),
    C37150114(37150114, "DC模块组: 柜温探头故障"),
    C37150115(37150115, "DC模块组: 环温探头故障"),
    C37150116(37150116, "DC模块组: 模块电流不平衡"),
    //    C37150117(37150117, ""),
    C37150118(37150118, "DC模块组: 24V辅助电源故障"),
    C37150119(37150119, "DC模块组: 紧急停机"),
    C37150120(37150120, "DC模块组: 接地故障"),
    C37150121(37150121, "DC模块组: 母线电压不平衡"),
    C37150122(37150122, "DC模块组: 模块温度过温"),
    C37150123(37150123, "DC模块组: 风扇故障"),
    C37150124(37150124, "DC模块组: 电池继电器开路"),
    C37150125(37150125, "DC模块组: 校准参数异常"),
    C37150126(37150126, "DC模块组: 保险故障"),
    C37150127(37150127, "DC模块组: DSP初始化故障"),
    C37150128(37150128, "DC模块组: 电池软启动失败"),
    C37150129(37150129, "DC模块组: CANA通讯故障"),
    C37150130(37150130, "DC模块组: 母线继电器开路"),
    C37150131(37150131, "DC模块组: 母线软启动失败"),


    // DCDC (tab50-2)
    C37150200(37150200, "DC模块组: DSP版本故障"),
    C37150201(37150201, "DC模块组: CPLD版本故障"),
    C37150202(37150202, "DC模块组: 参数不匹配"),
    C37150203(37150203, "DC模块组: 硬件版本故障"),
    C37150204(37150204, "DC模块组: 485通讯故障"),
    C37150205(37150205, "DC模块组: CANB通讯故障"),
    C37150206(37150206, "DC模块组: 模块重号故障"),
    C37150207(37150207, "DC模块组: 15V辅助电源故障"),
    C37150208(37150208, "DC模块组: 母线继电器短路"),
    C37150209(37150209, "DC模块组: BMS电压异常"),
    C37150210(37150210, "DC模块组: BMS电流异常"),
    C37150211(37150211, "DC模块组: BMS温度异常"),
    C37150212(37150212, "DC模块组: BMS跳机"),
    C37150213(37150213, "DC模块组: 绝缘检测异常"),
    //    C37150214(37150214, ""),
    C37150215(37150215, "PCS: 光伏欠压"),
    C37150216(37150216, "PCS: 光伏过压"),
    C37150217(37150217, "PCS: 光伏接触器短路"),
    C37150218(37150218, "PCS: 光伏接触器开路"),
    C37150219(37150219, "PCS: PE-N接触器短路"),
    C37150220(37150220, "PCS: PE-N接触器开路"),
    C37150221(37150221, "PCS: N接触器短路"),
    C37150222(37150222, "PCS: N接触器开路"),


    // DCDC (tab51-4)
    C37151400(37151400, "DCDC模块1故障"),
    C37151401(37151401, "DCDC模块2故障"),
    C37151402(37151402, "DCDC模块3故障"),
    C37151403(37151403, "DCDC模块4故障"),
    C37151404(37151404, "DCDC模块5故障"),
    C37151405(37151405, "DCDC模块6故障"),
    C37151406(37151406, "DCDC模块7故障"),
    C37151407(37151407, "DCDC模块8故障"),
    C37151408(37151408, "DCDC模块9故障"),
    C37151409(37151409, "DCDC模块10故障"),
    C37151410(37151410, "机架功能板故障"),


    // AIR_CONDITION (tab60-1)
    C39060100(39060100, "高温告警"),
    C39060101(39060101, "内风机故障告警"),
    C39060102(39060102, "外风机故障告警"),
    C39060103(39060103, "压缩机故障告警"),
    C39060104(39060104, "柜内回风温度温度探头故障"),
    C39060105(39060105, "系统高压力告警"),
    C39060106(39060106, "低温告警"),
    C39060107(39060107, "直流过压告警"),
    C39060108(39060108, "直流欠压告警"),
    C39060109(39060109, "交流过压告警"),
    C39060110(39060110, "交流欠压告警"),
    C39060111(39060111, "交流掉电告警"),
    C39060112(39060112, "蒸发器温度传感器故障"),
    C39060113(39060113, "冷凝器温度传感器故障"),
    C39060114(39060114, "环境温度传感器故障"),
    C39060115(39060115, "蒸发器冻结报警"),
    C39060116(39060116, "频繁高压力告警"),
    C39060117(39060117, "严重告警总状态"),

    /**
     * 储能设备故障码 END
     * 规则：(300 + 设备类型ID前两位) * 100000 + 设备对应告警表序号 * 100 + bit位
     */

    ;

    private final int code;//桩端故障码
    private final String desc;

    DeviceStatusCodeType(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public static DeviceStatusCodeType getByCode(int code) {
        for (DeviceStatusCodeType type : DeviceStatusCodeType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return DeviceStatusCodeType.C00;
    }

    public static String getDescByCode(int code) {
        for (DeviceStatusCodeType type : DeviceStatusCodeType.values()) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return DeviceStatusCodeType.C00.getDesc();
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }


}