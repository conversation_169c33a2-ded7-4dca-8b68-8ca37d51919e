package eu.chargetime.ocpp.model.dc.type;

/**
 * : v3.7版本报文
 */
public enum EvseMessageType {
    EVSE_REGISTER((byte) 0x01, StreamDirection.Upstream),              // [上行]注册
    // EVSE_REGISTER_361((byte) 0x55, StreamDirection.Upstream),              // [上行]注册
    EVSE_HB((byte) 0x02, StreamDirection.Upstream),             // [上行]心跳

    AUTH_REQUEST((byte) 0x03, StreamDirection.Upstream),               // [上行]请求鉴权[在线卡或VIN码充电请求鉴权]
    AUTH_RESULT((byte) 0x50, StreamDirection.Downstream),              // [下行]鉴权结果下发

    CHARGE_FROM_EVSE((byte) 0x04, StreamDirection.Upstream),           // [上行]桩端发起充电请求[在线卡或VIN码充电请求]
    CHARGE_FROM_CLOUD((byte) 0x51, StreamDirection.Downstream),       // [下行]云端发起充电请求
    CHARGE_START((byte) 0x05, StreamDirection.Upstream),               // [上行]桩上报充电开始
    //    CHARGE_START2((byte) 0x53, StreamDirection.Upstream),               // [上行]桩上报充电开始. 3.6.0
    CHARGE_PROLONG((byte) 0x06, StreamDirection.Upstream),          // [上行]在线订单续费
    CHARGE_POWER_CTRL((byte) 0x52, StreamDirection.Downstream),          // [下行]功率分配
    CHARGE_SOC_CTRL((byte) 0x53, StreamDirection.Downstream),          // [下行]SOC控制
    CHARGE_HB((byte) 0x07, StreamDirection.Upstream),             // [上行]枪头数据上报
    CHARGE_UPDATE((byte) 0x08, StreamDirection.Upstream),               // [上行]桩上报充电中分时数据
    CHARGE_STOP_FROM_CLOUD((byte) 0x54, StreamDirection.Downstream),  // [下行]云端停止充电
    CHARGE_FINISHED((byte) 0x09, StreamDirection.Upstream),            // [上行]充电结束后充电记录上报

    EVSE_LOCK((byte) 0x55, StreamDirection.Downstream),            // [下行]远程锁定充电桩

    EVSE_REPORT_DEVICE_DETAIL((byte) 0x10, StreamDirection.Upstream),            // [上行]桩上报器件信息
    CLOUD_READ_DEVICE_DETAIL((byte) 0x56, StreamDirection.Downstream),            // [下行]云端查询器件信息

    CONFIG_MODIFY((byte) 0x57, StreamDirection.Downstream),            // [下行]修改桩配置
    CONFIG_QUERY((byte) 0x58, StreamDirection.Downstream),             // [下行]查询桩配置
    CARD_MODIFY((byte) 0x59, StreamDirection.Downstream),              //[下行] 下发紧急充电卡
    CARD_QUERY((byte) 0x60, StreamDirection.Downstream),               //[下行] 查询紧急充电卡
    VIN_MODIFY((byte) 0x61, StreamDirection.Downstream),              //[下行] 下发VIN码白名单
    VIN_QUERY((byte) 0x62, StreamDirection.Downstream),               //[下行] 查询VIN码白名单

    CONFIG_REPORT((byte) 0x11, StreamDirection.Downstream),            // [上行]桩上报配置变更

    REMOTE_UPGRADE((byte) 0x63, StreamDirection.Downstream),           // [下行]远程升级固件
    REMOTE_RESTART((byte) 0x64, StreamDirection.Downstream),           // [下行]远程重启充电桩
    REMOTE_DEBUG((byte) 0x65, StreamDirection.Downstream),             // [下行]远程DEBUG
    REMOTE_DEBUG_DATA_UPLOAD((byte) 0x12, StreamDirection.Downstream), // [下行]远程DEBUG数据上传

    ;

    private final byte code;
    private StreamDirection streamDirection;

    EvseMessageType(byte code, StreamDirection streamDirection) {
        this.code = code;
        this.streamDirection = streamDirection;
    }

    public static EvseMessageType codeOf(byte code) {
        for (EvseMessageType type : EvseMessageType.values()) {
            if (code == type.getCode()) {
                return type;
            }
        }
        return null;
    }

    public byte getCode() {
        return code;
    }

    public StreamDirection getStreamDirection() {
        return streamDirection;
    }
}
