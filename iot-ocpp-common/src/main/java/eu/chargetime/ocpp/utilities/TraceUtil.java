package eu.chargetime.ocpp.utilities;

import eu.chargetime.ocpp.model.Request;
import java.util.Optional;
import java.util.UUID;

public class TraceUtil {

    public static String traceId(Request request) {
        return Optional.ofNullable(request)
            .map(Request::getOcppMessageId)
            .map(e -> e.substring(e.length() - 12))
            .orElse("");
    }

    public static String shortSession(UUID sessionIndex) {
        return Optional.ofNullable(sessionIndex)
            .map(e -> e.toString().split("-"))
            .map(e -> e[e.length - 1])
            .orElse("");
    }

}
