package eu.chargetime.ocpp;

import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;
import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 客户端IP获取工具类
 * 支持多种代理头获取真实客户端IP
 */
public class ClientIpUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ClientIpUtil.class);
    
    // 常见的代理头，按优先级排序
    private static final List<String> PROXY_HEADERS = Arrays.asList(
        "X-Real-IP",           // Nginx代理
        "X-Forwarded-For",     // 标准代理头
        "X-Client-IP",         // Apache代理
        "CF-Connecting-IP",    // Cloudflare
        "True-Client-IP"       // Akamai
    );
    
    /**
     * 从ClientHandshake中获取客户端真实IP
     * 
     * @param clientHandshake WebSocket握手信息
     * @param webSocket WebSocket连接
     * @return 客户端真实IP地址，如果无法获取则返回直接连接的IP
     */
    public static String getClientRealIp(ClientHandshake clientHandshake, WebSocket webSocket) {
        // 首先尝试从代理头获取真实IP
        String realIp = getRealIpFromHeaders(clientHandshake);
        if (realIp != null && !realIp.isEmpty()) {
            logger.debug("从代理头获取到真实IP: {}", realIp);
            return realIp;
        }
        
        // 如果代理头中没有，则使用直接连接的IP
        if (webSocket != null && webSocket.getRemoteSocketAddress() != null) {
            String directIp = webSocket.getRemoteSocketAddress().getAddress().getHostAddress();
            // 如果是IPv6地址，转换为更友好的格式
            if (directIp.equals("0:0:0:0:0:0:0:1")) {
                directIp = "127.0.0.1:";
            }
            logger.debug("使用直接连接IP: {}", directIp);
            return directIp;
        }
        
        logger.warn("无法获取客户端IP");
        return "unknown";
    }
    
    /**
     * 从代理头中获取真实IP
     * 
     * @param clientHandshake WebSocket握手信息
     * @return 真实IP地址，如果未找到则返回null
     */
    private static String getRealIpFromHeaders(ClientHandshake clientHandshake) {
        for (String header : PROXY_HEADERS) {
            String value = clientHandshake.getFieldValue(header);
            if (value != null && !value.trim().isEmpty()) {
                // X-Forwarded-For可能包含多个IP，取第一个
                String[] ips = value.split(",");
                String firstIp = ips[0].trim();
                
                // 验证IP格式
                if (isValidIp(firstIp)) {
                    logger.debug("从代理头 {} 获取到IP: {}", header, firstIp);
                    return firstIp;
                }
            }
        }
        return null;
    }
    
    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址字符串
     * @return 是否为有效IP
     */
    private static boolean isValidIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        // 简单的IP格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 获取格式化的客户端地址（IP:端口）
     * 
     * @param clientHandshake WebSocket握手信息
     * @param webSocket WebSocket连接
     * @return 真实IP地址和格式化的地址字符串
     */
    public static Pair<String, String> getFormattedClientAddress(ClientHandshake clientHandshake, WebSocket webSocket) {
        String realIp = getClientRealIp(clientHandshake, webSocket);
        
        if (webSocket != null && webSocket.getRemoteSocketAddress() != null) {
            int port = webSocket.getRemoteSocketAddress().getPort();
            return Pair.of(realIp,  realIp + ":" + port);
        }
        
        return Pair.of(realIp, null);
    }
    
    /**
     * 获取所有代理头信息（用于调试）
     * 
     * @param clientHandshake WebSocket握手信息
     * @return 代理头信息字符串
     */
    public static String getProxyHeadersInfo(ClientHandshake clientHandshake) {
        StringBuilder sb = new StringBuilder();
        for (String header : PROXY_HEADERS) {
            String value = clientHandshake.getFieldValue(header);
            if (value != null && !value.trim().isEmpty()) {
                sb.append(header).append(": ").append(value).append("; ");
            }
        }
        return sb.toString();
    }
} 