package eu.chargetime.ocpp;
/*
   ChargeTime.eu - Java-OCA-OCPP

   MIT License

   Copyright (C) 2016-2018 <PERSON> <<EMAIL>>

   Permission is hereby granted, free of charge, to any person obtaining a copy
   of this software and associated documentation files (the "Software"), to deal
   in the Software without restriction, including without limitation the rights
   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   copies of the Software, and to permit persons to whom the Software is
   furnished to do so, subject to the following conditions:

   The above copyright notice and this permission notice shall be included in all
   copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
   SOFTWARE.
*/

import eu.chargetime.ocpp.biz.SaveTransDataService;
import eu.chargetime.ocpp.model.RadioMsgVo;
import eu.chargetime.ocpp.model.dc.evse.protocol.EvseMsgBase;
import java.time.Instant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebSocketReceiver implements Receiver {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketReceiver.class);

    private RadioEvents handler;
    private WebSocketReceiverEvents receiverEvents;
    private String channelKey;

    public WebSocketReceiver(WebSocketReceiverEvents handler, String chKey) {
        receiverEvents = handler;
        channelKey = chKey;
    }

    @Override
    public void disconnect() {
        receiverEvents.close();
        handler.disconnected();
    }

    void relay(String message, EvseMsgBase base) {
        if (base != null) {
            SaveTransDataService.evseExport(base.getTid(), base.getChKey(), base.getEvseNo(),
                message);
        } else {
            SaveTransDataService.evseExport(null, null, null, message);
        }
        handler.receivedMessage(message, base);
    }

    @Override
    public void send(RadioMsgVo msgVo) {
        EvseMsgBase base = msgVo.getBase();
        String message = msgVo.getMsg().toString();
        // TODO: 2025/4/21 WZFIX 待删除日志
        logger.info("WebSocketReceiver send. now: {}, message: {}", Instant.now(), msgVo);
        if (base != null) {
            SaveTransDataService.evseImport(base.getTid(), base.getChKey(), base.getEvseNo(),
                message);
        } else {
            SaveTransDataService.evseImport(null, null, null, message);
        }
        receiverEvents.relay(message);
    }

    @Override
    public boolean isClosed() {
        return receiverEvents.isClosed();
    }

    @Override
    public void accept(RadioEvents events) {
        handler = events;
    }

    public String getChannelKey() {
        return channelKey;
    }

}
