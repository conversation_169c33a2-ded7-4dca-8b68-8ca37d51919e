package eu.chargetime.ocpp.model.core;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016-2018 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/** Accepted values used with {@link StatusNotificationRequest}. */
public enum ChargePointErrorCode {
  ConnectorLockFailure, // 无法锁定或解锁枪
  EVCommunicationError, // 与车通信失败
  GroundFailure, // 接地故障
  HighTemperature, // 枪高温
  InternalError, // 内部硬件或软件组件出错
  LocalListConflict, // 本地列表冲突
  NoError, // 无错误
  OtherError, // 其他错误
  OverCurrentFailure, // 过流故障
  OverVoltage, // 过压
  PowerMeterFailure, // 无法读取电表/能量表/电能表
  PowerSwitchFailure, // 电源开关故障
  ReaderFailure, // idTag读取器失败
  ResetFailure, // 重置故障
  UnderVoltage, // 欠压
  WeakSignal, // 无线信号弱
  ;
}
