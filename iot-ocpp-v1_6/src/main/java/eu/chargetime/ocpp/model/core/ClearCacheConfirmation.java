package eu.chargetime.ocpp.model.core;

/*
ChargeTime.eu - Java-OCA-OCPP
Copyright (C) 2015-2016 <PERSON> <<EMAIL>>
Copyright (C) 2019 <PERSON> <<EMAIL>>

MIT License

Copyright (C) 2016-2018 <PERSON>
Copyright (C) 2019 <PERSON> <<EMAIL>>
Copyright (C) 2022 <PERSON> <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/

import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

/** Sent by the Charge Point to the Central System in response to a {@link ClearCacheRequest}. */
@XmlRootElement(name = "clearCacheResponse")
public class ClearCacheConfirmation extends Confirmation {

  private ClearCacheStatus status;

  /**
   * @deprecated use {@link #ClearCacheConfirmation(ClearCacheStatus)} to be sure to set required
   *     fields
   */
  @Deprecated
  public ClearCacheConfirmation() {}

  /**
   * Handle required fields.
   *
   * @param status the {@link ClearCacheStatus}, see {@link #setStatus(ClearCacheStatus)}
   */
  public ClearCacheConfirmation(ClearCacheStatus status) {
    setStatus(status);
  }

  /**
   * Accepted if the Charge Point has executed the request, otherwise rejected.
   *
   * @return the {@link ClearCacheStatus}.
   */
  public ClearCacheStatus getStatus() {
    return status;
  }

  /**
   * Required. Accepted if the Charge Point has executed the request, otherwise rejected.
   *
   * @param status the {@link ClearCacheStatus}.
   */
  @XmlElement
  public void setStatus(ClearCacheStatus status) {
    this.status = status;
  }

  /**
   * Accepted if the Charge Point has executed the request, otherwise rejected.
   *
   * @return the {@link ClearCacheStatus}.
   */
  @Deprecated
  public ClearCacheStatus objStatus() {
    return status;
  }

  @Override
  public boolean validate() {
    return this.status != null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ClearCacheConfirmation that = (ClearCacheConfirmation) o;
    return status == that.status;
  }

  @Override
  public int hashCode() {
    return Objects.hash(status);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("status", status)
        .add("isValid", validate())
        .toString();
  }
}
