package eu.chargetime.ocpp.model.core;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016-2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 * Copyright (C) 2022 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Sent by the Charge Point to the Central System or vice versa in response to a {@link
 * DataTransferRequest}.
 */
@XmlRootElement(name = "dataTransferResponse")
@XmlType(propOrder = {"status", "data"})
public class DataTransferConfirmation extends Confirmation {

  private DataTransferStatus status;
  private String data;

  /**
   * @deprecated use {@link #DataTransferConfirmation(DataTransferStatus)} to be sure to set
   *     required fields
   */
  @Deprecated
  public DataTransferConfirmation() {}

  /**
   * Handle required fields.
   *
   * @param status the {@link DataTransferStatus}, see {@link #setStatus(DataTransferStatus)}
   */
  public DataTransferConfirmation(DataTransferStatus status) {
    setStatus(status);
  }

  /**
   * This indicates the success or failure of the data transfer.
   *
   * @return the {@link DataTransferStatus}.
   */
  public DataTransferStatus getStatus() {
    return status;
  }

  /**
   * Required. This indicates the success or failure of the data transfer.
   *
   * @param status the {@link DataTransferStatus}.
   */
  @XmlElement
  public void setStatus(DataTransferStatus status) {
    this.status = status;
  }

  /**
   * This indicates the success or failure of the data transfer.
   *
   * @return the {@link DataTransferStatus}.
   */
  @Deprecated
  public DataTransferStatus objStatus() {
    return status;
  }

  /**
   * Optional. Data in response to request.
   *
   * @return data.
   */
  public String getData() {
    return data;
  }

  /**
   * Optional. Data in response to request.
   *
   * @param data String, data
   */
  @XmlElement
  public void setData(String data) {
    this.data = data;
  }

  @Override
  public boolean validate() {
    return this.status != null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    DataTransferConfirmation that = (DataTransferConfirmation) o;
    return status == that.status && Objects.equals(data, that.data);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, data);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("status", status)
        .add("data", data)
        .add("isValid", validate())
        .toString();
  }
}
