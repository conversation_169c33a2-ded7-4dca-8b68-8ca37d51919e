package eu.chargetime.ocpp.model.core;

import eu.chargetime.ocpp.model.Validatable;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016-2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/**
 * Collection of one or more sampled values in {@link MeterValuesRequest}. All {@link SampledValue}
 * in a {@link MeterValue} are sampled at the same point in time.
 */
@XmlRootElement
@XmlType(propOrder = {"timestamp", "sampledValue"})
public class MeterValue implements Validatable {

  private ZonedDateTime timestamp;
  private SampledValue[] sampledValue;

  /**
   * @deprecated use {@link #MeterValue(ZonedDateTime, SampledValue[])} to be sure to set required
   *     fields
   */
  @Deprecated
  public MeterValue() {}

  /**
   * Handle required fields.
   *
   * @param timestamp {@link ZonedDateTime} timestamp, see {@link #setTimestamp(ZonedDateTime)}
   * @param sampledValue Array of {@link SampledValue}, see {@link #setSampledValue(SampledValue[])}
   */
  public MeterValue(ZonedDateTime timestamp, SampledValue[] sampledValue) {
    setTimestamp(timestamp);
    setSampledValue(sampledValue);
  }

  @Override
  public boolean validate() {
    boolean valid = timestamp != null && sampledValue != null && sampledValue.length > 0;

    if (valid) {
      for (SampledValue value : sampledValue) {
        valid &= value.validate();
      }
    }
    return valid;
  }

  /**
   * Timestamp for measured value(s).
   *
   * @return original timestamp.
   */
  public ZonedDateTime getTimestamp() {
    return timestamp;
  }

  /**
   * Required. Timestamp for measured value(s).
   *
   * @param timestamp {@link ZonedDateTime} timestamp
   */
  @XmlElement
  public void setTimestamp(ZonedDateTime timestamp) {
    this.timestamp = timestamp;
  }

  /**
   * Timestamp for measured value(s).
   *
   * @return original timestamp.
   */
  @Deprecated
  public ZonedDateTime objTimestamp() {
    return timestamp;
  }

  /**
   * One or more measured values.
   *
   * @return Array of {@link SampledValue}.
   */
  public SampledValue[] getSampledValue() {
    return sampledValue;
  }

  /**
   * Required. One or more measured values.
   *
   * @param sampledValue Array of {@link SampledValue}.
   */
  @XmlElement
  public void setSampledValue(SampledValue[] sampledValue) {
    this.sampledValue = sampledValue;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    MeterValue that = (MeterValue) o;
    return Objects.equals(timestamp, that.timestamp)
        && Arrays.equals(sampledValue, that.sampledValue);
  }

  @Override
  public int hashCode() {
    return Objects.hash(timestamp, sampledValue);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("timestamp", timestamp)
        .add("sampledValue", sampledValue)
        .add("isValid", validate())
        .toString();
  }
}
