package eu.chargetime.ocpp.model.core;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016-2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

import eu.chargetime.ocpp.model.RequestWithId;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

/** Sent by the Central System to the Charge Point. */
@XmlRootElement
public class ResetRequest extends RequestWithId {

  private ResetType type;

  /** @deprecated use {@link #ResetRequest(ResetType)} to be sure to set required fields */
  @Deprecated
  public ResetRequest() {}

  /**
   * Handle required fields.
   *
   * @param type the {@link ResetType}, see {@link #setType(ResetType)}
   */
  public ResetRequest(ResetType type) {
    setType(type);
  }

  /**
   * This contains the type of reset that the Charge Point should perform.
   *
   * @return the {@link ResetType}.
   */
  public ResetType getType() {
    return type;
  }

  /**
   * Required. This contains the type of reset that the Charge Point should perform.
   *
   * @param type the {@link ResetType}.
   */
  @XmlElement
  public void setType(ResetType type) {
    this.type = type;
  }

  /**
   * This contains the type of reset that the Charge Point should perform.
   *
   * @return the {@link ResetType}.
   */
  @Deprecated
  public ResetType objType() {
    return type;
  }

  @Override
  public boolean validate() {
    return type != null;
  }

  @Override
  public boolean transactionRelated() {
    return false;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ResetRequest that = (ResetRequest) o;
    return type == that.type;
  }

  @Override
  public int hashCode() {
    return Objects.hash(type);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this).add("type", type).add("isValid", validate()).toString();
  }
}
