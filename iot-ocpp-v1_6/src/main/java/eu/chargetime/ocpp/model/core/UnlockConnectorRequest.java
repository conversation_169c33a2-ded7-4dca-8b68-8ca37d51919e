package eu.chargetime.ocpp.model.core;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016-2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

import eu.chargetime.ocpp.PropertyConstraintException;
import eu.chargetime.ocpp.model.RequestWithId;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

/** Sent by the Central System to the Charge Point. */
@XmlRootElement
public class UnlockConnectorRequest extends RequestWithId {

  private Integer connectorId;

  /** @deprecated use {@link #UnlockConnectorRequest(Integer)} to be sure to set required fields */
  @Deprecated
  public UnlockConnectorRequest() {}

  /**
   * Handle required fields.
   *
   * @param connectorId integer, value &gt; 0, see {@link #setConnectorId(Integer)}
   */
  public UnlockConnectorRequest(Integer connectorId) {
    setConnectorId(connectorId);
  }

  @Override
  public boolean validate() {
    return connectorId != null && connectorId > 0;
  }

  /**
   * This contains the identifier of the connector to be unlocked.
   *
   * @return connector.
   */
  public Integer getConnectorId() {
    return connectorId;
  }

  /**
   * Required. This contains the identifier of the connector to be unlocked.
   *
   * @param connectorId integer, value &gt; 0.
   */
  @XmlElement
  public void setConnectorId(Integer connectorId) {
    if (connectorId == null || connectorId <= 0) {
      throw new PropertyConstraintException(connectorId, "connectorId must be > 0");
    }

    this.connectorId = connectorId;
  }

  @Override
  public boolean transactionRelated() {
    return false;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    UnlockConnectorRequest that = (UnlockConnectorRequest) o;
    return Objects.equals(connectorId, that.connectorId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(connectorId);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("connectorId", connectorId)
        .add("isValid", validate())
        .toString();
  }
}
