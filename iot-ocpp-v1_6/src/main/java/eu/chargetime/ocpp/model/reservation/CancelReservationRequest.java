package eu.chargetime.ocpp.model.reservation;

import eu.chargetime.ocpp.model.RequestWithId;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2016 <PERSON> <<EMAIL>>
 * Copyright (C) 2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/** Sent by the Central System to the Charge Point. */
@XmlRootElement
public class CancelReservationRequest extends RequestWithId {

  private Integer reservationId;

  /**
   * @deprecated use {@link #CancelReservationRequest(Integer)} to be sure to set required fields
   */
  @Deprecated
  public CancelReservationRequest() {}

  /**
   * Handle required fields.
   *
   * @param reservationId Integer, id of the reservation, see {@link #setReservationId(Integer)}
   */
  public CancelReservationRequest(Integer reservationId) {
    setReservationId(reservationId);
  }

  @Override
  public boolean validate() {
    return reservationId != null;
  }

  /**
   * Id of the reservation to cancel.
   *
   * @return Integer, id of the reservation.
   */
  public Integer getReservationId() {
    return reservationId;
  }

  /**
   * Required. Id of the reservation to cancel.
   *
   * @param reservationId Integer, id of the reservation.
   */
  @XmlElement
  public void setReservationId(Integer reservationId) {
    this.reservationId = reservationId;
  }

  @Override
  public boolean transactionRelated() {
    return false;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    CancelReservationRequest that = (CancelReservationRequest) o;
    return Objects.equals(reservationId, that.reservationId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reservationId);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("reservationId", reservationId)
        .add("isValid", validate())
        .toString();
  }
}
