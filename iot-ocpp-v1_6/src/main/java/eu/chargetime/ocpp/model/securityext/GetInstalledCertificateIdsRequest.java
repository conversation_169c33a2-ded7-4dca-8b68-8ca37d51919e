package eu.chargetime.ocpp.model.securityext;

/*
   ChargeTime.eu - Java-OCA-OCPP

   MIT License

   Copyright (C) 2022 Mathias <PERSON> <<EMAIL>>

   Permission is hereby granted, free of charge, to any person obtaining a copy
   of this software and associated documentation files (the "Software"), to deal
   in the Software without restriction, including without limitation the rights
   to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   copies of the Software, and to permit persons to whom the Software is
   furnished to do so, subject to the following conditions:

   The above copyright notice and this permission notice shall be included in all
   copies or substantial portions of the Software.

   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
   SOFTWARE.
*/

import eu.chargetime.ocpp.model.RequestWithId;
import eu.chargetime.ocpp.model.securityext.types.CertificateUseEnumType;
import eu.chargetime.ocpp.utilities.MoreObjects;

import java.util.Objects;

public class GetInstalledCertificateIdsRequest extends RequestWithId {

  private CertificateUseEnumType certificateType;

  /**
   * Private default constructor for serialization purposes.
   */
  private GetInstalledCertificateIdsRequest() {
  }

  /**
   * Handle required fields.
   *
   * @param certificateType CertificateUseEnumType. See {@link #setCertificateType(CertificateUseEnumType)}
   */
  public GetInstalledCertificateIdsRequest(CertificateUseEnumType certificateType) {
    setCertificateType(certificateType);
  }

  /**
   * Indicates the type of certificates requested.
   *
   * @return {@link CertificateUseEnumType}
   */
  public CertificateUseEnumType getCertificateType() {
    return certificateType;
  }

  /**
   * Required. Indicates the type of certificates requested.
   *
   * @param certificateType {@link CertificateUseEnumType}
   */
  public void setCertificateType(CertificateUseEnumType certificateType) {
    this.certificateType = certificateType;
  }

  @Override
  public boolean transactionRelated() {
    return false;
  }

  @Override
  public boolean validate() {
    return certificateType != null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    GetInstalledCertificateIdsRequest that = (GetInstalledCertificateIdsRequest) o;
    return Objects.equals(certificateType, that.certificateType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(certificateType);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
      .add("certificateType", certificateType)
      .add("isValid", validate()).toString();
  }
}
