package eu.chargetime.ocpp.model.smartcharging;

/*
 * ChargeTime.eu - Java-OCA-OCPP
 *
 * MIT License
 *
 * Copyright (C) 2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2018 <PERSON> <<EMAIL>>
 * Copyright (C) 2019 <PERSON> <<EMAIL>>
 * Copyright (C) 2022 Emil <PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

import eu.chargetime.ocpp.model.Confirmation;
import eu.chargetime.ocpp.utilities.MoreObjects;
import java.util.Objects;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "clearChargingProfileResponse")
public class ClearChargingProfileConfirmation extends Confirmation {

  private ClearChargingProfileStatus status;

  /**
   * @deprecated use {@link #ClearChargingProfileConfirmation(ClearChargingProfileStatus)} to be
   *     sure to set required fields
   */
  @Deprecated
  public ClearChargingProfileConfirmation() {}

  /**
   * Handle required fields.
   *
   * @param status the {@link ChargingProfileStatus}, see {@link
   *     #setStatus(ClearChargingProfileStatus)}.
   */
  public ClearChargingProfileConfirmation(ClearChargingProfileStatus status) {
    setStatus(status);
  }

  /**
   * This indicates the success or failure of the change of the charging profile.
   *
   * @return the {@link ClearChargingProfileStatus}.
   */
  public ClearChargingProfileStatus getStatus() {
    return status;
  }

  /**
   * Required. This indicates the success or failure of the change of the charging profile.
   *
   * @param status the {@link ClearChargingProfileStatus}.
   */
  @XmlElement
  public void setStatus(ClearChargingProfileStatus status) {
    this.status = status;
  }

  @Override
  public boolean validate() {
    return this.status != null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ClearChargingProfileConfirmation that = (ClearChargingProfileConfirmation) o;
    return status == that.status;
  }

  @Override
  public int hashCode() {
    return Objects.hash(status);
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("status", status)
        .add("isValid", validate())
        .toString();
  }
}
